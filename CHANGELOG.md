## [52.0.2](https://github.com/unlockre/components-library/compare/v52.0.1...v52.0.2) (2025-06-12)


### Bug Fixes

* Fix PlacesAutocompleteField address ([#631](https://github.com/unlockre/components-library/issues/631)) ([61f4e8f](https://github.com/unlockre/components-library/commit/61f4e8f11b114060af7f71c25ec1dd03dbf40848))

## [52.0.1](https://github.com/unlockre/components-library/compare/v52.0.0...v52.0.1) (2025-06-11)


### Bug Fixes

* Ignore null event in onInputChange handler ([#629](https://github.com/unlockre/components-library/issues/629)) ([f03ad52](https://github.com/unlockre/components-library/commit/f03ad520e26afe19530330460e0a23c4551b71ba))

# [52.0.0](https://github.com/unlockre/components-library/compare/v51.0.1...v52.0.0) (2025-06-11)


### Bug Fixes

* Limit place suggestions to specific places on the map ([#627](https://github.com/unlockre/components-library/issues/627)) ([2d6c6cb](https://github.com/unlockre/components-library/commit/2d6c6cb476dc98a61472c4a20d8fb2fbea78d40d))


### BREAKING CHANGES

* Change AutocompleteField onInputChange prop signature

## [51.0.1](https://github.com/unlockre/components-library/compare/v51.0.0...v51.0.1) (2025-06-10)


### Bug Fixes

* Properly pass disable portal to autocomplete field ([#625](https://github.com/unlockre/components-library/issues/625)) ([dcb4199](https://github.com/unlockre/components-library/commit/dcb4199971f7ed5429f841920c8d293d97a11da2))

# [51.0.0](https://github.com/unlockre/components-library/compare/v50.2.0...v51.0.0) (2025-06-09)


### Bug Fixes

* Fix AutocompleteField and PlacesAutocompleteField logic ([#623](https://github.com/unlockre/components-library/issues/623)) ([e8aa0be](https://github.com/unlockre/components-library/commit/e8aa0be25a421a25db309959ca56fca7f69113fa))


### BREAKING CHANGES

* Deprecate usePlace hook and remove AutocompleteField freeSolo prop

# [50.2.0](https://github.com/unlockre/components-library/compare/v50.1.0...v50.2.0) (2025-06-06)


### Features

* Improve autocomplete fields ([#621](https://github.com/unlockre/components-library/issues/621)) ([96a7ddc](https://github.com/unlockre/components-library/commit/96a7ddccdff281f48076dafda3aed4ca3e20dca9))

# [50.1.0](https://github.com/unlockre/components-library/compare/v50.0.1...v50.1.0) (2025-06-05)


### Features

* Support is disabled on dialog buttons ([#619](https://github.com/unlockre/components-library/issues/619)) ([00c18dd](https://github.com/unlockre/components-library/commit/00c18dd43a7a37389b3022605efd29e2b1aea4a9))

## [50.0.1](https://github.com/unlockre/components-library/compare/v50.0.0...v50.0.1) (2025-05-30)


### Bug Fixes

* Correct incorrect close handling on dialog modal ([#617](https://github.com/unlockre/components-library/issues/617)) ([a5799a6](https://github.com/unlockre/components-library/commit/a5799a6d0917a17f95efcf105bbcc24a46e13856))

# [50.0.0](https://github.com/unlockre/components-library/compare/v49.2.1...v50.0.0) (2025-05-23)


### Bug Fixes

* Fix Drawer closing issue ([#615](https://github.com/unlockre/components-library/issues/615)) ([79baf68](https://github.com/unlockre/components-library/commit/79baf6837c9f25d082ed07f95f6f873f9e359d2b))


### BREAKING CHANGES

* Rename Drawer props and export it without default

## [49.2.1](https://github.com/unlockre/components-library/compare/v49.2.0...v49.2.1) (2025-05-22)


### Bug Fixes

* Fix VirtualTable loading issues ([#613](https://github.com/unlockre/components-library/issues/613)) ([a4ec85a](https://github.com/unlockre/components-library/commit/a4ec85a807e792babda2b52a1f6e53791c9a96c6))

# [49.2.0](https://github.com/unlockre/components-library/compare/v49.1.1...v49.2.0) (2025-05-22)


### Features

* Add Menu layer prop ([#611](https://github.com/unlockre/components-library/issues/611)) ([f092afa](https://github.com/unlockre/components-library/commit/f092afad254afcbf4cab37daa3b004a7aadd8057))

## [49.1.1](https://github.com/unlockre/components-library/compare/v49.1.0...v49.1.1) (2025-05-15)


### Bug Fixes

* Fix menu positioning ([#610](https://github.com/unlockre/components-library/issues/610)) ([79888b4](https://github.com/unlockre/components-library/commit/79888b4a2a52f3055b344812d040d8d30def66b5))

# [49.1.0](https://github.com/unlockre/components-library/compare/v49.0.0...v49.1.0) (2025-05-15)


### Features

* Update drawer styles ([#608](https://github.com/unlockre/components-library/issues/608)) ([dbb10c3](https://github.com/unlockre/components-library/commit/dbb10c36aa2a651fa9af42585328c5511266360f))

# [49.0.0](https://github.com/unlockre/components-library/compare/v48.3.0...v49.0.0) (2025-05-08)


### Features

* Add menus components ([#604](https://github.com/unlockre/components-library/issues/604)) ([16189c7](https://github.com/unlockre/components-library/commit/16189c7c13dcfd09431a9887eedf2dbb203bed03))


### BREAKING CHANGES

* Remove old menu component and add new ones under a menus folder

# [48.3.0](https://github.com/unlockre/components-library/compare/v48.2.0...v48.3.0) (2025-05-07)


### Features

* Update colors ([#602](https://github.com/unlockre/components-library/issues/602)) ([08619a4](https://github.com/unlockre/components-library/commit/08619a43cb4e89175f59f79a013a7645e73bc799))

# [48.2.0](https://github.com/unlockre/components-library/compare/v48.1.2...v48.2.0) (2025-04-16)


### Features

* Add className to TabSelector ([#598](https://github.com/unlockre/components-library/issues/598)) ([867858b](https://github.com/unlockre/components-library/commit/867858ba5117164f143222697cf1b4c36b805792))

## [48.1.2](https://github.com/unlockre/components-library/compare/v48.1.1...v48.1.2) (2025-04-15)


### Bug Fixes

* Correct table header width ([#596](https://github.com/unlockre/components-library/issues/596)) ([54a7092](https://github.com/unlockre/components-library/commit/54a7092392fd6c24da29fe0182855b225e3e093c))
* Fix zebra stripping styling ([#595](https://github.com/unlockre/components-library/issues/595)) ([d35bc5d](https://github.com/unlockre/components-library/commit/d35bc5dd1b49fd36f985187e815413a25aa6f2c6))

## [48.1.1](https://github.com/unlockre/components-library/compare/v48.1.0...v48.1.1) (2025-04-14)


### Bug Fixes

* Correct icon with color shrinking ([#593](https://github.com/unlockre/components-library/issues/593)) ([674d2a6](https://github.com/unlockre/components-library/commit/674d2a66cf2a73a6c62433485f2e521225ebc244))
* Correct table header width align issues ([#592](https://github.com/unlockre/components-library/issues/592)) ([6ab1d8d](https://github.com/unlockre/components-library/commit/6ab1d8d8876eaf40e5d95068c1ae386e81b181ec))

# [48.1.0](https://github.com/unlockre/components-library/compare/v48.0.2...v48.1.0) (2025-04-11)


### Features

* Replace multi select checkbox behaviour ([#589](https://github.com/unlockre/components-library/issues/589)) ([04a4671](https://github.com/unlockre/components-library/commit/04a46710765cbe067305341a041919582a97ed1f))

## [48.0.2](https://github.com/unlockre/components-library/compare/v48.0.1...v48.0.2) (2025-04-08)


### Bug Fixes

* Unselect on ESC ([#587](https://github.com/unlockre/components-library/issues/587)) ([9872da0](https://github.com/unlockre/components-library/commit/9872da0f25c422a716fd5a8472b812a64062b709))

## [48.0.1](https://github.com/unlockre/components-library/compare/v48.0.0...v48.0.1) (2025-04-01)


### Bug Fixes

* Correct cell styles application order in virtual table ([#575](https://github.com/unlockre/components-library/issues/575)) ([b324c39](https://github.com/unlockre/components-library/commit/b324c39ef65ea759e36f7ae55bd89171e5785a7f))
* Correct header cell align ([#581](https://github.com/unlockre/components-library/issues/581)) ([636d700](https://github.com/unlockre/components-library/commit/636d700f988a7c338d6b9736477bbd5c8a999f3f))

# [48.0.0](https://github.com/unlockre/components-library/compare/v47.0.3...v48.0.0) (2025-03-31)


### Features

* Add rows multiple selection on shift + click ([#566](https://github.com/unlockre/components-library/issues/566)) ([b924690](https://github.com/unlockre/components-library/commit/b924690f4ff765e835db20898557fee3c4f850e6))


### BREAKING CHANGES

* Change onSelectedItemsChange signature and rename isRowClickable to areRowsClickable

## [47.0.3](https://github.com/unlockre/components-library/compare/v47.0.2...v47.0.3) (2025-03-31)


### Bug Fixes

* Fix peer dependencies ([#579](https://github.com/unlockre/components-library/issues/579)) ([b3d5aad](https://github.com/unlockre/components-library/commit/b3d5aad78de7c7451e9c0a17d98e9fc7365decc4))

## [47.0.2](https://github.com/unlockre/components-library/compare/v47.0.1...v47.0.2) (2025-03-31)


### Bug Fixes

* Move styled-components to peer dependency ([#577](https://github.com/unlockre/components-library/issues/577)) ([6235490](https://github.com/unlockre/components-library/commit/6235490d8a44d6fa69d4430a9d9e20de975016d4))

## [47.0.1](https://github.com/unlockre/components-library/compare/v47.0.0...v47.0.1) (2025-03-28)


### Bug Fixes

* Fix legacy button props type ([#576](https://github.com/unlockre/components-library/issues/576)) ([596ad1c](https://github.com/unlockre/components-library/commit/596ad1c696eef705f88c9124c5aef73e0db41435))

# [47.0.0](https://github.com/unlockre/components-library/compare/v46.2.0...v47.0.0) (2025-03-28)


### Bug Fixes

* Update next and react dependencies ([#574](https://github.com/unlockre/components-library/issues/574)) ([5fff4ff](https://github.com/unlockre/components-library/commit/5fff4ffeb8df12663c1711e6f9e910a87d64e254))


### BREAKING CHANGES

* Update react to ^18.0.0 and next to ^15.0.0

# [46.2.0](https://github.com/unlockre/components-library/compare/v46.1.2...v46.2.0) (2025-03-21)


### Features

* Add description for column headers ([#570](https://github.com/unlockre/components-library/issues/570)) ([f2466c8](https://github.com/unlockre/components-library/commit/f2466c8b83f04da6d0ba763fe7b55a13cf0a6b98))

## [46.1.2](https://github.com/unlockre/components-library/compare/v46.1.1...v46.1.2) (2025-03-18)


### Bug Fixes

* Fix CounterField min/max ([#567](https://github.com/unlockre/components-library/issues/567)) ([5fb0306](https://github.com/unlockre/components-library/commit/5fb0306ccdfdbf80861695d0f633e7992cd56f1f))

## [46.1.1](https://github.com/unlockre/components-library/compare/v46.1.0...v46.1.1) (2025-03-10)


### Bug Fixes

* Correct modal description font size ([#564](https://github.com/unlockre/components-library/issues/564)) ([8687902](https://github.com/unlockre/components-library/commit/868790250645e3e12e4b3e6bd3196ff0a7f792af))

# [46.1.0](https://github.com/unlockre/components-library/compare/v46.0.0...v46.1.0) (2025-03-07)


### Features

* Add alert dialog loading state ([#562](https://github.com/unlockre/components-library/issues/562)) ([ead173d](https://github.com/unlockre/components-library/commit/ead173d132fcb771c2b728492749e2438ca6b115))

# [46.0.0](https://github.com/unlockre/components-library/compare/v45.4.0...v46.0.0) (2025-02-27)


### Features

* Add isRowSelectable + Update sort direction icon ([#559](https://github.com/unlockre/components-library/issues/559)) ([e793dd9](https://github.com/unlockre/components-library/commit/e793dd9eb212663048d610836daad29c6c71d313))


### BREAKING CHANGES

* Rename areRowsSelectable to isRowSelectable

# [45.4.0](https://github.com/unlockre/components-library/compare/v45.3.1...v45.4.0) (2025-02-25)


### Features

* Support initialSortDirection in VirtualTable columns ([#557](https://github.com/unlockre/components-library/issues/557)) ([09d4929](https://github.com/unlockre/components-library/commit/09d4929678f916d51be920395317d3eb5dc2beb6))

## [45.3.1](https://github.com/unlockre/components-library/compare/v45.3.0...v45.3.1) (2025-02-25)


### Bug Fixes

* Fix VirtualTable sorting ([#554](https://github.com/unlockre/components-library/issues/554)) ([61c8648](https://github.com/unlockre/components-library/commit/61c86484b7796993dd03aca94845ba74d87cd5f0))
* Use spaces in fontVariantNumeric values ([#555](https://github.com/unlockre/components-library/issues/555)) ([1a6d9f0](https://github.com/unlockre/components-library/commit/1a6d9f0d88e30995691f3ca444dbc17fae3ac583))

# [45.3.0](https://github.com/unlockre/components-library/compare/v45.2.1...v45.3.0) (2025-02-24)


### Bug Fixes

* Add missing icon and correct margin on alert dialog ([#546](https://github.com/unlockre/components-library/issues/546)) ([3331c9c](https://github.com/unlockre/components-library/commit/3331c9c24a8bdcce1f79c230d40455fa76a5afec))
* Correct harcoded font families ([#548](https://github.com/unlockre/components-library/issues/548)) ([3841d63](https://github.com/unlockre/components-library/commit/3841d639f75d4e6bc7a513d868267cc82f726e5a))


### Features

* Support new typographies ([#547](https://github.com/unlockre/components-library/issues/547)) ([ef044b2](https://github.com/unlockre/components-library/commit/ef044b2bdbb63c3c2ff6692ea9dff6373b223ca9))

## [45.2.1](https://github.com/unlockre/components-library/compare/v45.2.0...v45.2.1) (2025-02-13)


### Bug Fixes

* Fix Dialog autoFocus ([#544](https://github.com/unlockre/components-library/issues/544)) ([7fac705](https://github.com/unlockre/components-library/commit/7fac705ce5d96be3ca987e0494b7cc529342aa03))

# [45.2.0](https://github.com/unlockre/components-library/compare/v45.1.5...v45.2.0) (2025-02-13)


### Features

* Add autoFocus prop to DialogActionButton ([#543](https://github.com/unlockre/components-library/issues/543)) ([a599094](https://github.com/unlockre/components-library/commit/a599094ecd2886da0697e2577b585b58eafdeaa4))

## [45.1.5](https://github.com/unlockre/components-library/compare/v45.1.4...v45.1.5) (2025-02-13)


### Bug Fixes

* Allow empty alert dialogs ([#542](https://github.com/unlockre/components-library/issues/542)) ([69de8bc](https://github.com/unlockre/components-library/commit/69de8bc03b694477ee5d617e94d70a73823e01cb))

## [45.1.4](https://github.com/unlockre/components-library/compare/v45.1.3...v45.1.4) (2025-02-13)


### Bug Fixes

* Add font-weight reset in UnstyledHeading ([#541](https://github.com/unlockre/components-library/issues/541)) ([af5e26c](https://github.com/unlockre/components-library/commit/af5e26c0210a173b85baae9b771a5dd41eb64997))

## [45.1.3](https://github.com/unlockre/components-library/compare/v45.1.2...v45.1.3) (2025-02-13)


### Bug Fixes

* Fix AlertDialog Props type ([#540](https://github.com/unlockre/components-library/issues/540)) ([74e6e0b](https://github.com/unlockre/components-library/commit/74e6e0b348bb183e387e9d7229c5582498da27f6))

## [45.1.2](https://github.com/unlockre/components-library/compare/v45.1.1...v45.1.2) (2025-02-13)


### Bug Fixes

* Fix DialogBox Props type ([#539](https://github.com/unlockre/components-library/issues/539)) ([b0e25e5](https://github.com/unlockre/components-library/commit/b0e25e5c6fe6ea5e01da1bd03453b98a25b7cc0a))

## [45.1.1](https://github.com/unlockre/components-library/compare/v45.1.0...v45.1.1) (2025-02-13)


### Bug Fixes

* Simplify Dialog logic ([#538](https://github.com/unlockre/components-library/issues/538)) ([fb470fd](https://github.com/unlockre/components-library/commit/fb470fd96ac7fed731fa6b4355f4a47fa7b62c8f))

# [45.1.0](https://github.com/unlockre/components-library/compare/v45.0.1...v45.1.0) (2025-02-12)


### Features

* Add dialog components ([#537](https://github.com/unlockre/components-library/issues/537)) ([a1d3f39](https://github.com/unlockre/components-library/commit/a1d3f393a76b02b4c65a76237423f2a4034f1d1f))

## [45.0.1](https://github.com/unlockre/components-library/compare/v45.0.0...v45.0.1) (2025-02-07)


### Bug Fixes

* Fix theme colors ([#536](https://github.com/unlockre/components-library/issues/536)) ([1568c10](https://github.com/unlockre/components-library/commit/1568c10af07e63a01dae845a114290cf4c6f9741))

# [45.0.0](https://github.com/unlockre/components-library/compare/v44.0.0...v45.0.0) (2025-02-06)


### Features

* Support Inter Variable font ([#533](https://github.com/unlockre/components-library/issues/533)) ([c6c2b62](https://github.com/unlockre/components-library/commit/c6c2b625f78d86a030a8ebfbfa40d075a968c3d9))


### BREAKING CHANGES

* Use Inter Variable instead of Inter font

# [44.0.0](https://github.com/unlockre/components-library/compare/v43.0.0...v44.0.0) (2025-02-06)


### Features

* Update theme colors ([#534](https://github.com/unlockre/components-library/issues/534)) ([af54e38](https://github.com/unlockre/components-library/commit/af54e38aaa84e474e848882b9d36a4b625b0dd13))


### BREAKING CHANGES

* Rename iconDisabled and iconSecondary theme colors

# [43.0.0](https://github.com/unlockre/components-library/compare/v42.1.4...v43.0.0) (2025-02-05)


### Features

* Add unstyled components ([#531](https://github.com/unlockre/components-library/issues/531)) ([64d21ff](https://github.com/unlockre/components-library/commit/64d21ff8c91ba39e8b1f0ca74bba1023047719da))


### BREAKING CHANGES

* Use named exports in unstyled components and deprecate UnstyledH2

## [42.1.4](https://github.com/unlockre/components-library/compare/v42.1.3...v42.1.4) (2025-01-31)


### Bug Fixes

* Fix VirtualTable issues ([#529](https://github.com/unlockre/components-library/issues/529)) ([cb77f6d](https://github.com/unlockre/components-library/commit/cb77f6d243938cf97ac5589da72379641e07491c))

## [42.1.3](https://github.com/unlockre/components-library/compare/v42.1.2...v42.1.3) (2025-01-31)


### Bug Fixes

* Fix VirtualTable fixed columns ([#527](https://github.com/unlockre/components-library/issues/527)) ([9212bb1](https://github.com/unlockre/components-library/commit/9212bb1731810fa09d508a236636d421836653ec))

## [42.1.2](https://github.com/unlockre/components-library/compare/v42.1.1...v42.1.2) (2025-01-30)


### Bug Fixes

* Fix VirtualTable fixed columns ([#523](https://github.com/unlockre/components-library/issues/523)) ([d236b69](https://github.com/unlockre/components-library/commit/d236b692511af7df19a09f384909c3a2ad52f3b5))

## [42.1.1](https://github.com/unlockre/components-library/compare/v42.1.0...v42.1.1) (2025-01-28)


### Bug Fixes

* Fix VirtualTable issues ([#521](https://github.com/unlockre/components-library/issues/521)) ([1ed8028](https://github.com/unlockre/components-library/commit/1ed802812bed4ed485825aa661af22cb43ce7977))

# [42.1.0](https://github.com/unlockre/components-library/compare/v42.0.1...v42.1.0) (2025-01-27)


### Features

* Support dynamic columns in VirtualTable ([#519](https://github.com/unlockre/components-library/issues/519)) ([788c891](https://github.com/unlockre/components-library/commit/788c891c5c74667067a00b6d13986979380fbc63))

## [42.0.1](https://github.com/unlockre/components-library/compare/v42.0.0...v42.0.1) (2025-01-25)


### Bug Fixes

* Update @unlockre/utils-array dependency ([#518](https://github.com/unlockre/components-library/issues/518)) ([e2e24f3](https://github.com/unlockre/components-library/commit/e2e24f3b0a0fa4fdd60a8747e69d65608b7ffd9a))

# [42.0.0](https://github.com/unlockre/components-library/compare/v41.3.0...v42.0.0) (2025-01-24)


### Features

* Support VirtualTable draggable rows ([#516](https://github.com/unlockre/components-library/issues/516)) ([aa9e9a4](https://github.com/unlockre/components-library/commit/aa9e9a408706020756c3257a6ec3baea478a7a4b))


### BREAKING CHANGES

* Remove VirtualTable onInfiniteScroll prop and change renderer params

# [41.3.0](https://github.com/unlockre/components-library/compare/v41.2.0...v41.3.0) (2024-12-19)


### Features

* Allow offset and fallback position ([#510](https://github.com/unlockre/components-library/issues/510)) ([7ba39f7](https://github.com/unlockre/components-library/commit/7ba39f70dd07c6dd89dfc3b24ff7f2810a4f64e9))

# [41.2.0](https://github.com/unlockre/components-library/compare/v41.1.0...v41.2.0) (2024-12-18)


### Features

* Add onMouseEnter and onMouseLeave props to Tooltip component ([#506](https://github.com/unlockre/components-library/issues/506)) ([968ad11](https://github.com/unlockre/components-library/commit/968ad1101465adf1758c8711e31a9a13712d5da5))

# [41.1.0](https://github.com/unlockre/components-library/compare/v41.0.0...v41.1.0) (2024-11-21)


### Features

* Add infinite scroll prop to storybook ([#502](https://github.com/unlockre/components-library/issues/502)) ([ee7c737](https://github.com/unlockre/components-library/commit/ee7c7375420ea0cdbce7457a67736f1ce70f3c57))
* Add infinite scrolling ([#501](https://github.com/unlockre/components-library/issues/501)) ([7e97364](https://github.com/unlockre/components-library/commit/7e97364d4bed216571ec0cd9033228a0c2f84b51))

# [41.0.0](https://github.com/unlockre/components-library/compare/v40.0.0...v41.0.0) (2024-11-17)


### Bug Fixes

* Fix VirtualTable scrolling issue ([#467](https://github.com/unlockre/components-library/issues/467)) ([66c0049](https://github.com/unlockre/components-library/commit/66c0049fd8cb4d87ecba8c0c6a2529e75b03b928))


### BREAKING CHANGES

* Change VirtualTable onScroll handler signature

# [40.0.0](https://github.com/unlockre/components-library/compare/v39.2.1...v40.0.0) (2024-11-15)


### Features

* Add violet variant to animated dots and replace shouldHighlightRowBorder prop in virtual table ([#462](https://github.com/unlockre/components-library/issues/462)) ([1183fe9](https://github.com/unlockre/components-library/commit/1183fe9ac288adf298506724ac4c93843a1dd13c))


### BREAKING CHANGES

* Replaced shouldHighlightRowBorder prop from VirtualTable with getRowHighlightedBorderColor

## [39.2.1](https://github.com/unlockre/components-library/compare/v39.2.0...v39.2.1) (2024-11-14)


### Bug Fixes

* Fix some VirtualTable issues ([#464](https://github.com/unlockre/components-library/issues/464)) ([0f6fb36](https://github.com/unlockre/components-library/commit/0f6fb36392041b681ded231750be394e8c444771))

# [39.2.0](https://github.com/unlockre/components-library/compare/v39.1.0...v39.2.0) (2024-11-14)


### Features

* Add onScroll prop to VirtualTable ([#461](https://github.com/unlockre/components-library/issues/461)) ([b51b534](https://github.com/unlockre/components-library/commit/b51b53461d756f329ebbc956e035e34c6bee06e3))

# [39.1.0](https://github.com/unlockre/components-library/compare/v39.0.0...v39.1.0) (2024-11-12)


### Features

* Add shouldHighlightSelectedItems prop + Add classNames ([#458](https://github.com/unlockre/components-library/issues/458)) ([fe2c0ae](https://github.com/unlockre/components-library/commit/fe2c0aecb479a70a1b8480d2c523b3b3da26d5a0))

# [39.0.0](https://github.com/unlockre/components-library/compare/v38.0.0...v39.0.0) (2024-11-08)


### Features

* Add multi-cell components ([#459](https://github.com/unlockre/components-library/issues/459)) ([05fe8ee](https://github.com/unlockre/components-library/commit/05fe8ee998cfa325d22eef467131f33834630f00))


### BREAKING CHANGES

* Rename TableColumn align prop to horizontalAlign and rename VirtualTable classNames

# [38.0.0](https://github.com/unlockre/components-library/compare/v37.1.0...v38.0.0) (2024-11-01)


### Features

* Add DateRangeField *CalendarRef props ([#457](https://github.com/unlockre/components-library/issues/457)) ([0ac17aa](https://github.com/unlockre/components-library/commit/0ac17aa6e929d90ab5c1cd4ebc85f3600edd6df5))


### BREAKING CHANGES

* Use named exports for DateField and DateRangeField

# [37.1.0](https://github.com/unlockre/components-library/compare/v37.0.0...v37.1.0) (2024-11-01)


### Features

* Add DateField calendarRef prop ([#455](https://github.com/unlockre/components-library/issues/455)) ([91bdeab](https://github.com/unlockre/components-library/commit/91bdeab82a1ca8fb6210c701fdce6d2bf642ac76))

# [37.0.0](https://github.com/unlockre/components-library/compare/v36.5.0...v37.0.0) (2024-10-24)


### Features

* Add grouped columns ([#454](https://github.com/unlockre/components-library/issues/454)) ([b296eae](https://github.com/unlockre/components-library/commit/b296eae0106ed16af73085e6cefeb05000ecab7b))


### BREAKING CHANGES

* Rename isZebraStriped (boolean) prop to isRowZebraStriped (function)

# [36.5.0](https://github.com/unlockre/components-library/compare/v36.4.0...v36.5.0) (2024-10-17)


### Features

* Add autoscaler debug ([#452](https://github.com/unlockre/components-library/issues/452)) ([2c512c4](https://github.com/unlockre/components-library/commit/2c512c489f136a37cc9a6c2f1ef20f4aba7ec9a1))

# [36.4.0](https://github.com/unlockre/components-library/compare/v36.3.0...v36.4.0) (2024-10-17)


### Features

* Add testId prop to buttons ([#450](https://github.com/unlockre/components-library/issues/450)) ([ad9d68a](https://github.com/unlockre/components-library/commit/ad9d68af6e5005303ad1c936fb5cded7bb108256))

# [36.3.0](https://github.com/unlockre/components-library/compare/v36.2.0...v36.3.0) (2024-10-16)


### Features

* Add themes to use in packages ([#445](https://github.com/unlockre/components-library/issues/445)) ([de16645](https://github.com/unlockre/components-library/commit/de166458926436a878990c5ea291f93d975d13aa))

# [36.2.0](https://github.com/unlockre/components-library/compare/v36.1.2...v36.2.0) (2024-10-15)


### Features

* Update widget header title type + add classnames ([#443](https://github.com/unlockre/components-library/issues/443)) ([bf5c2ca](https://github.com/unlockre/components-library/commit/bf5c2ca87fecf781cd05a029b44be82adc2037c9))

## [36.1.2](https://github.com/unlockre/components-library/compare/v36.1.1...v36.1.2) (2024-10-15)


### Bug Fixes

* Use props for component data-testid ([#441](https://github.com/unlockre/components-library/issues/441)) ([48c1be1](https://github.com/unlockre/components-library/commit/48c1be1b998a498ad2ef0dcd71e957d9b2f5ea4f))

## [36.1.1](https://github.com/unlockre/components-library/compare/v36.1.0...v36.1.1) (2024-10-15)


### Bug Fixes

* Add testid to select field dropdown button ([#439](https://github.com/unlockre/components-library/issues/439)) ([fa66e40](https://github.com/unlockre/components-library/commit/fa66e4068962ab83bc9c28200f220fb9a7ea3aa6))

# [36.1.0](https://github.com/unlockre/components-library/compare/v36.0.0...v36.1.0) (2024-10-15)


### Features

* Change zebra stripe color ([#437](https://github.com/unlockre/components-library/issues/437)) ([b9ee18b](https://github.com/unlockre/components-library/commit/b9ee18bcf198527124affc8d8bf8167455eacb8a))

# [36.0.0](https://github.com/unlockre/components-library/compare/v35.0.0...v36.0.0) (2024-10-15)


### Features

* Add tab variant ([#435](https://github.com/unlockre/components-library/issues/435)) ([#436](https://github.com/unlockre/components-library/issues/436)) ([fcb26b4](https://github.com/unlockre/components-library/commit/fcb26b4d5980f5652bafb9b657a75b9bfceb5938))


### BREAKING CHANGES

* Changes in TabProvider. It was renamed and receives new props.

# [35.0.0](https://github.com/unlockre/components-library/compare/v34.0.1...v35.0.0) (2024-10-08)


### Features

* Upgrade to react@^18.0.0 ([#433](https://github.com/unlockre/components-library/issues/433)) ([c79454d](https://github.com/unlockre/components-library/commit/c79454d08d2dffead0a066362d15d1c31f357af5))


### BREAKING CHANGES

* Upgrade to react@^18.0.0

## [34.0.1](https://github.com/unlockre/components-library/compare/v34.0.0...v34.0.1) (2024-10-08)


### Bug Fixes

* Propagate isDisabled to new button ([#431](https://github.com/unlockre/components-library/issues/431)) ([084464d](https://github.com/unlockre/components-library/commit/084464df6c66900519c08eec0388e2f50a7ab742))

# [34.0.0](https://github.com/unlockre/components-library/compare/v33.0.0...v34.0.0) (2024-10-07)


### Bug Fixes

* Fix range fields ([#430](https://github.com/unlockre/components-library/issues/430)) ([068d2e4](https://github.com/unlockre/components-library/commit/068d2e495c6557b4e110441112d3e81ad9ab14a1))


### Features

* Add new ButtonGroup variant (primary) ([#428](https://github.com/unlockre/components-library/issues/428)) ([7bdbea4](https://github.com/unlockre/components-library/commit/7bdbea45f047df74595409545426743dc40da386))
* Add range fields layout ([#424](https://github.com/unlockre/components-library/issues/424)) ([5511943](https://github.com/unlockre/components-library/commit/551194344de0afd435848d721aa1dea9e83284de))


### BREAKING CHANGES

* Remove label prop from range fields

# [33.0.0](https://github.com/unlockre/components-library/compare/v32.3.0...v33.0.0) (2024-10-06)


### Features

* Add new button component ([#425](https://github.com/unlockre/components-library/issues/425)) ([dc05225](https://github.com/unlockre/components-library/commit/dc0522503c5d33a53b8a697be28ce8cd05c20889))


### BREAKING CHANGES

* Remove ButtonProps type

# [32.3.0](https://github.com/unlockre/components-library/compare/v32.2.1...v32.3.0) (2024-10-04)


### Features

* Add cell class names ([#426](https://github.com/unlockre/components-library/issues/426)) ([c7846c6](https://github.com/unlockre/components-library/commit/c7846c669433cbbaacc7bbc8ea4f5096030fa637))

## [32.2.1](https://github.com/unlockre/components-library/compare/v32.2.0...v32.2.1) (2024-09-30)


### Bug Fixes

* Show highlight border bottom ([#421](https://github.com/unlockre/components-library/issues/421)) ([e20a075](https://github.com/unlockre/components-library/commit/e20a0750de49c4c443911f53cc2375dc720399e4))

# [32.2.0](https://github.com/unlockre/components-library/compare/v32.1.3...v32.2.0) (2024-09-26)


### Features

* Add virtual table summary row border ([#417](https://github.com/unlockre/components-library/issues/417)) ([ee5d9d3](https://github.com/unlockre/components-library/commit/ee5d9d372f1a3e3396d3dac801f7cee50fc99fb5))

## [32.1.3](https://github.com/unlockre/components-library/compare/v32.1.2...v32.1.3) (2024-09-26)


### Bug Fixes

* Modal should hide styles ([#418](https://github.com/unlockre/components-library/issues/418)) ([8acee57](https://github.com/unlockre/components-library/commit/8acee576a7ecd1a1bf8128e2893d5fb5ca57b0c9))

## [32.1.2](https://github.com/unlockre/components-library/compare/v32.1.1...v32.1.2) (2024-09-25)


### Bug Fixes

* Fix getColor theme utility ([#416](https://github.com/unlockre/components-library/issues/416)) ([3b7f8d4](https://github.com/unlockre/components-library/commit/3b7f8d43636864e502d9d67deed1dee82f20a0e3))

## [32.1.1](https://github.com/unlockre/components-library/compare/v32.1.0...v32.1.1) (2024-09-25)


### Bug Fixes

* Fix theme issues ([#415](https://github.com/unlockre/components-library/issues/415)) ([6cbb556](https://github.com/unlockre/components-library/commit/6cbb55613934f952a551c12f77df3f412c1c1896))

# [32.1.0](https://github.com/unlockre/components-library/compare/v32.0.0...v32.1.0) (2024-09-25)


### Features

* Add theme extend utility ([#413](https://github.com/unlockre/components-library/issues/413)) ([f41bd09](https://github.com/unlockre/components-library/commit/f41bd0941bdc3aace863115d5823d164adf59670))

# [32.0.0](https://github.com/unlockre/components-library/compare/v31.2.1...v32.0.0) (2024-09-24)


### Features

* Support more themes ([#409](https://github.com/unlockre/components-library/issues/409)) ([8f594a7](https://github.com/unlockre/components-library/commit/8f594a74111e9292b9a554e840698dc42c3288c2))


### BREAKING CHANGES

* ThemeProvider now expects a theme as prop and rename theme.colors.palette to plural.

## [31.2.1](https://github.com/unlockre/components-library/compare/v31.2.0...v31.2.1) (2024-09-24)


### Bug Fixes

* Add Portal to Tooltip ([#410](https://github.com/unlockre/components-library/issues/410)) ([8291517](https://github.com/unlockre/components-library/commit/8291517b95b0b0cc3dd825a17f240154ef6c4839))

# [31.2.0](https://github.com/unlockre/components-library/compare/v31.1.0...v31.2.0) (2024-09-19)


### Bug Fixes

* Remove gray variants ([#407](https://github.com/unlockre/components-library/issues/407)) ([65c4cfa](https://github.com/unlockre/components-library/commit/65c4cfa3ca5866b471690afe91050dedf4578d0b))


### Features

* Add LinkInfoValue ([#404](https://github.com/unlockre/components-library/issues/404)) ([7ee287e](https://github.com/unlockre/components-library/commit/7ee287e3201381abcba5ca100f0c926fa86585ca))
* Add stackable modal ([#396](https://github.com/unlockre/components-library/issues/396)) ([19d36ab](https://github.com/unlockre/components-library/commit/19d36ab87904f2f4d22a4b7a17b85d84f1ea8ba1))

# [31.1.0](https://github.com/unlockre/components-library/compare/v31.0.2...v31.1.0) (2024-09-18)


### Features

* Forward progress bar ref ([#405](https://github.com/unlockre/components-library/issues/405)) ([126c408](https://github.com/unlockre/components-library/commit/126c408709dc6ed8c35ba01eab4ddec1e5f6ba0b))

## [31.0.2](https://github.com/unlockre/components-library/compare/v31.0.1...v31.0.2) (2024-09-10)


### Bug Fixes

* Add descriptionTooltipWidth prop to InfoEntry ([#402](https://github.com/unlockre/components-library/issues/402)) ([4ac4559](https://github.com/unlockre/components-library/commit/4ac4559844d8abf1630eab91925e1801404d6abb))

## [31.0.1](https://github.com/unlockre/components-library/compare/v31.0.0...v31.0.1) (2024-09-05)


### Bug Fixes

* Fix TabSelector ([#401](https://github.com/unlockre/components-library/issues/401)) ([29cd94f](https://github.com/unlockre/components-library/commit/29cd94f2418258c9778b74a24d436d4438f692c1))

# [31.0.0](https://github.com/unlockre/components-library/compare/v30.0.0...v31.0.0) (2024-09-05)


### Features

* Improve Tab type ([#399](https://github.com/unlockre/components-library/issues/399)) ([e058aa2](https://github.com/unlockre/components-library/commit/e058aa24be0cf234527d750481536cc7145345b7))


### BREAKING CHANGES

* Remove Tab.label field (now TabSelector has a getTabLabel prop)

# [30.0.0](https://github.com/unlockre/components-library/compare/v29.1.0...v30.0.0) (2024-09-04)


### Features

* Add Tab name generic ([#398](https://github.com/unlockre/components-library/issues/398)) ([b87d262](https://github.com/unlockre/components-library/commit/b87d262b5baed759d63ebbf90ee4301c52977705))


### BREAKING CHANGES

* TabSelector Tab type now requires a mandatory TabName

# [29.1.0](https://github.com/unlockre/components-library/compare/v29.0.0...v29.1.0) (2024-09-04)


### Features

* Export InfoItemNoValuePill component ([#397](https://github.com/unlockre/components-library/issues/397)) ([969c9ee](https://github.com/unlockre/components-library/commit/969c9ee981dafdf1b58a53d21ea61ffe45e70488))

# [29.0.0](https://github.com/unlockre/components-library/compare/v28.0.0...v29.0.0) (2024-09-03)


### Features

* Add InfoList component ([#395](https://github.com/unlockre/components-library/issues/395)) ([1f3f156](https://github.com/unlockre/components-library/commit/1f3f156e4e04255b5968a79b0b7635fb98807612))
* Add ref and event handler props to IconWithColor component ([#394](https://github.com/unlockre/components-library/issues/394)) ([11d8925](https://github.com/unlockre/components-library/commit/11d8925d12de60e9d680b269d187d2542cf625dd))


### BREAKING CHANGES

* Deprecate InfoItemList component and add InfoList one

# [28.0.0](https://github.com/unlockre/components-library/compare/v27.0.0...v28.0.0) (2024-08-19)


### Features

* Support link in InfoItem ([#390](https://github.com/unlockre/components-library/issues/390)) ([f6594ea](https://github.com/unlockre/components-library/commit/f6594ea99b3a1aec76721bdf9b8b8e2c3d957fff))


### BREAKING CHANGES

* Add mandatory variant prop to Link

# [27.0.0](https://github.com/unlockre/components-library/compare/v26.9.3...v27.0.0) (2024-08-16)


### Features

* Add withErrorBoundary HOC ([#391](https://github.com/unlockre/components-library/issues/391)) ([600428a](https://github.com/unlockre/components-library/commit/600428a15fe862b3a1380e63937f7c17633ed524))


### BREAKING CHANGES

* Export ErrorBoundary instead of default

## [26.9.3](https://github.com/unlockre/components-library/compare/v26.9.2...v26.9.3) (2024-08-12)


### Bug Fixes

* Fix PrintingHider return type ([b584944](https://github.com/unlockre/components-library/commit/b584944562bc83a07be05acac16067ef7caec7d0))

## [26.9.2](https://github.com/unlockre/components-library/compare/v26.9.1...v26.9.2) (2024-08-12)


### Bug Fixes

* Fix PrintingHider ([#388](https://github.com/unlockre/components-library/issues/388)) ([f54b0a1](https://github.com/unlockre/components-library/commit/f54b0a16a509eda0779ef43234bd29a773b357b3))

## [26.9.1](https://github.com/unlockre/components-library/compare/v26.9.0...v26.9.1) (2024-08-07)


### Bug Fixes

* Prevent getItemId call when items array is empty ([#386](https://github.com/unlockre/components-library/issues/386)) ([47bd1f3](https://github.com/unlockre/components-library/commit/47bd1f3dbf62106ac120ebd17b8f215b39a0f297))

# [26.9.0](https://github.com/unlockre/components-library/compare/v26.8.0...v26.9.0) (2024-08-06)


### Features

* Add autoscaler component ([#384](https://github.com/unlockre/components-library/issues/384)) ([ae9d080](https://github.com/unlockre/components-library/commit/ae9d080468b0dfe8b0364c2c6cd24e79fd181660))
* Add data name to printing ready ([#383](https://github.com/unlockre/components-library/issues/383)) ([6209f19](https://github.com/unlockre/components-library/commit/6209f19d3c49bab181281675d33d47e776199720))

# [26.8.0](https://github.com/unlockre/components-library/compare/v26.7.0...v26.8.0) (2024-08-06)


### Bug Fixes

* Fix virtual table last row style ([#380](https://github.com/unlockre/components-library/issues/380)) ([7747ea7](https://github.com/unlockre/components-library/commit/7747ea7b46c73e742d32e67c48cc76bdb841e05d))


### Features

* Return null on print view ([#381](https://github.com/unlockre/components-library/issues/381)) ([1f6aba7](https://github.com/unlockre/components-library/commit/1f6aba7558a2654bf8a3c9fd978a0155a32d7220))

# [26.7.0](https://github.com/unlockre/components-library/compare/v26.6.0...v26.7.0) (2024-08-01)


### Features

* Use number format in counter field ([#378](https://github.com/unlockre/components-library/issues/378)) ([5b927ea](https://github.com/unlockre/components-library/commit/5b927ea0dc886e845b42459d287ecd364ed28c35))

# [26.6.0](https://github.com/unlockre/components-library/compare/v26.5.0...v26.6.0) (2024-07-29)


### Features

* Add new map view component ([#376](https://github.com/unlockre/components-library/issues/376)) ([101b26c](https://github.com/unlockre/components-library/commit/101b26cd3c03c2a0434bf80b95e1a29ac8579633))
* Add printing hide components ([#375](https://github.com/unlockre/components-library/issues/375)) ([432852a](https://github.com/unlockre/components-library/commit/432852afb83a91c040d54369598904e2e8aefde0))

# [26.5.0](https://github.com/unlockre/components-library/compare/v26.4.0...v26.5.0) (2024-07-26)


### Bug Fixes

* Correct table alignment ([#361](https://github.com/unlockre/components-library/issues/361)) ([2589374](https://github.com/unlockre/components-library/commit/258937417eb45959ecf9b28ec6606c5c49cbd24b))
* Hide footer in virtual table when no getSummary was provided in table columns ([#372](https://github.com/unlockre/components-library/issues/372)) ([077e577](https://github.com/unlockre/components-library/commit/077e577ab822e404898ab2cb15af0afac0b99c57))
* Improve table cell rendering ([#370](https://github.com/unlockre/components-library/issues/370)) ([037771b](https://github.com/unlockre/components-library/commit/037771bc3c156ae7f10db32b8579b997bf890567))
* Remove extra paddings from each row ([#369](https://github.com/unlockre/components-library/issues/369)) ([2a22622](https://github.com/unlockre/components-library/commit/2a22622551e724f48d24597c8d7fd83cd3a8560f))
* Show table bottom border in dense variant ([#371](https://github.com/unlockre/components-library/issues/371)) ([cd37abf](https://github.com/unlockre/components-library/commit/cd37abfee0a9372ee3d07d07630d2a5ae28437a9))


### Features

* Add highlighting border to row ([#373](https://github.com/unlockre/components-library/issues/373)) ([8b72126](https://github.com/unlockre/components-library/commit/8b72126458b00b08d30e11e0a585352b69b9fceb))

# [26.4.0](https://github.com/unlockre/components-library/compare/v26.3.1...v26.4.0) (2024-07-15)


### Features

* Add inputRef to PropertyFinder ([#367](https://github.com/unlockre/components-library/issues/367)) ([ae7bd22](https://github.com/unlockre/components-library/commit/ae7bd22d2776681301c93af902ce2b6bae92e5a7))

## [26.3.1](https://github.com/unlockre/components-library/compare/v26.3.0...v26.3.1) (2024-07-12)


### Bug Fixes

* Improve focus and keyboard handling in property finder ([#365](https://github.com/unlockre/components-library/issues/365)) ([adc20f1](https://github.com/unlockre/components-library/commit/adc20f153f0e10e39702f1e490e6fb919ce24d0f))

# [26.3.0](https://github.com/unlockre/components-library/compare/v26.2.3...v26.3.0) (2024-07-11)


### Features

* Add renderTableHeaderCellContent prop to virtual table ([#363](https://github.com/unlockre/components-library/issues/363)) ([bbf5d9a](https://github.com/unlockre/components-library/commit/bbf5d9aa3c9378da64ec21b1544d19adb83490c0))

## [26.2.3](https://github.com/unlockre/components-library/compare/v26.2.2...v26.2.3) (2024-07-11)


### Bug Fixes

* Accept ReactNode as tooltip children ([#359](https://github.com/unlockre/components-library/issues/359)) ([f1b2b3d](https://github.com/unlockre/components-library/commit/f1b2b3dd75ae26e203e32d847825479511a9068c))

## [26.2.2](https://github.com/unlockre/components-library/compare/v26.2.1...v26.2.2) (2024-07-08)


### Bug Fixes

* Fix table loading height and cells and header text align ([#355](https://github.com/unlockre/components-library/issues/355)) ([050d082](https://github.com/unlockre/components-library/commit/050d0821d0e9a6fa15ff215f518a2414904572ed))

## [26.2.1](https://github.com/unlockre/components-library/compare/v26.2.0...v26.2.1) (2024-07-08)


### Bug Fixes

* Correct weird behaviour on menu open ([#356](https://github.com/unlockre/components-library/issues/356)) ([2d4b43d](https://github.com/unlockre/components-library/commit/2d4b43d0843fc9ee31dc49ef40342c9dad89f530))

# [26.2.0](https://github.com/unlockre/components-library/compare/v26.1.0...v26.2.0) (2024-07-04)


### Features

* Add align to head and summary ([#353](https://github.com/unlockre/components-library/issues/353)) ([51a841c](https://github.com/unlockre/components-library/commit/51a841c55015f22a8f846b5da85aaa72954d34e5))

# [26.1.0](https://github.com/unlockre/components-library/compare/v26.0.0...v26.1.0) (2024-06-27)


### Features

* Add tab selector component ([#351](https://github.com/unlockre/components-library/issues/351)) ([70ac15e](https://github.com/unlockre/components-library/commit/70ac15ee204cfd639e7161c0f51433a14ef4594b))

# [26.0.0](https://github.com/unlockre/components-library/compare/v25.2.1...v26.0.0) (2024-06-22)


### Bug Fixes

* Simplify PropertyFinder ([#350](https://github.com/unlockre/components-library/issues/350)) ([1c3cf6d](https://github.com/unlockre/components-library/commit/1c3cf6df5c12d9968bde82ffc71b3c830a4e2b34))


### BREAKING CHANGES

* Deprecate keepOpen PropertyFinder prop

## [25.2.1](https://github.com/unlockre/components-library/compare/v25.2.0...v25.2.1) (2024-06-07)


### Bug Fixes

* Fix fields styling ([#348](https://github.com/unlockre/components-library/issues/348)) ([81ddbae](https://github.com/unlockre/components-library/commit/81ddbae83fb494e1f753ad51e1fb716907feb8ba))

# [25.2.0](https://github.com/unlockre/components-library/compare/v25.1.0...v25.2.0) (2024-06-05)


### Features

* Add table scroll listener and header hiding option ([#346](https://github.com/unlockre/components-library/issues/346)) ([3163614](https://github.com/unlockre/components-library/commit/3163614084596905d7718ff456e6deef5a52339c))

# [25.1.0](https://github.com/unlockre/components-library/compare/v25.0.0...v25.1.0) (2024-05-29)


### Features

* Improve VirtualTable component ([#344](https://github.com/unlockre/components-library/issues/344)) ([a10114b](https://github.com/unlockre/components-library/commit/a10114be177fbf647545ab47de7f5b63e6fd1cfe))

# [25.0.0](https://github.com/unlockre/components-library/compare/v24.1.0...v25.0.0) (2024-05-27)


### Features

* Add VirtualTable summaries ([#342](https://github.com/unlockre/components-library/issues/342)) ([deddd4b](https://github.com/unlockre/components-library/commit/deddd4b867c258250592dd6dc713c767bc4f9be8))


### BREAKING CHANGES

* Rename VirtualTable prop getRowId to getItemId

# [24.1.0](https://github.com/unlockre/components-library/compare/v24.0.0...v24.1.0) (2024-05-22)


### Features

* Add empty state to InfoItem ([#319](https://github.com/unlockre/components-library/issues/319)) ([1e77e36](https://github.com/unlockre/components-library/commit/1e77e363d50cb29d058d4bf2c17ac550c2667021))

# [24.0.0](https://github.com/unlockre/components-library/compare/v23.5.0...v24.0.0) (2024-05-14)


### Bug Fixes

* Update lila 500 color definition ([#321](https://github.com/unlockre/components-library/issues/321)) ([d2fb0d0](https://github.com/unlockre/components-library/commit/d2fb0d0095a1a04b4dcc16264839598168a9e47c))


### Features

* Add drawer improvements ([#338](https://github.com/unlockre/components-library/issues/338)) ([5d04f2f](https://github.com/unlockre/components-library/commit/5d04f2ff69ac14b765853b977c877a22f79d2e36))
* Add new features to EmptyState ([#339](https://github.com/unlockre/components-library/issues/339)) ([a35888a](https://github.com/unlockre/components-library/commit/a35888ae220867b0223b9ab89e12183fd2380273))


### BREAKING CHANGES

* Remove footer prop in Drawer component

# [23.5.0](https://github.com/unlockre/components-library/compare/v23.4.0...v23.5.0) (2024-05-02)


### Features

* Add progress-bar components ([#336](https://github.com/unlockre/components-library/issues/336)) ([d03ca74](https://github.com/unlockre/components-library/commit/d03ca74f1a99dd91d3274586444c5ff8fb19af1a))

# [23.4.0](https://github.com/unlockre/components-library/compare/v23.3.0...v23.4.0) (2024-04-25)


### Features

* Add getGroupDescription prop to VirtualTable ([#334](https://github.com/unlockre/components-library/issues/334)) ([01c08df](https://github.com/unlockre/components-library/commit/01c08df12862c3be6900e6f29a64e76b842536d0))

# [23.3.0](https://github.com/unlockre/components-library/compare/v23.2.0...v23.3.0) (2024-04-24)


### Features

* Improve VirtualTable (zebra striping and grouping) ([#332](https://github.com/unlockre/components-library/issues/332)) ([c0b42f5](https://github.com/unlockre/components-library/commit/c0b42f57e4beebe26f152c1e1cbaff47514c5442))

# [23.2.0](https://github.com/unlockre/components-library/compare/v23.1.0...v23.2.0) (2024-04-10)


### Bug Fixes

* Fix isItemListOpened state in select field ([#328](https://github.com/unlockre/components-library/issues/328)) ([27f57e8](https://github.com/unlockre/components-library/commit/27f57e80e83641e5a3ad3fbadfe4bea9c1e8e966))


### Features

* Add KeywayIcon ([#317](https://github.com/unlockre/components-library/issues/317)) ([69556ed](https://github.com/unlockre/components-library/commit/69556edd596e053130c11efce13eb4c565f3cbf5))

# [23.1.0](https://github.com/unlockre/components-library/compare/v23.0.0...v23.1.0) (2024-03-22)


### Features

* Add noHoverEffect prop to WidgetLayout ([#323](https://github.com/unlockre/components-library/issues/323)) ([4a2e5bb](https://github.com/unlockre/components-library/commit/4a2e5bb87d7c248ada760e1604a67fa36045c862))

# [23.0.0](https://github.com/unlockre/components-library/compare/v22.1.0...v23.0.0) (2024-03-20)


### Features

* Add WidgetsGrid component ([#322](https://github.com/unlockre/components-library/issues/322)) ([86c25cc](https://github.com/unlockre/components-library/commit/86c25cce96b25326af1622cd6c32029713d78cb3))


### BREAKING CHANGES

* Now WidgetsColumn and WidgetsRow components use grid layout

# [22.1.0](https://github.com/unlockre/components-library/compare/v22.0.0...v22.1.0) (2024-02-29)


### Features

* Add yellow color to temporary color palette ([#318](https://github.com/unlockre/components-library/issues/318)) ([aeb76d5](https://github.com/unlockre/components-library/commit/aeb76d5d77a4a2aba42e602a0681ca341499eea5))

# [22.0.0](https://github.com/unlockre/components-library/compare/v21.7.1...v22.0.0) (2024-02-27)


### Features

* Add size prop to EmptyState ([#315](https://github.com/unlockre/components-library/issues/315)) ([e19560c](https://github.com/unlockre/components-library/commit/e19560c39fe79087ae61eb5e60f8a88ad090d9b0))


### BREAKING CHANGES

* Add size mandatory prop to EmptyState

## [21.7.1](https://github.com/unlockre/components-library/compare/v21.7.0...v21.7.1) (2024-02-22)


### Bug Fixes

* Update zebra striped table logic ([#314](https://github.com/unlockre/components-library/issues/314)) ([4dafc86](https://github.com/unlockre/components-library/commit/4dafc86e89e72a0d19218ea38fc6a60039501a33))

# [21.7.0](https://github.com/unlockre/components-library/compare/v21.6.0...v21.7.0) (2024-02-21)


### Features

* Add EmptyState component ([#313](https://github.com/unlockre/components-library/issues/313)) ([6135aa0](https://github.com/unlockre/components-library/commit/6135aa0116553fa5b1bc7fd65f92e99d172458ad))
* Add VirtualTable variant ([#312](https://github.com/unlockre/components-library/issues/312)) ([f43fc02](https://github.com/unlockre/components-library/commit/f43fc024a62a8eab0472ddb1d15a67a573a84905))

# [21.6.0](https://github.com/unlockre/components-library/compare/v21.5.0...v21.6.0) (2024-02-09)


### Bug Fixes

* Add overflow: hidden to VirtualTable's root div component ([#311](https://github.com/unlockre/components-library/issues/311)) ([d2c0be8](https://github.com/unlockre/components-library/commit/d2c0be85f158de0666a2be99db35d12c99addd42))


### Features

* Add label optional prop to WidgetActionButton ([#310](https://github.com/unlockre/components-library/issues/310)) ([ed73eee](https://github.com/unlockre/components-library/commit/ed73eee8be1feb8d1297d3885fbf82c11793f281))

# [21.5.0](https://github.com/unlockre/components-library/compare/v21.4.0...v21.5.0) (2024-02-06)


### Features

* Add CenteredSpinner component ([#306](https://github.com/unlockre/components-library/issues/306)) ([df3e5ad](https://github.com/unlockre/components-library/commit/df3e5ad6aaa0c07dd114a77284633429cca01c06))
* Add IconButton component ([#305](https://github.com/unlockre/components-library/issues/305)) ([efdc30c](https://github.com/unlockre/components-library/commit/efdc30c816692c21271db88451fcfaa350d2f4c1))
* Add info-item components ([#309](https://github.com/unlockre/components-library/issues/309)) ([b01aa52](https://github.com/unlockre/components-library/commit/b01aa521e72b3c8bb8f17a4f70f5d074bcefb3a9))
* Add shapes components ([#308](https://github.com/unlockre/components-library/issues/308)) ([7eb69bd](https://github.com/unlockre/components-library/commit/7eb69bde9f781aa737fcc02b98acb560cabc843b))
* Add widget components ([#307](https://github.com/unlockre/components-library/issues/307)) ([bcf8c86](https://github.com/unlockre/components-library/commit/bcf8c86ed313bd088b1efa74e0de7e49d13dc7e2))

# [21.4.0](https://github.com/unlockre/components-library/compare/v21.3.2...v21.4.0) (2024-02-01)


### Features

* Support Phosphor icons ([#304](https://github.com/unlockre/components-library/issues/304)) ([4b05f67](https://github.com/unlockre/components-library/commit/4b05f67dbf4135bc088e30860b4c728f1ccbfb41))

## [21.3.2](https://github.com/unlockre/components-library/compare/v21.3.1...v21.3.2) (2024-01-30)


### Bug Fixes

* Add gradient border to no properties component ([#303](https://github.com/unlockre/components-library/issues/303)) ([9e005e1](https://github.com/unlockre/components-library/commit/9e005e13a6a260146030118ff8324eb54ee423de))

## [21.3.1](https://github.com/unlockre/components-library/compare/v21.3.0...v21.3.1) (2024-01-25)


### Bug Fixes

* Correct coloring on property finder no results blue variant ([#302](https://github.com/unlockre/components-library/issues/302)) ([8cc14e7](https://github.com/unlockre/components-library/commit/8cc14e76b22370306bf2008242d3cab333637a59))

# [21.3.0](https://github.com/unlockre/components-library/compare/v21.2.1...v21.3.0) (2024-01-25)


### Bug Fixes

* Correct circular dependency on property finder ([#301](https://github.com/unlockre/components-library/issues/301)) ([f296741](https://github.com/unlockre/components-library/commit/f2967414297718b02f68684e45456114b797ab87))


### Features

* Add keypilot variant for property finder ([#300](https://github.com/unlockre/components-library/issues/300)) ([bf00b85](https://github.com/unlockre/components-library/commit/bf00b85084292243f4b5908d13810ca4cbbb8c48))

## [21.2.1](https://github.com/unlockre/components-library/compare/v21.2.0...v21.2.1) (2023-11-30)


### Bug Fixes

* Remove min-width from PillDropdown ([#297](https://github.com/unlockre/components-library/issues/297)) ([9217bc5](https://github.com/unlockre/components-library/commit/9217bc574f8b7abea896d411ff3011b2e576e44f))
* Remove unnecesary overflow ([#296](https://github.com/unlockre/components-library/issues/296)) ([17d7d1f](https://github.com/unlockre/components-library/commit/17d7d1fda539c29ea3db719944885cb3850a84f2))

# [21.2.0](https://github.com/unlockre/components-library/compare/v21.1.0...v21.2.0) (2023-11-27)


### Features

* Export ButtonGroup height (buttonGroupHeight) ([#295](https://github.com/unlockre/components-library/issues/295)) ([ee6c709](https://github.com/unlockre/components-library/commit/ee6c70997c21f73bd96bda0998544c2e03d8cf75))

# [21.1.0](https://github.com/unlockre/components-library/compare/v21.0.0...v21.1.0) (2023-11-23)


### Features

* Add useThemeBreakpoints hook ([#291](https://github.com/unlockre/components-library/issues/291)) ([75d6714](https://github.com/unlockre/components-library/commit/75d6714b073eabdd32ea0ef98343d662a5e4da3b))

# [21.0.0](https://github.com/unlockre/components-library/compare/v20.8.3...v21.0.0) (2023-11-15)


### Features

* Add xl desktop size to theme ([#289](https://github.com/unlockre/components-library/issues/289)) ([8972fd1](https://github.com/unlockre/components-library/commit/8972fd1bf9f5b988754c5017004dd3fd23ed899a))
* Allow tooltip placement ([#290](https://github.com/unlockre/components-library/issues/290)) ([d82dd6a](https://github.com/unlockre/components-library/commit/d82dd6a17f35e4d39b40348e49a9f4e20f53fbc3))


### BREAKING CHANGES

* useTooltip hook now receives a mandatory tooltipPlacement param

## [20.8.3](https://github.com/unlockre/components-library/compare/v20.8.2...v20.8.3) (2023-11-09)


### Bug Fixes

* Fix file manager empty state ([#288](https://github.com/unlockre/components-library/issues/288)) ([47e7699](https://github.com/unlockre/components-library/commit/47e76997d46c56ed3911baf03a6ecd5ff5c67e5c))

## [20.8.2](https://github.com/unlockre/components-library/compare/v20.8.1...v20.8.2) (2023-11-09)


### Bug Fixes

* Fix kaTableWrapperComponent ([#287](https://github.com/unlockre/components-library/issues/287)) ([707abb0](https://github.com/unlockre/components-library/commit/707abb0c85261c685d9f24922189fcd32330646c))

## [20.8.1](https://github.com/unlockre/components-library/compare/v20.8.0...v20.8.1) (2023-11-02)


### Bug Fixes

* Add border-color to PropertyInput container ([#286](https://github.com/unlockre/components-library/issues/286)) ([a228dfb](https://github.com/unlockre/components-library/commit/a228dfb5ec820d2815edbddd8ab96018f52535e3))

# [20.8.0](https://github.com/unlockre/components-library/compare/v20.7.7...v20.8.0) (2023-11-02)


### Features

* Add ButtonGroup component ([#285](https://github.com/unlockre/components-library/issues/285)) ([df630fc](https://github.com/unlockre/components-library/commit/df630fc2993e34341bf9816cee1fef1b54f0a415))

## [20.7.7](https://github.com/unlockre/components-library/compare/v20.7.6...v20.7.7) (2023-10-31)


### Bug Fixes

* Fix can delete items on file manager ([#284](https://github.com/unlockre/components-library/issues/284)) ([8c2f208](https://github.com/unlockre/components-library/commit/8c2f2082f7e80004e57cb14b5b6bc453ff4f6cdf))

## [20.7.6](https://github.com/unlockre/components-library/compare/v20.7.5...v20.7.6) (2023-10-30)


### Bug Fixes

* Empty NumberField input when value is null or undefined ([#283](https://github.com/unlockre/components-library/issues/283)) ([a3fde79](https://github.com/unlockre/components-library/commit/a3fde790cf81ce9b1a7de457944a7251c30e3acb))

## [20.7.5](https://github.com/unlockre/components-library/compare/v20.7.4...v20.7.5) (2023-10-27)


### Bug Fixes

* Fix file manager types ([#282](https://github.com/unlockre/components-library/issues/282)) ([49db223](https://github.com/unlockre/components-library/commit/49db22314bc683502d0815887d44cc3cb6eb400e))

## [20.7.4](https://github.com/unlockre/components-library/compare/v20.7.3...v20.7.4) (2023-10-27)


### Bug Fixes

* Rename file-manager-props ([#281](https://github.com/unlockre/components-library/issues/281)) ([fe3cc11](https://github.com/unlockre/components-library/commit/fe3cc1103f795dc721eac682303cdb639bdbd7ee))

## [20.7.3](https://github.com/unlockre/components-library/compare/v20.7.2...v20.7.3) (2023-10-26)


### Bug Fixes

* Fix virtual table border ([#280](https://github.com/unlockre/components-library/issues/280)) ([27661b0](https://github.com/unlockre/components-library/commit/27661b02bfbe46dc530619384f8cbdf9c9a35adb))

## [20.7.2](https://github.com/unlockre/components-library/compare/v20.7.1...v20.7.2) (2023-10-25)


### Bug Fixes

* Keypilot mobile position ([#279](https://github.com/unlockre/components-library/issues/279)) ([4ea535f](https://github.com/unlockre/components-library/commit/4ea535fbbc9f1318c2d68335af6ca942600872e9))

## [20.7.1](https://github.com/unlockre/components-library/compare/v20.7.0...v20.7.1) (2023-10-25)


### Bug Fixes

* Downgrade pretty-bytes version and fix stuff ([#278](https://github.com/unlockre/components-library/issues/278)) ([e280ded](https://github.com/unlockre/components-library/commit/e280dedc0071eaa365d880564b07f5adca7db24d))

# [20.7.0](https://github.com/unlockre/components-library/compare/v20.6.3...v20.7.0) (2023-10-24)


### Features

* Add file-manager ([#277](https://github.com/unlockre/components-library/issues/277)) ([c3450d2](https://github.com/unlockre/components-library/commit/c3450d2b02f9c2aefcf6b14602cbf9e60ad6a407))

## [20.6.3](https://github.com/unlockre/components-library/compare/v20.6.2...v20.6.3) (2023-10-18)


### Bug Fixes

* Export ShowSnackbarOn type ([#276](https://github.com/unlockre/components-library/issues/276)) ([bbb9091](https://github.com/unlockre/components-library/commit/bbb9091651c2624176507ba51ca54cd3b93c686e))

## [20.6.2](https://github.com/unlockre/components-library/compare/v20.6.1...v20.6.2) (2023-10-12)


### Bug Fixes

* Stop selection checkbox click propagation ([#274](https://github.com/unlockre/components-library/issues/274)) ([0ad296d](https://github.com/unlockre/components-library/commit/0ad296dc80f8ee6a3e36cea58c63d2077bf7ddeb))

## [20.6.1](https://github.com/unlockre/components-library/compare/v20.6.0...v20.6.1) (2023-10-11)


### Bug Fixes

* Update mobile breakpoint value ([#273](https://github.com/unlockre/components-library/issues/273)) ([eecb48e](https://github.com/unlockre/components-library/commit/eecb48e3638517dd0681c6ee6e3308f5dd226495))

# [20.6.0](https://github.com/unlockre/components-library/compare/v20.5.0...v20.6.0) (2023-10-04)


### Features

* Add missing colors ([#272](https://github.com/unlockre/components-library/issues/272)) ([f3681a9](https://github.com/unlockre/components-library/commit/f3681a9134c45b65d002dd3ad037a98c52dd09dc))

# [20.5.0](https://github.com/unlockre/components-library/compare/v20.4.7...v20.5.0) (2023-09-29)


### Bug Fixes

* Fix Keypilot size and positioning for mobile version ([#270](https://github.com/unlockre/components-library/issues/270)) ([226d9df](https://github.com/unlockre/components-library/commit/226d9df4202d44f04193bd1c3efdd0bb41273da5))


### Features

* Add breakpoints and getResponsiveCss utility to theme ([#271](https://github.com/unlockre/components-library/issues/271)) ([8a7344a](https://github.com/unlockre/components-library/commit/8a7344a77a7f2499d9574585e4733a4619c95edd))

## [20.4.7](https://github.com/unlockre/components-library/compare/v20.4.6...v20.4.7) (2023-09-27)


### Bug Fixes

* Fix virtual table border ([#269](https://github.com/unlockre/components-library/issues/269)) ([79078f6](https://github.com/unlockre/components-library/commit/79078f6f5a0a47d5139b3dbc197b1c8999e98523))

## [20.4.6](https://github.com/unlockre/components-library/compare/v20.4.5...v20.4.6) (2023-09-18)


### Bug Fixes

* Add withRoundedBorders prop ([#268](https://github.com/unlockre/components-library/issues/268)) ([d706263](https://github.com/unlockre/components-library/commit/d706263bdccb63d88b458c6deb70345f2893c3e8))

## [20.4.5](https://github.com/unlockre/components-library/compare/v20.4.4...v20.4.5) (2023-09-15)


### Bug Fixes

* Add className to virtualTable ([#267](https://github.com/unlockre/components-library/issues/267)) ([a2e27f4](https://github.com/unlockre/components-library/commit/a2e27f497704a343da81b21f82e011bf8ce936cc))

## [20.4.4](https://github.com/unlockre/components-library/compare/v20.4.3...v20.4.4) (2023-09-14)


### Bug Fixes

* Change sort to extended sort ([#266](https://github.com/unlockre/components-library/issues/266)) ([f1781a6](https://github.com/unlockre/components-library/commit/f1781a648b13be159c98ccf6145ac8008111c963))

## [20.4.3](https://github.com/unlockre/components-library/compare/v20.4.2...v20.4.3) (2023-09-13)


### Bug Fixes

* Add type="button" to Checkbox and RadioButton components ([#265](https://github.com/unlockre/components-library/issues/265)) ([d46755b](https://github.com/unlockre/components-library/commit/d46755b26d1fe7b766177602e35ce51b7588201a))

## [20.4.2](https://github.com/unlockre/components-library/compare/v20.4.1...v20.4.2) (2023-09-12)


### Bug Fixes

* Fix VirtualTable sortFunction and pagination style ([#263](https://github.com/unlockre/components-library/issues/263)) ([ceac13c](https://github.com/unlockre/components-library/commit/ceac13ce66882b7123fe57cd856158c71ce73b75))

## [20.4.1](https://github.com/unlockre/components-library/compare/v20.4.0...v20.4.1) (2023-09-12)


### Bug Fixes

* Export tableSortOrders ([#261](https://github.com/unlockre/components-library/issues/261)) ([65dbe59](https://github.com/unlockre/components-library/commit/65dbe59851bc008076e03edb3894fadf008b440d))
* Rename TableSortOrder to TableSortDirection ([#262](https://github.com/unlockre/components-library/issues/262)) ([e91b779](https://github.com/unlockre/components-library/commit/e91b77904f31fb2b55b83782370ce21f883a048a))

# [20.4.0](https://github.com/unlockre/components-library/compare/v20.3.0...v20.4.0) (2023-09-12)


### Bug Fixes

* Fix virtual table sort function ([#260](https://github.com/unlockre/components-library/issues/260)) ([eccf675](https://github.com/unlockre/components-library/commit/eccf675e6012b8616635da56eac5af43d50a0dc0))


### Features

* Keypilot UI updates ([#257](https://github.com/unlockre/components-library/issues/257)) ([ab9929a](https://github.com/unlockre/components-library/commit/ab9929a08ecee06e3254f4922f0b06e2af3aef4f))

# [20.3.0](https://github.com/unlockre/components-library/compare/v20.2.0...v20.3.0) (2023-09-11)


### Bug Fixes

* Remove padding and add TabLabel ([#259](https://github.com/unlockre/components-library/issues/259)) ([d65d3d9](https://github.com/unlockre/components-library/commit/d65d3d9c77a444f5d8938d9b03dd28bca0bbfe13))


### Features

* Change label prop type in Tab component ([#258](https://github.com/unlockre/components-library/issues/258)) ([d531dc6](https://github.com/unlockre/components-library/commit/d531dc63c1931304ec77f1e19ff4b5646737d4b4))

# [20.2.0](https://github.com/unlockre/components-library/compare/v20.1.2...v20.2.0) (2023-09-05)


### Bug Fixes

* Fix virtualization ([#256](https://github.com/unlockre/components-library/issues/256)) ([ca0f61d](https://github.com/unlockre/components-library/commit/ca0f61dc341528b896f56ff9b99f22df5c5b39b4))


### Features

* Add shouldHide prop to modal ([#255](https://github.com/unlockre/components-library/issues/255)) ([cf77f3f](https://github.com/unlockre/components-library/commit/cf77f3fe35b591fe1b3346b49b6fc96180bf0cc2))

## [20.1.2](https://github.com/unlockre/components-library/compare/v20.1.1...v20.1.2) (2023-09-04)


### Bug Fixes

* Fix PropertyFinder types ([#254](https://github.com/unlockre/components-library/issues/254)) ([1b4690f](https://github.com/unlockre/components-library/commit/1b4690fcb1445706b68e8620d468011059c3fa11))

## [20.1.1](https://github.com/unlockre/components-library/compare/v20.1.0...v20.1.1) (2023-09-04)


### Bug Fixes

* Fix PropertyFinder types and styles ([#253](https://github.com/unlockre/components-library/issues/253)) ([0d27e35](https://github.com/unlockre/components-library/commit/0d27e35b18bcf9ec74a7a7ce36af394d588731b8))

# [20.1.0](https://github.com/unlockre/components-library/compare/v20.0.1...v20.1.0) (2023-08-31)


### Features

* Add Keypilot component ([#247](https://github.com/unlockre/components-library/issues/247)) ([141afae](https://github.com/unlockre/components-library/commit/141afae2ac445cab42bc8cac074c81ff89ddc167))

## [20.0.1](https://github.com/unlockre/components-library/compare/v20.0.0...v20.0.1) (2023-08-31)


### Bug Fixes

* Use title instead of component in stories ([#252](https://github.com/unlockre/components-library/issues/252)) ([859ef10](https://github.com/unlockre/components-library/commit/859ef10db105591edc955e896e598bb196ae3195))

# [20.0.0](https://github.com/unlockre/components-library/compare/v19.8.0...v20.0.0) (2023-08-31)


### Bug Fixes

* Align theme with figma ([#246](https://github.com/unlockre/components-library/issues/246)) ([8a1a571](https://github.com/unlockre/components-library/commit/8a1a57115e8d18a40a4e6a8f0a835de10b19cdb2))
* Fix storybook integration ([#248](https://github.com/unlockre/components-library/issues/248)) ([f89f595](https://github.com/unlockre/components-library/commit/f89f595a3746c498db4a83d65ea2cb387b17f9c1))
* Remove pill text uppercase transformation ([#249](https://github.com/unlockre/components-library/issues/249)) ([9eca869](https://github.com/unlockre/components-library/commit/9eca869d2aed5ea1e4d44e0bb678a39ec4f922b5))


### Features

* Add PropertyFinder component ([#250](https://github.com/unlockre/components-library/issues/250)) ([6b4d3d8](https://github.com/unlockre/components-library/commit/6b4d3d8a2b8db690301aeeac801384b1f0c78452))


### BREAKING CHANGES

* We are no longer transforming the pill text to uppercase

# [19.8.0](https://github.com/unlockre/components-library/compare/v19.7.0...v19.8.0) (2023-08-14)


### Features

* Add new display m typography ([#244](https://github.com/unlockre/components-library/issues/244)) ([f0cbf2d](https://github.com/unlockre/components-library/commit/f0cbf2d35f2b00a592d4adabd7430a6f09dadf3b))

# [19.7.0](https://github.com/unlockre/components-library/compare/v19.6.0...v19.7.0) (2023-08-10)


### Features

* Add virtual table component ([#242](https://github.com/unlockre/components-library/issues/242)) ([8f8603f](https://github.com/unlockre/components-library/commit/8f8603f5e4d3b09bff5ff182c158f5ad4a592c9b))

# [19.6.0](https://github.com/unlockre/components-library/compare/v19.5.0...v19.6.0) (2023-08-09)


### Features

* Add loading dots component ([#243](https://github.com/unlockre/components-library/issues/243)) ([37c190c](https://github.com/unlockre/components-library/commit/37c190c247672b980179f5168a995c34267f9cc3))

# [19.5.0](https://github.com/unlockre/components-library/compare/v19.4.2...v19.5.0) (2023-06-30)


### Features

* Add UserAvatar component ([#240](https://github.com/unlockre/components-library/issues/240)) ([0b14e78](https://github.com/unlockre/components-library/commit/0b14e784d0c3d5c3b9fc6979255d5ded46fc899b))

## [19.4.2](https://github.com/unlockre/components-library/compare/v19.4.1...v19.4.2) (2023-06-23)


### Bug Fixes

* Restore Button component to previous version ([#239](https://github.com/unlockre/components-library/issues/239)) ([163ae76](https://github.com/unlockre/components-library/commit/163ae76297f9d1d2649ab50e98453cb2b71d9907))

## [19.4.1](https://github.com/unlockre/components-library/compare/v19.4.0...v19.4.1) (2023-06-22)


### Bug Fixes

* Add ref prop to Checkbox, MenuItemLink, and TableRowCheckbox ([#233](https://github.com/unlockre/components-library/issues/233)) ([e5ed6c7](https://github.com/unlockre/components-library/commit/e5ed6c7e80b9fc43187c1efaf71198dc1f472fb3))

# [19.4.0](https://github.com/unlockre/components-library/compare/v19.3.1...v19.4.0) (2023-06-15)


### Features

* Add error-info components ([#238](https://github.com/unlockre/components-library/issues/238)) ([622310e](https://github.com/unlockre/components-library/commit/622310ec53282008e9a3cdc70ebf07fc2cc82705))

## [19.3.1](https://github.com/unlockre/components-library/compare/v19.3.0...v19.3.1) (2023-06-14)


### Bug Fixes

* Disable file field entries when FileField is disabled ([#237](https://github.com/unlockre/components-library/issues/237)) ([aef1727](https://github.com/unlockre/components-library/commit/aef17278ce4754ea1042f4577947a2e7ca9dbd8e))

# [19.3.0](https://github.com/unlockre/components-library/compare/v19.2.0...v19.3.0) (2023-06-13)


### Features

* Add gray color 800 variant ([#236](https://github.com/unlockre/components-library/issues/236)) ([57ca94a](https://github.com/unlockre/components-library/commit/57ca94a76b2e0e9024513d14270e20d223a5b373))

# [19.2.0](https://github.com/unlockre/components-library/compare/v19.1.0...v19.2.0) (2023-06-13)


### Features

* Add green color 50 variant ([#235](https://github.com/unlockre/components-library/issues/235)) ([fba2293](https://github.com/unlockre/components-library/commit/fba2293d0f04f757a6e4569e614bb804aa380e8a))

# [19.1.0](https://github.com/unlockre/components-library/compare/v19.0.2...v19.1.0) (2023-06-13)


### Features

* Add lila color and orange1 50 new color ([#234](https://github.com/unlockre/components-library/issues/234)) ([1155bb8](https://github.com/unlockre/components-library/commit/1155bb801d960d6c5693c06a99328d4501fdd8e0))

## [19.0.2](https://github.com/unlockre/components-library/compare/v19.0.1...v19.0.2) (2023-06-12)


### Bug Fixes

* Rename checkbox isSelected prop to isChecked ([#232](https://github.com/unlockre/components-library/issues/232)) ([ffce7d5](https://github.com/unlockre/components-library/commit/ffce7d5914d73b5c6315d859e20506edd5e6f07d))

## [19.0.1](https://github.com/unlockre/components-library/compare/v19.0.0...v19.0.1) (2023-06-09)


### Bug Fixes

* Change getItemColor prop to optional ([#231](https://github.com/unlockre/components-library/issues/231)) ([1a32eaf](https://github.com/unlockre/components-library/commit/1a32eafe8a1701d0b0f9fd23108452d2481b39fb))

# [19.0.0](https://github.com/unlockre/components-library/compare/v18.2.1...v19.0.0) (2023-06-09)


### Features

* Replace checkbox component ([#230](https://github.com/unlockre/components-library/issues/230)) ([d762f29](https://github.com/unlockre/components-library/commit/d762f29572f69d7314b94ad828d89f118f5907f3))


### BREAKING CHANGES

* No more Ref, Rename props (value -> isSelected, indeterminate -> isIndeterminated), No more MUI.

## [18.2.1](https://github.com/unlockre/components-library/compare/v18.2.0...v18.2.1) (2023-06-08)


### Bug Fixes

* Disable dollar sign in CurrencyField ([#229](https://github.com/unlockre/components-library/issues/229)) ([9f3c8f0](https://github.com/unlockre/components-library/commit/9f3c8f036750c2bc61266d324e6de143a4df30c7))

# [18.2.0](https://github.com/unlockre/components-library/compare/v18.1.3...v18.2.0) (2023-05-03)


### Features

* Add type font weight 200 ([#226](https://github.com/unlockre/components-library/issues/226)) ([539d7e8](https://github.com/unlockre/components-library/commit/539d7e8c69ae868d4628eeea947759e36da36199))
* Expose thousand separator ([#225](https://github.com/unlockre/components-library/issues/225)) ([81f0dc3](https://github.com/unlockre/components-library/commit/81f0dc380fdabd292ce07e85fe056d193fd8636f))

## [18.1.3](https://github.com/unlockre/components-library/compare/v18.1.2...v18.1.3) (2023-04-28)


### Bug Fixes

* Adjust footer styles ([#224](https://github.com/unlockre/components-library/issues/224)) ([13b109d](https://github.com/unlockre/components-library/commit/13b109d67a05d0cba30d2a57a2979231933d3663))

## [18.1.2](https://github.com/unlockre/components-library/compare/v18.1.1...v18.1.2) (2023-04-27)


### Bug Fixes

* Expose hideFileRemove FileEntry prop in file fields ([#223](https://github.com/unlockre/components-library/issues/223)) ([bcf922a](https://github.com/unlockre/components-library/commit/bcf922ac4551edb02bf5db4d9db50873c20d6826))

## [18.1.1](https://github.com/unlockre/components-library/compare/v18.1.0...v18.1.1) (2023-04-27)


### Bug Fixes

* Fix render-actions-left prop ([#222](https://github.com/unlockre/components-library/issues/222)) ([a1ba4a9](https://github.com/unlockre/components-library/commit/a1ba4a9a5d40b25150d159f7253c76c1cad4c353))

# [18.1.0](https://github.com/unlockre/components-library/compare/v18.0.0...v18.1.0) (2023-04-27)


### Features

* Add renderActionsLeft FileEntry prop ([#221](https://github.com/unlockre/components-library/issues/221)) ([db363f8](https://github.com/unlockre/components-library/commit/db363f8231aeb80bde9ca2841e7a49004f6cf8da))

# [18.0.0](https://github.com/unlockre/components-library/compare/v17.1.0...v18.0.0) (2023-04-26)


### Features

* Add footerRight Field prop ([#220](https://github.com/unlockre/components-library/issues/220)) ([08911dc](https://github.com/unlockre/components-library/commit/08911dcc7f89a77713dae6eae2a2a0567ca902b9))


### BREAKING CHANGES

* Rename actionButton Field prop to headerRight

# [17.1.0](https://github.com/unlockre/components-library/compare/v17.0.0...v17.1.0) (2023-04-20)


### Features

* Add requestOptions prop to PlacesAutocompleteField ([#219](https://github.com/unlockre/components-library/issues/219)) ([405bf03](https://github.com/unlockre/components-library/commit/405bf03c27cc3145f49117aef7d59dfcbc8457a1))

# [17.0.0](https://github.com/unlockre/components-library/compare/v16.2.3...v17.0.0) (2023-04-19)


### Features

* Add variants to InfoItemList component ([#217](https://github.com/unlockre/components-library/issues/217)) ([344fde1](https://github.com/unlockre/components-library/commit/344fde1020c21d7931e3f4eef9770ca4924127d0))
* Update AutocompleteField look and feel ([#218](https://github.com/unlockre/components-library/issues/218)) ([b48cbc2](https://github.com/unlockre/components-library/commit/b48cbc2f50ff3d0efcf1cc561227114babdd16e9))


### BREAKING CHANGES

* Add InfoItemList component variant prop (mandatory)
* Now AutocompleteField uses TextField component

## [16.2.3](https://github.com/unlockre/components-library/compare/v16.2.2...v16.2.3) (2023-04-14)


### Bug Fixes

* Add position relative style (required by z-index) ([#216](https://github.com/unlockre/components-library/issues/216)) ([284085a](https://github.com/unlockre/components-library/commit/284085ad0de958d224e717dc9fa9eb798a3133f4))

## [16.2.2](https://github.com/unlockre/components-library/compare/v16.2.1...v16.2.2) (2023-04-13)


### Bug Fixes

* Add zIndex style to portal container ([#215](https://github.com/unlockre/components-library/issues/215)) ([2c2a0f0](https://github.com/unlockre/components-library/commit/2c2a0f020a5c0dc202d0d58277080d94ccd46ecd))
* Import hooks from utils-package ([#214](https://github.com/unlockre/components-library/issues/214)) ([81949ea](https://github.com/unlockre/components-library/commit/81949eae76a4e9d24ded2edbbd0b1ab9a46ee8d8))

## [16.2.1](https://github.com/unlockre/components-library/compare/v16.2.0...v16.2.1) (2023-04-11)


### Bug Fixes

* Remove horizontal padding on tableHeaderCell ([#213](https://github.com/unlockre/components-library/issues/213)) ([0292629](https://github.com/unlockre/components-library/commit/0292629efb29c9285bc83ea2d0c7eb40a1197552))

# [16.2.0](https://github.com/unlockre/components-library/compare/v16.1.0...v16.2.0) (2023-04-11)


### Features

* Allow to fix some columns from table-component ([#211](https://github.com/unlockre/components-library/issues/211)) ([3b4c534](https://github.com/unlockre/components-library/commit/3b4c53469d01ad1d4bccce83212385b9683bbaaa))

# [16.1.0](https://github.com/unlockre/components-library/compare/v16.0.0...v16.1.0) (2023-04-10)


### Features

* Add aria-label to TextInput ([#212](https://github.com/unlockre/components-library/issues/212)) ([5f8a570](https://github.com/unlockre/components-library/commit/5f8a570e59febe9a8d2c0b44cf957aef5b2c5936))

# [16.0.0](https://github.com/unlockre/components-library/compare/v15.4.0...v16.0.0) (2023-04-04)


### Features

* Add white and dark variants ([#210](https://github.com/unlockre/components-library/issues/210)) ([cb98634](https://github.com/unlockre/components-library/commit/cb986347f76ed9a6d34b14f550a03839f6b39940))


### BREAKING CHANGES

* Add mandatory prop to Tooltip (variant)

# [15.4.0](https://github.com/unlockre/components-library/compare/v15.3.0...v15.4.0) (2023-03-31)


### Features

* Add table selection checkbox ([#209](https://github.com/unlockre/components-library/issues/209)) ([52ddc42](https://github.com/unlockre/components-library/commit/52ddc42654fed14f5f5ac090dda1b160edb52202))

# [15.3.0](https://github.com/unlockre/components-library/compare/v15.2.0...v15.3.0) (2023-03-29)


### Features

* Provide a way to close itemList in footer ([#208](https://github.com/unlockre/components-library/issues/208)) ([79ac78b](https://github.com/unlockre/components-library/commit/79ac78b181357829c5bf9d0571e8cf8c5433629a))

# [15.2.0](https://github.com/unlockre/components-library/compare/v15.1.0...v15.2.0) (2023-03-28)


### Bug Fixes

* Remove line and leave only one button ([#207](https://github.com/unlockre/components-library/issues/207)) ([acdf3b2](https://github.com/unlockre/components-library/commit/acdf3b23ec374fead3b5cb10387ee6654f192073))


### Features

* Add groups and footer to item lists ([#206](https://github.com/unlockre/components-library/issues/206)) ([7af2e02](https://github.com/unlockre/components-library/commit/7af2e028e10d9b595d7d7ab8d48a2b08b855827a))

# [15.1.0](https://github.com/unlockre/components-library/compare/v15.0.1...v15.1.0) (2023-03-27)


### Features

* Add size prop to breadcrumb list ([#205](https://github.com/unlockre/components-library/issues/205)) ([9abfb75](https://github.com/unlockre/components-library/commit/9abfb757fcdfa90b607fb44d5e84b3eb98134d0e))

## [15.0.1](https://github.com/unlockre/components-library/compare/v15.0.0...v15.0.1) (2023-03-21)


### Bug Fixes

* Fix text wrapping in TextInfoItemValue component ([#204](https://github.com/unlockre/components-library/issues/204)) ([fa61ae2](https://github.com/unlockre/components-library/commit/fa61ae2ef00d8543ae06f2fd556883a31ae5152f))

# [15.0.0](https://github.com/unlockre/components-library/compare/v14.2.0...v15.0.0) (2023-03-20)


### Bug Fixes

* Add type button to tag component ([#201](https://github.com/unlockre/components-library/issues/201)) ([da3a061](https://github.com/unlockre/components-library/commit/da3a061441ad379800123e4ea6bd61522c29494a))
* Set thousandSeparator in NumberField ([#203](https://github.com/unlockre/components-library/issues/203)) ([ec3f619](https://github.com/unlockre/components-library/commit/ec3f619aebfe9ec3c55fd47434c835460bee503e))


### BREAKING CHANGES

* Removed NumberField thousandSeparator prop (now included by default)

# [14.2.0](https://github.com/unlockre/components-library/compare/v14.1.0...v14.2.0) (2023-03-17)


### Features

* Add ChevronButton component ([#198](https://github.com/unlockre/components-library/issues/198)) ([82e96e7](https://github.com/unlockre/components-library/commit/82e96e7b31f59cb3f888d5f28681892d8d4d2a26))
* Add CollapsibleInfoItemRow component and related stuff ([#199](https://github.com/unlockre/components-library/issues/199)) ([e9fd12b](https://github.com/unlockre/components-library/commit/e9fd12ba596237ef65c63f48ed40b8449c3c114a))

# [14.1.0](https://github.com/unlockre/components-library/compare/v14.0.3...v14.1.0) (2023-03-15)


### Features

* Allow to handle row expand toggled ([#197](https://github.com/unlockre/components-library/issues/197)) ([ca648fd](https://github.com/unlockre/components-library/commit/ca648fd1e909b32592654894257c2d67cc8c8152))

## [14.0.3](https://github.com/unlockre/components-library/compare/v14.0.2...v14.0.3) (2023-03-13)


### Bug Fixes

* Allow selectableRowsNoSelectAll prop on table ([#196](https://github.com/unlockre/components-library/issues/196)) ([2534891](https://github.com/unlockre/components-library/commit/2534891ee484d8436ad12fe45d55fc7a9bdc1c3a))

## [14.0.2](https://github.com/unlockre/components-library/compare/v14.0.1...v14.0.2) (2023-03-13)


### Bug Fixes

* Add new colors to theme ([#195](https://github.com/unlockre/components-library/issues/195)) ([36d96f8](https://github.com/unlockre/components-library/commit/36d96f855c230fe7bbab378ba195ef1288335c28))

## [14.0.1](https://github.com/unlockre/components-library/compare/v14.0.0...v14.0.1) (2023-02-28)


### Bug Fixes

* Move union to the value (InfoItem) ([#194](https://github.com/unlockre/components-library/issues/194)) ([3eb8eb9](https://github.com/unlockre/components-library/commit/3eb8eb90fdb043ffc9524555fd4d4b38c0d274b3))

# [14.0.0](https://github.com/unlockre/components-library/compare/v13.7.0...v14.0.0) (2023-02-28)


### Features

* Add InfoItemList component ([#193](https://github.com/unlockre/components-library/issues/193)) ([c3c7994](https://github.com/unlockre/components-library/commit/c3c79946683e10ccf6c5b512132925b038739547))


### BREAKING CHANGES

* Deprecate InfoList in favor of using the new InfoItemList component

# [13.7.0](https://github.com/unlockre/components-library/compare/v13.6.0...v13.7.0) (2023-02-22)


### Bug Fixes

* Improve floating ui usage ([#192](https://github.com/unlockre/components-library/issues/192)) ([bab941f](https://github.com/unlockre/components-library/commit/bab941f16f358b924afad4478462cde70d5087a8))


### Features

* Add data-testid prop to modal ([#190](https://github.com/unlockre/components-library/issues/190)) ([49ce3c6](https://github.com/unlockre/components-library/commit/49ce3c6e0f8132aee833aec372c07620e2f89d2f))

# [13.6.0](https://github.com/unlockre/components-library/compare/v13.5.0...v13.6.0) (2023-02-22)


### Features

* Allow passing strategy to use-date-picker hook ([#191](https://github.com/unlockre/components-library/issues/191)) ([213a9f6](https://github.com/unlockre/components-library/commit/213a9f632a8322b80f447b606d011cc71b85d310))

# [13.5.0](https://github.com/unlockre/components-library/compare/v13.4.2...v13.5.0) (2023-02-10)


### Features

* Create button-dropdown component ([#189](https://github.com/unlockre/components-library/issues/189)) ([dc128ff](https://github.com/unlockre/components-library/commit/dc128ff3ffe24a45d15ee226c880acdfa464a6cd))

## [13.4.2](https://github.com/unlockre/components-library/compare/v13.4.1...v13.4.2) (2023-02-07)


### Bug Fixes

* Add height variant property ([#188](https://github.com/unlockre/components-library/issues/188)) ([26c9b8f](https://github.com/unlockre/components-library/commit/26c9b8f11f3bd4fc716e7fa8024c7b2130118e9b))

## [13.4.1](https://github.com/unlockre/components-library/compare/v13.4.0...v13.4.1) (2023-02-06)


### Bug Fixes

* Add default color ([#187](https://github.com/unlockre/components-library/issues/187)) ([069d904](https://github.com/unlockre/components-library/commit/069d904bc632b33f25e4f735252ccefaa24109a4))

# [13.4.0](https://github.com/unlockre/components-library/compare/v13.3.1...v13.4.0) (2023-02-06)


### Features

* Add uploadableFileSchema ([#186](https://github.com/unlockre/components-library/issues/186)) ([9530e57](https://github.com/unlockre/components-library/commit/9530e57e7a7cbe4ee603e5d64476f2ab3b228230))

## [13.3.1](https://github.com/unlockre/components-library/compare/v13.3.0...v13.3.1) (2023-02-06)


### Bug Fixes

* Fix radio-button ([#183](https://github.com/unlockre/components-library/issues/183)) ([6ab56c7](https://github.com/unlockre/components-library/commit/6ab56c7cf69b5b6c49521d64363ddae00960d094))

# [13.3.0](https://github.com/unlockre/components-library/compare/v13.2.0...v13.3.0) (2023-02-06)


### Features

* Add showTooltip and hideTooltip functions to useTooltip hook ([#181](https://github.com/unlockre/components-library/issues/181)) ([2f8cc1b](https://github.com/unlockre/components-library/commit/2f8cc1beafa754347407dfdd8ec7f3df7f074f1d))
* Expose isRequired prop on date-field ([#184](https://github.com/unlockre/components-library/issues/184)) ([30a5d75](https://github.com/unlockre/components-library/commit/30a5d75a3bfa3043202d492d3b63f89c3efbac1e))

# [13.2.0](https://github.com/unlockre/components-library/compare/v13.1.1...v13.2.0) (2023-02-03)


### Features

* Add radio-button component ([#182](https://github.com/unlockre/components-library/issues/182)) ([2346ace](https://github.com/unlockre/components-library/commit/2346aced4b4b1c8958a271f2d4bf274fb6b818dc))
* Add role to snackbar for tests e2e ([#180](https://github.com/unlockre/components-library/issues/180)) ([c77bc42](https://github.com/unlockre/components-library/commit/c77bc42c91177c6ecbbcb65ee36a67be8c3ff38d))

## [13.1.1](https://github.com/unlockre/components-library/compare/v13.1.0...v13.1.1) (2023-01-18)


### Bug Fixes

* Use current items on useToggleButton hook ([#179](https://github.com/unlockre/components-library/issues/179)) ([df052b8](https://github.com/unlockre/components-library/commit/df052b83a623fbb41de73b2fb27e1bb7e12574f1))

# [13.1.0](https://github.com/unlockre/components-library/compare/v13.0.0...v13.1.0) (2023-01-13)


### Features

* Add blue 950 to color palette ([#178](https://github.com/unlockre/components-library/issues/178)) ([bc5ed9d](https://github.com/unlockre/components-library/commit/bc5ed9d1e5c7b9fd0458c9725d7164e83528d865))

# [13.0.0](https://github.com/unlockre/components-library/compare/v12.6.0...v13.0.0) (2022-12-29)


### Features

* Extract state management to a hook in tab components ([#177](https://github.com/unlockre/components-library/issues/177)) ([12d1c5a](https://github.com/unlockre/components-library/commit/12d1c5ab4ac87a3e0ca0b5d97edf9c388f8624f7))


### BREAKING CHANGES

* Extract state management to a hook in tab components

# [12.6.0](https://github.com/unlockre/components-library/compare/v12.5.0...v12.6.0) (2022-12-21)


### Features

* Add gray and information variant ([#176](https://github.com/unlockre/components-library/issues/176)) ([f8d2fb2](https://github.com/unlockre/components-library/commit/f8d2fb22f968742deccd011ff55341c6662cc779))

# [12.5.0](https://github.com/unlockre/components-library/compare/v12.4.1...v12.5.0) (2022-12-13)


### Features

* Add link component ([#175](https://github.com/unlockre/components-library/issues/175)) ([ab8f374](https://github.com/unlockre/components-library/commit/ab8f3740a57a2f75fa1357ce6f538384ddf44f8f))

## [12.4.1](https://github.com/unlockre/components-library/compare/v12.4.0...v12.4.1) (2022-12-13)


### Bug Fixes

* Add missing style to NumberRangeField component ([#174](https://github.com/unlockre/components-library/issues/174)) ([0c5f7be](https://github.com/unlockre/components-library/commit/0c5f7be746f67f073593089cdee577921e083448))

# [12.4.0](https://github.com/unlockre/components-library/compare/v12.3.1...v12.4.0) (2022-12-12)


### Bug Fixes

* Fix AutocompleteField Props type ([#172](https://github.com/unlockre/components-library/issues/172)) ([2b20070](https://github.com/unlockre/components-library/commit/2b20070c660a3d41a885709e1773ad040ac58153))


### Features

* Create number range field component ([#173](https://github.com/unlockre/components-library/issues/173)) ([10c7b35](https://github.com/unlockre/components-library/commit/10c7b35180a9387a0877d177e53d7fa5fc7c6083))

## [12.3.1](https://github.com/unlockre/components-library/compare/v12.3.0...v12.3.1) (2022-11-18)


### Bug Fixes

* Pill dropdown height and icon size ([#170](https://github.com/unlockre/components-library/issues/170)) ([aca241f](https://github.com/unlockre/components-library/commit/aca241f20a67d850f3503b8eb01e5666bef6cd4a))

# [12.3.0](https://github.com/unlockre/components-library/compare/v12.2.0...v12.3.0) (2022-11-08)


### Bug Fixes

* Remove checkbox item ([#169](https://github.com/unlockre/components-library/issues/169)) ([d62da4b](https://github.com/unlockre/components-library/commit/d62da4b02194ca340afa03cc4e1f688f4839dcbf))


### Features

* Add InfoList component and related stuff ([#168](https://github.com/unlockre/components-library/issues/168)) ([68236b9](https://github.com/unlockre/components-library/commit/68236b9921a3d68e06412b80d653878daf8d9f10))

# [12.2.0](https://github.com/unlockre/components-library/compare/v12.1.0...v12.2.0) (2022-11-07)


### Features

* Add DateRangeField component ([#166](https://github.com/unlockre/components-library/issues/166)) ([e2b1c3a](https://github.com/unlockre/components-library/commit/e2b1c3ae5721e525a5b7dafe49eaedeeff9b9736))

# [12.1.0](https://github.com/unlockre/components-library/compare/v12.0.0...v12.1.0) (2022-11-04)


### Features

* Add onTabSelect prop to TabSelectionProvider ([#167](https://github.com/unlockre/components-library/issues/167)) ([9dd7cbe](https://github.com/unlockre/components-library/commit/9dd7cbe7caa5a8d537d803731f2662176ead0e9e))

# [12.0.0](https://github.com/unlockre/components-library/compare/v11.8.1...v12.0.0) (2022-11-03)


### Bug Fixes

* Fix checkbox-group direction ([#164](https://github.com/unlockre/components-library/issues/164)) ([35815df](https://github.com/unlockre/components-library/commit/35815dff7e92412f0abb0d634289d2c71d2f1253))


### Features

* Simplify tabs rendering and logic (TabSelectionContext) ([#163](https://github.com/unlockre/components-library/issues/163)) ([73f0ad2](https://github.com/unlockre/components-library/commit/73f0ad2875143b77102e5a7f1be16abf831bbe2f))


### BREAKING CHANGES

* Remove unnecessary props and move the logic to a context provider (TabSelectionContext)

## [11.8.1](https://github.com/unlockre/components-library/compare/v11.8.0...v11.8.1) (2022-11-03)


### Bug Fixes

* Fix useSnackbar result type ([#165](https://github.com/unlockre/components-library/issues/165)) ([f582f8e](https://github.com/unlockre/components-library/commit/f582f8e64bb7a7f7a89f22d4eb9b8fa67a30be2a))

# [11.8.0](https://github.com/unlockre/components-library/compare/v11.7.0...v11.8.0) (2022-11-03)


### Features

* Add leftElement prop to pill component ([#162](https://github.com/unlockre/components-library/issues/162)) ([cb5afda](https://github.com/unlockre/components-library/commit/cb5afda7d2db93171004f5ab8d275781c557b612))
* Create shadowedScroll component ([#161](https://github.com/unlockre/components-library/issues/161)) ([13b8089](https://github.com/unlockre/components-library/commit/13b8089713e354f5cc48a130025783992a3e87a8))

# [11.7.0](https://github.com/unlockre/components-library/compare/v11.6.1...v11.7.0) (2022-11-02)


### Features

* Add checkbox-group ([#159](https://github.com/unlockre/components-library/issues/159)) ([ce53835](https://github.com/unlockre/components-library/commit/ce538351fa59b457a0fa89bc16d4a12d98af7983))
* Add showSnackbarOn to useSnackbar and export snackbarProps ([#157](https://github.com/unlockre/components-library/issues/157)) ([5d59f20](https://github.com/unlockre/components-library/commit/5d59f20065f3bbdc12551b0106114a015d6dba61))
* Create drawer component ([#160](https://github.com/unlockre/components-library/issues/160)) ([d900d43](https://github.com/unlockre/components-library/commit/d900d433fbacdf86b4d9e761f7df996e84f3a1e3))

## [11.6.1](https://github.com/unlockre/components-library/compare/v11.6.0...v11.6.1) (2022-10-28)


### Bug Fixes

* Add missing button type to PasswordVisibilityButton ([#158](https://github.com/unlockre/components-library/issues/158)) ([36069bd](https://github.com/unlockre/components-library/commit/36069bda14cf83ef89679f5d9b1470858b69163c))

# [11.6.0](https://github.com/unlockre/components-library/compare/v11.5.0...v11.6.0) (2022-10-28)


### Features

* Add useSnackbar hook ([#156](https://github.com/unlockre/components-library/issues/156)) ([1436c98](https://github.com/unlockre/components-library/commit/1436c9854aef17800e0c5b3660f7a171fcf6c04f))

# [11.5.0](https://github.com/unlockre/components-library/compare/v11.4.1...v11.5.0) (2022-10-27)


### Features

* Add PasswordField component and related stuff ([#155](https://github.com/unlockre/components-library/issues/155)) ([1b5ef5c](https://github.com/unlockre/components-library/commit/1b5ef5c302a6e103d7f75f8822c5251695d1cf21))

## [11.4.1](https://github.com/unlockre/components-library/compare/v11.4.0...v11.4.1) (2022-10-25)


### Bug Fixes

* Remove unnecessary style import ([#154](https://github.com/unlockre/components-library/issues/154)) ([fcf8590](https://github.com/unlockre/components-library/commit/fcf8590f7220836639f2347314a44c26f5f2d896))

# [11.4.0](https://github.com/unlockre/components-library/compare/v11.3.0...v11.4.0) (2022-10-24)


### Features

* Add blue 450 to palette ([#152](https://github.com/unlockre/components-library/issues/152)) ([0ef0969](https://github.com/unlockre/components-library/commit/0ef0969faf95aba0a366c19ee3ed9fadec9f6659))
* Add lightBlue pill variant ([#153](https://github.com/unlockre/components-library/issues/153)) ([b2f4ca0](https://github.com/unlockre/components-library/commit/b2f4ca09db48d0db0ed58c70368dcb3bb64116da))

# [11.3.0](https://github.com/unlockre/components-library/compare/v11.2.1...v11.3.0) (2022-10-24)


### Bug Fixes

* Add variant prop on Tag component ([#149](https://github.com/unlockre/components-library/issues/149)) ([556d27f](https://github.com/unlockre/components-library/commit/556d27f2675532306855839f7bde42aeede37bc4))


### Features

* Add Menu and related stuff ([#151](https://github.com/unlockre/components-library/issues/151)) ([1391ced](https://github.com/unlockre/components-library/commit/1391ced920291eef939e9c59aba63e9fc6491b04))
* Add UnstyledAnchor component ([#150](https://github.com/unlockre/components-library/issues/150)) ([939fbc7](https://github.com/unlockre/components-library/commit/939fbc7c4ace598c6c2e5aa7269a06eda9ddaeab))

## [11.2.1](https://github.com/unlockre/components-library/compare/v11.2.0...v11.2.1) (2022-10-20)


### Bug Fixes

* Expose onChangePage prop on Table component ([#148](https://github.com/unlockre/components-library/issues/148)) ([b6ac406](https://github.com/unlockre/components-library/commit/b6ac40670ba8259588c77a6f669db41ddc559c8b))

# [11.2.0](https://github.com/unlockre/components-library/compare/v11.1.0...v11.2.0) (2022-10-18)


### Features

* Add new pill variants ([#146](https://github.com/unlockre/components-library/issues/146)) ([3014963](https://github.com/unlockre/components-library/commit/30149633edbb432c2cd87693e6a2d5a9da1556c8))
* Use typography styles in theme ([#147](https://github.com/unlockre/components-library/issues/147)) ([611af8d](https://github.com/unlockre/components-library/commit/611af8df167f0ee70f888266dd7df5be4d40f00d))

# [11.1.0](https://github.com/unlockre/components-library/compare/v11.0.0...v11.1.0) (2022-10-13)


### Features

* Add TagsField component ([#145](https://github.com/unlockre/components-library/issues/145)) ([62a5ebb](https://github.com/unlockre/components-library/commit/62a5ebbbd94f3cc11a7d22e6d5ab1d49d98179a3))

# [11.0.0](https://github.com/unlockre/components-library/compare/v10.4.4...v11.0.0) (2022-10-06)


### Features

* Support removing items on toggle-button-group ([#144](https://github.com/unlockre/components-library/issues/144)) ([f35155d](https://github.com/unlockre/components-library/commit/f35155d92a25e15b7b7d6d65425a6a1d806bf1d1))


### BREAKING CHANGES

* Remove renderToggleButton function and rename some props.

## [10.4.4](https://github.com/unlockre/components-library/compare/v10.4.3...v10.4.4) (2022-10-04)


### Bug Fixes

* Add isNotClosable prop to modal ([#142](https://github.com/unlockre/components-library/issues/142)) ([7a25420](https://github.com/unlockre/components-library/commit/7a25420da3c5fa65ed94e58743ebc3f250c761d6))
* Export snackbar variant type ([#141](https://github.com/unlockre/components-library/issues/141)) ([ef0075a](https://github.com/unlockre/components-library/commit/ef0075abe8d7eeb4cdee325b932f11afaa052fd3))

## [10.4.3](https://github.com/unlockre/components-library/compare/v10.4.2...v10.4.3) (2022-09-30)


### Bug Fixes

* Add flex-wrap style to toggle-button-group ([#140](https://github.com/unlockre/components-library/issues/140)) ([62189b1](https://github.com/unlockre/components-library/commit/62189b1dd7dcb3a54473af4f6e43cc1d5a24ad51))

## [10.4.2](https://github.com/unlockre/components-library/compare/v10.4.1...v10.4.2) (2022-09-30)


### Bug Fixes

* Add type button to ToggleButton ([#139](https://github.com/unlockre/components-library/issues/139)) ([1f793d2](https://github.com/unlockre/components-library/commit/1f793d239551e145c602d5b54d3235de1e428f15))

## [10.4.1](https://github.com/unlockre/components-library/compare/v10.4.0...v10.4.1) (2022-09-28)


### Bug Fixes

* Add nowrap to tab title ([#138](https://github.com/unlockre/components-library/issues/138)) ([c5af7c4](https://github.com/unlockre/components-library/commit/c5af7c4edeaa58df963567cdd7becffa8fde1ae0))

# [10.4.0](https://github.com/unlockre/components-library/compare/v10.3.2...v10.4.0) (2022-09-27)


### Features

* Add MultiFileField component ([#137](https://github.com/unlockre/components-library/issues/137)) ([637416a](https://github.com/unlockre/components-library/commit/637416a94cf73169cf01eb9720735e7743a499f8))

## [10.3.2](https://github.com/unlockre/components-library/compare/v10.3.1...v10.3.2) (2022-09-21)


### Bug Fixes

* Fix SingleFileField not opening the browser file picker ([#136](https://github.com/unlockre/components-library/issues/136)) ([323cb83](https://github.com/unlockre/components-library/commit/323cb83d0863e5f84a64de33a2b3a6671f8fb639))

## [10.3.1](https://github.com/unlockre/components-library/compare/v10.3.0...v10.3.1) (2022-09-20)


### Bug Fixes

* Prefer file extension for file type ([#135](https://github.com/unlockre/components-library/issues/135)) ([3f72d6f](https://github.com/unlockre/components-library/commit/3f72d6f50dcef5a0410f7704243129365b098be0))

# [10.3.0](https://github.com/unlockre/components-library/compare/v10.2.1...v10.3.0) (2022-09-20)


### Features

* Add blue variant and nor border variant to table ([#134](https://github.com/unlockre/components-library/issues/134)) ([5028ffa](https://github.com/unlockre/components-library/commit/5028ffaa0b44ae4889b0943d819fa98d29c58fa8))

## [10.2.1](https://github.com/unlockre/components-library/compare/v10.2.0...v10.2.1) (2022-09-16)


### Bug Fixes

* Correct min max on counter field ([#133](https://github.com/unlockre/components-library/issues/133)) ([1fda044](https://github.com/unlockre/components-library/commit/1fda044a326dd2bf24ee8500f6973d5e2bce1c57))

# [10.2.0](https://github.com/unlockre/components-library/compare/v10.1.3...v10.2.0) (2022-09-14)


### Bug Fixes

* Fix message information variant background ([#130](https://github.com/unlockre/components-library/issues/130)) ([9ef26b5](https://github.com/unlockre/components-library/commit/9ef26b522cd034b893ddbb196cccd281f139a3c7))


### Features

* Table improvements ([#129](https://github.com/unlockre/components-library/issues/129)) ([f3f8c12](https://github.com/unlockre/components-library/commit/f3f8c1297c181d780804ce753ccea08dcc4b1b42))

## [10.1.3](https://github.com/unlockre/components-library/compare/v10.1.2...v10.1.3) (2022-09-06)


### Bug Fixes

* Move ref to an internal container (remove children function) ([#126](https://github.com/unlockre/components-library/issues/126)) ([e388cd7](https://github.com/unlockre/components-library/commit/e388cd73f2f071aabbdabf38d73a6c27c664e3a5))

## [10.1.2](https://github.com/unlockre/components-library/compare/v10.1.1...v10.1.2) (2022-09-06)


### Bug Fixes

* Wait for steps to be ready ([#125](https://github.com/unlockre/components-library/issues/125)) ([768c8e5](https://github.com/unlockre/components-library/commit/768c8e52c5fbb6b2f0703ccf4c898554a1cd7154))

## [10.1.1](https://github.com/unlockre/components-library/compare/v10.1.0...v10.1.1) (2022-09-06)


### Bug Fixes

* Change fill property for stroke property ([#124](https://github.com/unlockre/components-library/issues/124)) ([31c40f5](https://github.com/unlockre/components-library/commit/31c40f505f8312a0f7a20a72207198a580f293a9))
* Correct counter field styles ([#123](https://github.com/unlockre/components-library/issues/123)) ([fd93f9e](https://github.com/unlockre/components-library/commit/fd93f9e4c4082672ece6e9adbd3dfa3e43ed5c57))

# [10.1.0](https://github.com/unlockre/components-library/compare/v10.0.0...v10.1.0) (2022-09-06)


### Features

* Add CounterField component ([#120](https://github.com/unlockre/components-library/issues/120)) ([1020248](https://github.com/unlockre/components-library/commit/1020248bc22e35409d80d8acf78c527c3d05cabe))
* Add SingleFileField component ([#122](https://github.com/unlockre/components-library/issues/122)) ([2330d9e](https://github.com/unlockre/components-library/commit/2330d9e890c1b0bf2ab7cd1e8dc0cb55529601a6))

# [10.0.0](https://github.com/unlockre/components-library/compare/v9.1.0...v10.0.0) (2022-09-05)


### Features

* Add xlarge button size ([#121](https://github.com/unlockre/components-library/issues/121)) ([bf63ddc](https://github.com/unlockre/components-library/commit/bf63ddcf67bf79da4813be267534f51d5013812e))


### BREAKING CHANGES

* Add xlarge button size and change large sizes

# [9.1.0](https://github.com/unlockre/components-library/compare/v9.0.0...v9.1.0) (2022-09-01)


### Bug Fixes

* Align TabList children to the bottom ([#115](https://github.com/unlockre/components-library/issues/115)) ([715b0dd](https://github.com/unlockre/components-library/commit/715b0ddd30b027e363983fbe2336040887d8fdea))
* Changing size of text used for Tab component - New Value 's' (14px) ([#110](https://github.com/unlockre/components-library/issues/110)) ([2e1d255](https://github.com/unlockre/components-library/commit/2e1d255d54f5421703a2480ffd8f971f546c390a))
* Improve Breadcrumbs API and tests ([#118](https://github.com/unlockre/components-library/issues/118)) ([64eb0f6](https://github.com/unlockre/components-library/commit/64eb0f63a6f9b8d45d179625830618f1ae0da281))
* Remove background to transparent variant button ([#114](https://github.com/unlockre/components-library/issues/114)) ([9886bd7](https://github.com/unlockre/components-library/commit/9886bd73f008db9fe3fc644e822a613b8a33113e))


### Features

* Add BreadcrumbList component ([#116](https://github.com/unlockre/components-library/issues/116)) ([79330f8](https://github.com/unlockre/components-library/commit/79330f8bc0c91dc7dd80c0432733dfb145f37d92))
* Add onCenterUpdate to MapView ([#113](https://github.com/unlockre/components-library/issues/113)) ([1c944dc](https://github.com/unlockre/components-library/commit/1c944dc5f233bfc33c78ebeecad0a5ccd36ac227))

# [9.0.0](https://github.com/unlockre/components-library/compare/v8.10.2...v9.0.0) (2022-08-26)


### Bug Fixes

* Adjust buttons typography ([#111](https://github.com/unlockre/components-library/issues/111)) ([48a0005](https://github.com/unlockre/components-library/commit/48a00059c21191a6759e1a1962cddeee055e0558))
* Set fixed height ([#112](https://github.com/unlockre/components-library/issues/112)) ([f292ba2](https://github.com/unlockre/components-library/commit/f292ba2df3b18a66f4474f3a99d7167165faa45d))


### BREAKING CHANGES

* Button no longer has margin: 0 auto.

## [8.10.2](https://github.com/unlockre/components-library/compare/v8.10.1...v8.10.2) (2022-08-23)


### Bug Fixes

* Add z-index to snackbar ([#109](https://github.com/unlockre/components-library/issues/109)) ([1952f8c](https://github.com/unlockre/components-library/commit/1952f8c902801b0a51665bc33d5498bda068ac10))

## [8.10.1](https://github.com/unlockre/components-library/compare/v8.10.0...v8.10.1) (2022-08-19)


### Bug Fixes

* Fix ToggleButtonGroup and ToggleButton styles ([#108](https://github.com/unlockre/components-library/issues/108)) ([e08582e](https://github.com/unlockre/components-library/commit/e08582e7b5d7128d86745dc88d85db940416f601))

# [8.10.0](https://github.com/unlockre/components-library/compare/v8.9.3...v8.10.0) (2022-08-17)


### Features

* Add ToggleButtonGroup and ToggleButton components ([#106](https://github.com/unlockre/components-library/issues/106)) ([633ba85](https://github.com/unlockre/components-library/commit/633ba85d241f7592f9203b12a9d58da719a2dc48))

## [8.9.3](https://github.com/unlockre/components-library/compare/v8.9.2...v8.9.3) (2022-08-16)


### Bug Fixes

* Remove unnecessary props from tree select field ([#107](https://github.com/unlockre/components-library/issues/107)) ([6a1298e](https://github.com/unlockre/components-library/commit/6a1298e1e49e1527e1d883fedcd2ef7be7599416))

## [8.9.2](https://github.com/unlockre/components-library/compare/v8.9.1...v8.9.2) (2022-08-12)


### Bug Fixes

* Use onClickOutsideModal everywhere ([#105](https://github.com/unlockre/components-library/issues/105)) ([ab908b0](https://github.com/unlockre/components-library/commit/ab908b0f9e4551bee82efb1dd9489fc8aabe29d7))

## [8.9.1](https://github.com/unlockre/components-library/compare/v8.9.0...v8.9.1) (2022-08-11)


### Bug Fixes

* Add disabled by variant button styles ([#104](https://github.com/unlockre/components-library/issues/104)) ([8471b87](https://github.com/unlockre/components-library/commit/8471b872f9b8f890d6bbf9e90eb0d4c0b416d080))

# [8.9.0](https://github.com/unlockre/components-library/compare/v8.8.3...v8.9.0) (2022-08-11)


### Bug Fixes

* Add containers to side elements ([#103](https://github.com/unlockre/components-library/issues/103)) ([0a7be34](https://github.com/unlockre/components-library/commit/0a7be34aef4b74d908bae8c8a62120acfa8881a0))


### Features

* Add left and right elements to modal ([#102](https://github.com/unlockre/components-library/issues/102)) ([0ab0f32](https://github.com/unlockre/components-library/commit/0ab0f3268780abfb9176a6f72e3d179f10e7394e))

## [8.8.3](https://github.com/unlockre/components-library/compare/v8.8.2...v8.8.3) (2022-08-10)


### Bug Fixes

* Fix field styles and behavior ([#101](https://github.com/unlockre/components-library/issues/101)) ([5a03160](https://github.com/unlockre/components-library/commit/5a0316066c3488b8072ac561de28a50abcd6d183))

## [8.8.2](https://github.com/unlockre/components-library/compare/v8.8.1...v8.8.2) (2022-08-09)


### Bug Fixes

* Align currency-field with design ([#100](https://github.com/unlockre/components-library/issues/100)) ([1cd90f6](https://github.com/unlockre/components-library/commit/1cd90f642f0f9252fe1c50d9bf113f66bf8cf197))

## [8.8.1](https://github.com/unlockre/components-library/compare/v8.8.0...v8.8.1) (2022-08-05)


### Bug Fixes

* Add z-index to modal ([#99](https://github.com/unlockre/components-library/issues/99)) ([9c1a9d5](https://github.com/unlockre/components-library/commit/9c1a9d5a147c0c70c329df9cffcf08f6acb9409b))

# [8.8.0](https://github.com/unlockre/components-library/compare/v8.7.1...v8.8.0) (2022-08-05)


### Bug Fixes

* Fix button icons theme usage ([#98](https://github.com/unlockre/components-library/issues/98)) ([da4b46c](https://github.com/unlockre/components-library/commit/da4b46c032cf75b7cbf1b6752c4cfc85348d13a5))


### Features

* Add TreeSelectField ([#96](https://github.com/unlockre/components-library/issues/96)) ([1d6dee1](https://github.com/unlockre/components-library/commit/1d6dee17edbf083be45e693a6d7c0b1513cfc678))

## [8.7.1](https://github.com/unlockre/components-library/compare/v8.7.0...v8.7.1) (2022-08-01)


### Bug Fixes

* Add z-index and sort css alphabetically ([#97](https://github.com/unlockre/components-library/issues/97)) ([16f4c8b](https://github.com/unlockre/components-library/commit/16f4c8b86a9044a0273847307a03da72a7cc8e15))

# [8.7.0](https://github.com/unlockre/components-library/compare/v8.6.0...v8.7.0) (2022-07-26)


### Features

* Add Button with variants ([#95](https://github.com/unlockre/components-library/issues/95)) ([7a6b7f7](https://github.com/unlockre/components-library/commit/7a6b7f7fd9c08f314f2e16a53315b14601973295))
* Add DatePicker component ([#93](https://github.com/unlockre/components-library/issues/93)) ([311e0d3](https://github.com/unlockre/components-library/commit/311e0d337c552313d2fac7b6060b9fb4fe86d38a))

# [8.6.0](https://github.com/unlockre/components-library/compare/v8.5.0...v8.6.0) (2022-07-25)


### Features

*  Add progressComponent to Table and fix PillDropdown ([#94](https://github.com/unlockre/components-library/issues/94)) ([d5b36ed](https://github.com/unlockre/components-library/commit/d5b36ed725cd2dd12f148030024922f840865933))

# [8.5.0](https://github.com/unlockre/components-library/compare/v8.4.0...v8.5.0) (2022-07-20)


### Features

* Improve table component and create spinner component ([#92](https://github.com/unlockre/components-library/issues/92)) ([50d2f75](https://github.com/unlockre/components-library/commit/50d2f75fa41130c1560f471ef0e322ca59a686df))

# [8.4.0](https://github.com/unlockre/components-library/compare/v8.3.3...v8.4.0) (2022-07-18)


### Bug Fixes

* Add missing type="button" attr to ItemEntryContainer button ([#87](https://github.com/unlockre/components-library/issues/87)) ([130eed9](https://github.com/unlockre/components-library/commit/130eed96795ab66a617127a60088badeaf9139f3))


### Features

* Add new table component ([#86](https://github.com/unlockre/components-library/issues/86)) ([4e84031](https://github.com/unlockre/components-library/commit/4e84031ca919c16be704ab47a0614fb7f23f22aa))

## [8.3.3](https://github.com/unlockre/components-library/compare/v8.3.2...v8.3.3) (2022-07-13)


### Bug Fixes

* Fix disabled FieldHeader style ([#85](https://github.com/unlockre/components-library/issues/85)) ([f13814f](https://github.com/unlockre/components-library/commit/f13814fdbbee758be6bc6826b2c1cb1e671a5645))

## [8.3.2](https://github.com/unlockre/components-library/compare/v8.3.1...v8.3.2) (2022-07-13)


### Bug Fixes

* Improve SelectField keyboard control ([#84](https://github.com/unlockre/components-library/issues/84)) ([ea686ec](https://github.com/unlockre/components-library/commit/ea686ec966a1f785e8c494df3d3288432225c88a))

## [8.3.1](https://github.com/unlockre/components-library/compare/v8.3.0...v8.3.1) (2022-07-12)


### Bug Fixes

* Fix SelectField (Single/Multi) issues ([#83](https://github.com/unlockre/components-library/issues/83)) ([6c161e3](https://github.com/unlockre/components-library/commit/6c161e3024cfb1f187b99bb69ae889d27232e33a))

# [8.3.0](https://github.com/unlockre/components-library/compare/v8.2.1...v8.3.0) (2022-07-11)


### Features

* Improve (Single|Multi)SelectField ([#82](https://github.com/unlockre/components-library/issues/82)) ([269b8c4](https://github.com/unlockre/components-library/commit/269b8c49ad8885c02e5a570f7d795ea2584046be))

## [8.2.1](https://github.com/unlockre/components-library/compare/v8.2.0...v8.2.1) (2022-06-30)


### Bug Fixes

* Fix bug on modal event propagation ([89ad4ed](https://github.com/unlockre/components-library/commit/89ad4ed15e76902b19a757c86b6c16d0044022f9))

# [8.2.0](https://github.com/unlockre/components-library/compare/v8.1.1...v8.2.0) (2022-06-29)


### Features

* Add walkthrough component ([#80](https://github.com/unlockre/components-library/issues/80)) ([f40ba69](https://github.com/unlockre/components-library/commit/f40ba699481cb87f9b8ab568b8a9a418360b50e2))

## [8.1.1](https://github.com/unlockre/components-library/compare/v8.1.0...v8.1.1) (2022-06-28)


### Bug Fixes

* Add BaseButton type casting ([#79](https://github.com/unlockre/components-library/issues/79)) ([e51dd48](https://github.com/unlockre/components-library/commit/e51dd48e0daf4e89ff1ff94e652e67836077ebd9))

# [8.1.0](https://github.com/unlockre/components-library/compare/v8.0.0...v8.1.0) (2022-06-28)


### Features

* Implement error icon on text field component ([#78](https://github.com/unlockre/components-library/issues/78)) ([d8fe20c](https://github.com/unlockre/components-library/commit/d8fe20c6460cf9ae95d61eddafd8a99e14668742))
* Implement message component ([#76](https://github.com/unlockre/components-library/issues/76)) ([9bfc660](https://github.com/unlockre/components-library/commit/9bfc6606976306e3423f8acdd10c82c691c090d8))
* Implement phone number field component ([#77](https://github.com/unlockre/components-library/issues/77)) ([445170c](https://github.com/unlockre/components-library/commit/445170ce35434d9cd7ac3c2b402f2ff32fefcce9))

# [8.0.0](https://github.com/unlockre/components-library/compare/v7.2.0...v8.0.0) (2022-06-27)


### Bug Fixes

* Update styles at pill & pill-dropdown ([#75](https://github.com/unlockre/components-library/issues/75)) ([1d74482](https://github.com/unlockre/components-library/commit/1d7448205a0058d469c4232b050cf8fd2ceb4df5))


### BREAKING CHANGES

* Now Pill and PillDrown components expect a type prop and a new inverted variant

# [7.2.0](https://github.com/unlockre/components-library/compare/v7.1.0...v7.2.0) (2022-06-23)


### Features

* Add theme getColorByAlias utility ([#74](https://github.com/unlockre/components-library/issues/74)) ([d981b25](https://github.com/unlockre/components-library/commit/d981b25b9c242b51ac4c4ba860de366c354b71b4))

# [7.1.0](https://github.com/unlockre/components-library/compare/v7.0.0...v7.1.0) (2022-06-21)


### Features

* Add ErrorBoundary component ([#73](https://github.com/unlockre/components-library/issues/73)) ([c8cc6a5](https://github.com/unlockre/components-library/commit/c8cc6a5fc5303a8df3e401df6cb8ed4f59c1e04f))

# [7.0.0](https://github.com/unlockre/components-library/compare/v6.0.5...v7.0.0) (2022-06-16)


### Bug Fixes

* Add new font-size ([#70](https://github.com/unlockre/components-library/issues/70)) ([3727c90](https://github.com/unlockre/components-library/commit/3727c9069ad8557d902335e8deaa0c7e8cd60984))
* Throw error as default case in getArrowStyle at use-tooltip ([#72](https://github.com/unlockre/components-library/issues/72)) ([6c70ee2](https://github.com/unlockre/components-library/commit/6c70ee2bbb3df0efa12986739462249537ea99a6))


### Code Refactoring

* Split ItemList and SelectField into components by mode ([#71](https://github.com/unlockre/components-library/issues/71)) ([8d6f4d6](https://github.com/unlockre/components-library/commit/8d6f4d6a89351e2aa83a4395ed85fbf3cf4acc5c))


### Features

* Multi item select ([#55](https://github.com/unlockre/components-library/issues/55)) ([4780225](https://github.com/unlockre/components-library/commit/4780225b509893005ed5757495eb23588f5f32c6))


### BREAKING CHANGES

* No more SelectField component and instead now we have SingleSelectField and MultiSelectField.

## [6.0.5](https://github.com/unlockre/components-library/compare/v6.0.4...v6.0.5) (2022-06-03)


### Bug Fixes

* Fix @types/react version ([#69](https://github.com/unlockre/components-library/issues/69)) ([df745c0](https://github.com/unlockre/components-library/commit/df745c0fed2ddea8c838e3731d075608a1de4bce))

## [6.0.4](https://github.com/unlockre/components-library/compare/v6.0.3...v6.0.4) (2022-06-02)


### Bug Fixes

* Wrap CheckLabel component with MuiThemeProvider ([#68](https://github.com/unlockre/components-library/issues/68)) ([09f6719](https://github.com/unlockre/components-library/commit/09f6719ea96b58e24ac301c0841670b7d980ee3a))

## [6.0.3](https://github.com/unlockre/components-library/compare/v6.0.2...v6.0.3) (2022-06-01)


### Bug Fixes

* Adjust tab borders ([#67](https://github.com/unlockre/components-library/issues/67)) ([883bb31](https://github.com/unlockre/components-library/commit/883bb310d6abefd3dce0cf489b0c2dc4789a4165))
* Corrected tabs not displaying ([#65](https://github.com/unlockre/components-library/issues/65)) ([25c877d](https://github.com/unlockre/components-library/commit/25c877dc55f190c56e96a906637ee17cdb015d1b))
* Fix SecondaryButton default and hover colors ([#66](https://github.com/unlockre/components-library/issues/66)) ([a127c07](https://github.com/unlockre/components-library/commit/a127c07baf69da30f522cafa783e4d1276140d98))

## [6.0.2](https://github.com/unlockre/components-library/compare/v6.0.1...v6.0.2) (2022-05-24)


### Bug Fixes

* Add blue 800 and gray 020 theme colors ([#63](https://github.com/unlockre/components-library/issues/63)) ([b1584f4](https://github.com/unlockre/components-library/commit/b1584f413ee4e249ee215ee2cd8cec17ca499406))

## [6.0.1](https://github.com/unlockre/components-library/compare/v6.0.0...v6.0.1) (2022-05-24)


### Bug Fixes

* Add SelectField icons ([#62](https://github.com/unlockre/components-library/issues/62)) ([c08ebb8](https://github.com/unlockre/components-library/commit/c08ebb843a04fa0992cb4e9c5c449907d25ab3cb))
* Fix DateField theme issue ([#61](https://github.com/unlockre/components-library/issues/61)) ([529e006](https://github.com/unlockre/components-library/commit/529e0063e06be4d90e3fec45363a45c3071d55aa))

# [6.0.0](https://github.com/unlockre/components-library/compare/v5.1.0...v6.0.0) (2022-05-23)


### Bug Fixes

* Add Select button type ("button") ([#58](https://github.com/unlockre/components-library/issues/58)) ([1d7a56a](https://github.com/unlockre/components-library/commit/1d7a56a662e3079edf4e74bf25f4c6c99b14bfb8))
* Fix placeholder style ([#60](https://github.com/unlockre/components-library/issues/60)) ([5e87486](https://github.com/unlockre/components-library/commit/5e874865e540418eebda76bc306a20e11460d81a))


### Features

* Add fontWeight to theme getTypography helper ([#57](https://github.com/unlockre/components-library/issues/57)) ([665e25f](https://github.com/unlockre/components-library/commit/665e25fdd0efdde6c1ec5249b911a07328e35a2f))
* Use theme in field components ([#59](https://github.com/unlockre/components-library/issues/59)) ([43e3ea1](https://github.com/unlockre/components-library/commit/43e3ea1d62af503016fa3a66ab0d80a5fa275a11))


### BREAKING CHANGES

* Now TextField and TextArea need to rendered with ThemeProvider as an ancestor

# [5.1.0](https://github.com/unlockre/components-library/compare/v5.0.2...v5.1.0) (2022-05-20)


### Features

* Add display typography and fix blue 040 color ([#56](https://github.com/unlockre/components-library/issues/56)) ([414d4d8](https://github.com/unlockre/components-library/commit/414d4d8bc7f925b582c730571bfb9641964697ca))

## [5.0.2](https://github.com/unlockre/components-library/compare/v5.0.1...v5.0.2) (2022-05-19)


### Bug Fixes

* Adding type to base button ([#53](https://github.com/unlockre/components-library/issues/53)) ([fdaa542](https://github.com/unlockre/components-library/commit/fdaa542763432aa2d7bc59e8befef72f53238af5))
* Expose SelectFieldProps type ([#54](https://github.com/unlockre/components-library/issues/54)) ([ed27c68](https://github.com/unlockre/components-library/commit/ed27c68796827861bd47a4dc618bfa4cc4455f03))

## [5.0.1](https://github.com/unlockre/components-library/compare/v5.0.0...v5.0.1) (2022-05-18)


### Bug Fixes

* Fix Tooltip types ([#52](https://github.com/unlockre/components-library/issues/52)) ([cf845ef](https://github.com/unlockre/components-library/commit/cf845efcb69be0a57507ae11071d8d8fafcd1203))

# [5.0.0](https://github.com/unlockre/components-library/compare/v4.1.1...v5.0.0) (2022-05-18)


### Bug Fixes

* corrected typo on tab className ([#49](https://github.com/unlockre/components-library/issues/49)) ([11f5b52](https://github.com/unlockre/components-library/commit/11f5b5250b399367ecd70f8dd603cdd5d68c20bf))


### Features

* Add SelectField component ([#51](https://github.com/unlockre/components-library/issues/51)) ([c9739a4](https://github.com/unlockre/components-library/commit/c9739a445c25908e050aa41ee61782e8566a5e24))
* Add Tooltip component ([#50](https://github.com/unlockre/components-library/issues/50)) ([53522d3](https://github.com/unlockre/components-library/commit/53522d3b2f0a676668ea9e18320b08ce3769548a))


### BREAKING CHANGES

* Snackbar timeout is now restarted if the given handler (`onTimeout` function) changes.

## [4.1.1](https://github.com/unlockre/components-library/compare/v4.1.0...v4.1.1) (2022-05-13)


### Bug Fixes

* Add zindex to itemlist container ([#48](https://github.com/unlockre/components-library/issues/48)) ([d6c46ed](https://github.com/unlockre/components-library/commit/d6c46edba408bfab108a184bf818bdd67bbae7c3))

# [4.1.0](https://github.com/unlockre/components-library/compare/v4.0.0...v4.1.0) (2022-05-11)


### Bug Fixes

* Set undefined value to placeholder option ([#46](https://github.com/unlockre/components-library/issues/46)) ([5513dc9](https://github.com/unlockre/components-library/commit/5513dc9a65d232e33adc7872b867470476f12678))


### Features

* Add TextAreaField placeholder ([#47](https://github.com/unlockre/components-library/issues/47)) ([289cdd7](https://github.com/unlockre/components-library/commit/289cdd713251059fb8c8600038f0e8280bca0b0f))

# [4.0.0](https://github.com/unlockre/components-library/compare/v3.0.0...v4.0.0) (2022-05-09)


### Bug Fixes

* Fix DataField placeholder ([#43](https://github.com/unlockre/components-library/issues/43)) ([2fc1a3c](https://github.com/unlockre/components-library/commit/2fc1a3c01a63103b4a02f13da540f6d9671cd9b7))


### Code Refactoring

* Configure @fontsource/inter package as a peer dep ([#45](https://github.com/unlockre/components-library/issues/45)) ([2a77926](https://github.com/unlockre/components-library/commit/2a7792619e32315e0d94126ab5a1e8c54ab403c2))


### Features

* Add placeholder to Select component ([#41](https://github.com/unlockre/components-library/issues/41)) ([cca487f](https://github.com/unlockre/components-library/commit/cca487f2d50ed580f1ca844a3fe6121061ec25ef))
* Add snackbar component ([#44](https://github.com/unlockre/components-library/issues/44)) ([62ee21a](https://github.com/unlockre/components-library/commit/62ee21adff2c8fa17760b23aa237463cb6f52aa9))


### BREAKING CHANGES

* Configure @fontsource/inter package as a peer dep

# [3.0.0](https://github.com/unlockre/components-library/compare/v2.0.1...v3.0.0) (2022-05-06)


### Bug Fixes

* Fix TextField placeholder ([#40](https://github.com/unlockre/components-library/issues/40)) ([1d58644](https://github.com/unlockre/components-library/commit/1d586448d9d5ad491d2a54a2490797cd7cf4bd0e))


### Features

* Add tabs components ([#35](https://github.com/unlockre/components-library/issues/35)) ([11add5d](https://github.com/unlockre/components-library/commit/11add5d0fcdcda46db0962c3c27b142a4726398c))

## [2.0.1](https://github.com/unlockre/components-library/compare/v2.0.0...v2.0.1) (2022-05-03)


### Bug Fixes

* Expose TextField props in DateField ([#38](https://github.com/unlockre/components-library/issues/38)) ([3963b8d](https://github.com/unlockre/components-library/commit/3963b8d2639e804470bacab96b7d8b660c283785))

# [2.0.0](https://github.com/unlockre/components-library/compare/v1.14.0...v2.0.0) (2022-05-03)


* feat!: Use TextField in DateField (#36) ([a788d0b](https://github.com/unlockre/components-library/commit/a788d0b60599c40b32c9cbf9a4e8e4c260c2d213)), closes [#36](https://github.com/unlockre/components-library/issues/36)


### BREAKING CHANGES

* Value now has to be of type Date

* refactor: Remove no longer needed component (ConfigProvider)
* Deprecate ConfigProvider

# [1.14.0](https://github.com/unlockre/components-library/compare/v1.13.0...v1.14.0) (2022-04-22)


### Bug Fixes

* Only allow number or null in NumberField value/defaultValue props ([#34](https://github.com/unlockre/components-library/issues/34)) ([ffc27c9](https://github.com/unlockre/components-library/commit/ffc27c9ef6c040e77e12efac56cbe8c91f80f778))


### Features

* Add Dropdown component ([#32](https://github.com/unlockre/components-library/issues/32)) ([b91600d](https://github.com/unlockre/components-library/commit/b91600dabd46351753f8d6f610e740897cb2ddda))

# [1.13.0](https://github.com/unlockre/components-library/compare/v1.12.0...v1.13.0) (2022-04-18)


### Bug Fixes

* Remove usehooks ts dependency ([#29](https://github.com/unlockre/components-library/issues/29)) ([7378e9e](https://github.com/unlockre/components-library/commit/7378e9e82f78d82f0e4690541afafd1dbb51a8fb))


### Features

* Add MuiThemeProvider ([#30](https://github.com/unlockre/components-library/issues/30)) ([66f22c0](https://github.com/unlockre/components-library/commit/66f22c07daf1b9d308aa5a263251a962e45fd069))

# [1.12.0](https://github.com/unlockre/components-library/compare/v1.11.0...v1.12.0) (2022-04-13)


### Features

* Improve Modal and related logic ([#28](https://github.com/unlockre/components-library/issues/28)) ([ce89503](https://github.com/unlockre/components-library/commit/ce89503c04708c65248236206b0bc1e0505ec269))

# [1.11.0](https://github.com/unlockre/components-library/compare/v1.10.0...v1.11.0) (2022-04-12)


### Bug Fixes

* Fix places autocomplete double filtering ([#25](https://github.com/unlockre/components-library/issues/25)) ([a3e39d7](https://github.com/unlockre/components-library/commit/a3e39d7d8219e61ebddfb1d418a4131311203140))


### Features

* Add Modal* components ([#27](https://github.com/unlockre/components-library/issues/27)) ([b732ea9](https://github.com/unlockre/components-library/commit/b732ea91f2a66eff7ee0bc539cf986c0dd8ac700))
* Add theme related logic (ThemeProvider component and extras) ([#26](https://github.com/unlockre/components-library/issues/26)) ([144742d](https://github.com/unlockre/components-library/commit/144742dc8fceb252da15700fc2147b58ea2fcc66))

# [1.10.0](https://github.com/unlockre/components-library/compare/v1.9.1...v1.10.0) (2022-03-31)


### Features

* Add new fields ([#21](https://github.com/unlockre/components-library/issues/21)) ([8c27958](https://github.com/unlockre/components-library/commit/8c27958aa753f7adbd6f7484ac14459eb23831e9))
* Add TextAreaField component ([#24](https://github.com/unlockre/components-library/issues/24)) ([69787b4](https://github.com/unlockre/components-library/commit/69787b42ba450857ccfe8374d031e7dec355e2be))

## [1.9.1](https://github.com/unlockre/components-library/compare/v1.9.0...v1.9.1) (2022-03-30)


### Bug Fixes

* React as peer dep with upper bound ([#23](https://github.com/unlockre/components-library/issues/23)) ([cb97bd9](https://github.com/unlockre/components-library/commit/cb97bd949927664b7d744fe8f39102883e00dd9c))

# [1.9.0](https://github.com/unlockre/components-library/compare/v1.8.0...v1.9.0) (2022-03-29)


### Features

* Add RangeSlider value formatting ([#22](https://github.com/unlockre/components-library/issues/22)) ([322d513](https://github.com/unlockre/components-library/commit/322d513e7a16dfd2aafc253f2e162a4f63a32f85))

# [1.8.0](https://github.com/unlockre/components-library/compare/v1.7.0...v1.8.0) (2022-03-25)


### Features

* New TextField component version ([#20](https://github.com/unlockre/components-library/issues/20)) ([77fffa8](https://github.com/unlockre/components-library/commit/77fffa8664b2aa151b904c6f694763e5dc04cf66))

# [1.7.0](https://github.com/unlockre/components-library/compare/v1.6.1...v1.7.0) (2022-03-23)


### Features

* Next release ([#19](https://github.com/unlockre/components-library/issues/19)) ([5b1cc8e](https://github.com/unlockre/components-library/commit/5b1cc8e3a5e35dce2bab5d92769c1a5d0a4ede73))

## [1.6.1](https://github.com/unlockre/components-library/compare/v1.6.0...v1.6.1) (2022-03-18)


### Bug Fixes

* Fix DataTable styles ([#18](https://github.com/unlockre/components-library/issues/18)) ([64ed2bc](https://github.com/unlockre/components-library/commit/64ed2bcbe230ac85294f05fd7cc7ff6eab3536d9))

# [1.6.0](https://github.com/unlockre/components-library/compare/v1.5.0...v1.6.0) (2022-03-17)


### Bug Fixes

* Fix checkbox ([#15](https://github.com/unlockre/components-library/issues/15)) ([e39a212](https://github.com/unlockre/components-library/commit/e39a21247eee3449c6f4496418092b114119447a))


### Features

* Next release ([#17](https://github.com/unlockre/components-library/issues/17)) ([abf221d](https://github.com/unlockre/components-library/commit/abf221d30ec0f45f134959729e45ff12ff2d6974))

# [1.5.0](https://github.com/unlockre/components-library/compare/v1.4.6...v1.5.0) (2022-03-11)


### Features

* Improve data table style ([#16](https://github.com/unlockre/components-library/issues/16)) ([93bd91e](https://github.com/unlockre/components-library/commit/93bd91e354c67d98051cc1ae9297ac1d0670f6c6))

## [1.4.6](https://github.com/unlockre/components-library/compare/v1.4.5...v1.4.6) (2022-02-11)


### Bug Fixes

* Fix map-view bounds issue and some extras ([#14](https://github.com/unlockre/components-library/issues/14)) ([4eb408d](https://github.com/unlockre/components-library/commit/4eb408daaad357d8a3b273da296eb6f5c577cd37))

## [1.4.5](https://github.com/unlockre/components-library/compare/v1.4.4...v1.4.5) (2022-02-10)


### Bug Fixes

* Fix autocomplete-field cross icon size ([#12](https://github.com/unlockre/components-library/issues/12)) ([484e0df](https://github.com/unlockre/components-library/commit/484e0df662960bd66f63ed97a25b534901e644d3))
* Fix button ([#13](https://github.com/unlockre/components-library/issues/13)) ([9d584af](https://github.com/unlockre/components-library/commit/9d584afec235dcf17560cb37bc975c36ab734593))

## [1.4.4](https://github.com/unlockre/components-library/compare/v1.4.3...v1.4.4) (2022-02-09)


### Bug Fixes

* Fix fields style (round 2) ([#11](https://github.com/unlockre/components-library/issues/11)) ([e741296](https://github.com/unlockre/components-library/commit/e741296c026cc569c4e2b350543bae8ae9516a7e))

## [1.4.3](https://github.com/unlockre/components-library/compare/v1.4.2...v1.4.3) (2022-02-09)


### Bug Fixes

* Fix fields style ([#10](https://github.com/unlockre/components-library/issues/10)) ([4043183](https://github.com/unlockre/components-library/commit/4043183a64426bbba2b04a2911ba8302675bc01b))

## [1.4.2](https://github.com/unlockre/components-library/compare/v1.4.1...v1.4.2) (2022-02-04)


### Bug Fixes

* Fix range slider ([#9](https://github.com/unlockre/components-library/issues/9)) ([f42c19c](https://github.com/unlockre/components-library/commit/f42c19c50163e7d67fca6caa650c7e43caf53fad))

## [1.4.1](https://github.com/unlockre/components-library/compare/v1.4.0...v1.4.1) (2022-02-04)


### Bug Fixes

* Add limit to range slider ([#8](https://github.com/unlockre/components-library/issues/8)) ([b6f8112](https://github.com/unlockre/components-library/commit/b6f81120e6b30fd035859d94b2e199912e92e8e5))

# [1.4.0](https://github.com/unlockre/components-library/compare/v1.3.1...v1.4.0) (2022-02-04)


### Features

* Add places-autocomplete-field component and some extras ([#7](https://github.com/unlockre/components-library/issues/7)) ([919558e](https://github.com/unlockre/components-library/commit/919558e45ee04e937aca2ee693d0654260b7410e))

## [1.3.1](https://github.com/unlockre/components-library/compare/v1.3.0...v1.3.1) (2022-02-03)


### Bug Fixes

* Use camel case in MapBounds type ([c12078c](https://github.com/unlockre/components-library/commit/c12078c1fbeeb507f2698380c987f7570df078c9))

# [1.3.0](https://github.com/unlockre/components-library/compare/v1.2.0...v1.3.0) (2022-02-03)


### Features

* Add text-field and autocomplete-field components ([#5](https://github.com/unlockre/components-library/issues/5)) ([26feb68](https://github.com/unlockre/components-library/commit/26feb68db3ffe84ee5e97c47dd4c81cfdaa381ee))
* Filter components ([#4](https://github.com/unlockre/components-library/issues/4)) ([9ae9f84](https://github.com/unlockre/components-library/commit/9ae9f84fc776c0caddf7c35d5a9d9a29e6ea7fed))

# [1.2.0](https://github.com/unlockre/components-library/compare/v1.1.0...v1.2.0) (2022-01-30)


### Bug Fixes

* Remove DataTable autoHeight prop ([#2](https://github.com/unlockre/components-library/issues/2)) ([81f4d99](https://github.com/unlockre/components-library/commit/81f4d991c7fbf721455766a8c8289ad0c68df1b0))


### Features

* Add unstyled and map components ([#3](https://github.com/unlockre/components-library/issues/3)) ([dbb6c68](https://github.com/unlockre/components-library/commit/dbb6c68d52724d7fb4de2f34c845104b8d7af1a4))

# [1.1.0](https://github.com/unlockre/components-library/compare/v1.0.0...v1.1.0) (2022-01-18)


### Features

* Add DataTable component ([#1](https://github.com/unlockre/components-library/issues/1)) ([b81f42b](https://github.com/unlockre/components-library/commit/b81f42b532d41c6934715f3c0476f22bfe9b88ae))

# 1.0.0 (2022-01-12)


### Features

* Setup project ([b84bf97](https://github.com/unlockre/components-library/commit/b84bf97fcecf2d07dff69369a510e016a0fdbe18))
