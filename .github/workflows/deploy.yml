name: Deploy

on:
  workflow_call:
    inputs:
      vercel-alias-domain:
        description: Vercel Alias Domain
        type: string
      vercel-environment:
        description: Vercel Environment
        required: true
        type: string
      with-semantic-release:
        description: With Semantic Release?
        default: false
        type: boolean

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v2
        with:
          persist-credentials: false

      - name: Setup Node and Yarn
        uses: unlockre/github-resources/actions/setup-node-and-yarn@main
        with:
          github-token: ${{ secrets.GH_PAT }}

      - name: Build
        run: |
          yarn build
        env:
          GH_PAT: ${{ secrets.GH_PAT }}

      - name: Deploy to Vercel
        uses: unlockre/github-resources/actions/deploy-to-vercel@main
        with:
          github-token: ${{ secrets.GH_PAT }}
          project-name: Components Library
          vercel-alias-domain: ${{ inputs.vercel-alias-domain }}
          vercel-environment: ${{ inputs.vercel-environment }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-token: ${{ secrets.VERCEL_TOKEN }}

      - name: Semantic versioning
        if: ${{ inputs.with-semantic-release }}
        run: |
          yarn semantic-release
        env:
          GH_PAT: ${{ secrets.GH_PAT }}
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
          NODE_AUTH_TOKEN: ${{ secrets.GH_PAT }}
          NPM_TOKEN: ${{ secrets.GH_PAT }}
          RELEASES_SLACK_WEBHOOK: ${{ secrets.RELEASES_SLACK_WEBHOOK }}
