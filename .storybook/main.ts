import type {StorybookConfig} from "@storybook/react-vite";
import tsconfigPaths from "vite-tsconfig-paths";

const config: StorybookConfig = {
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-designs"
  ],

  framework: "@storybook/react-vite",

  stories: ["../src/**/*.stories.@(js|jsx|ts|tsx)"],

  viteFinal: async config => ({
    ...config,
    plugins: [...(config.plugins ?? []), tsconfigPaths()]
  })
};

export default config;
