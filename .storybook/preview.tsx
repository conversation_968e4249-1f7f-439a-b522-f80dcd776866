import "@fontsource-variable/inter";

import type {Preview} from "@storybook/react";
import {createGlobalStyle} from "styled-components";

import {ThemeProvider} from "../src/theme-provider";
import {lightTheme} from "../src/theme-provider/theme/light-theme";
import {magicVioletTheme} from "../src/theme-provider/theme/magic-violet-theme";

// eslint-disable-next-line @typescript-eslint/naming-convention
const GlobalStyle = createGlobalStyle`
  html, body, #storybook-root {
    height: 100%;
  }
`;

const preview: Preview = {
  initialGlobals: {
    theme: "light"
  },
  globalTypes: {
    theme: {
      description: "Global theme for components",
      toolbar: {
        dynamicTitle: true,
        icon: "paintbrush",
        items: [
          {title: "Light", value: "light"},
          {title: "Magic Violet", value: "magicViolet"}
        ],
        title: "Theme"
      }
    }
  },
  parameters: {
    actions: {
      argTypesRegex: "^on[A-Z].*"
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/
      }
    }
  },
  decorators: [
    (story, context) => (
      <ThemeProvider
        theme={
          context.globals.theme === "light" ? lightTheme : magicVioletTheme
        }
      >
        <GlobalStyle />
        {story()}
      </ThemeProvider>
    )
  ]
};

export default preview;
