# @unlockre/components-library

> Keyway Components Library

- [About](#about)
- [Installation](#installation)
  - [Package](#package)
  - [Peer Dependencies](#peer-dependencies)
- [Components](#components)
  - [AutocompleteField](#autocompletefield)
  - [badges/*](src/badges)
  - [BreadcrumbList](src/breadcrumb-list)
  - [button/Button](src/button) (deprecated use `buttons/Button` instead)
  - [buttons/Button](src/buttons)
  - [ButtonDropdown](src/button-dropdown)
  - [ButtonGroup](src/button-group)
  - [Checkbox](src/checkbox)
  - [ChevronButton](src/chevron-button)
  - [ErrorInfo](src/error-info)
  - [DataTable](#datatable)
  - [DateField](#datefield)
  - [DatePicker](src/date-picker)
  - [Drawer](src/drawer)
  - [dialogs/*](src/dialogs)
  - [ErrorBoundary](src/error-boundary)
  - [info-item/*](src/info-item)
  - [InfoList](src/info-list)
  - [Link](src/link)
  - [Map](#map)
  - [menus/*](src/menus)
  - [Message](src/message)
  - [Modal](#modal)
  - [PasswordField](src/password-field)
  - [Pill](#pill)
  - [PillDropdown](#pilldropdown)
  - [PlacesAutocompleteField](#placesautocompletefield)
  - [PhoneNumberField](src/phone-number-field)
  - [*ProgressBar](src/progress-bar)
  - [PropertyFinder](src/property-finder)
  - [RadioButtonGroup](src/radio-button-group)
  - [RangeSlider](#rangeslider)
  - [Select](#select)
  - [shapes/*](src/shapes)
  - [Snackbar](src/snackbar)
  - [StackableModal](src/stackable-modal)
  - [Table](src/table)
  - [Tabs](src/tabs)
  - [TabSelector](src/tab-selector/)
  - [TextField](#textfield)
  - [ThemeProvider](src/theme-provider)
  - [ToggleButtonGroup](src/toggle-button-group)
  - [Tooltip](src/tooltip/README.md)
  - [Unstyled*](#unstyled)
  - [UserAvatar](src/user-avatar)
  - [Walkthrough](src/walkthrough)
  - [widget/*](src/widget)
- [Development](#development)
  - [Linking](#linking)
- [Release](#release)
- [License](#license)

## About

This package contains all the foundational components that serve as the building blocks for the applications we run on **Keyway**.

## Installation

### Package

Add the following to the `.yarnrc.yml` so Yarn can fetch the **Keyway** packages from Github NPM registry.

```yml
npmScopes:
  unlockre:
    npmAuthToken: "${GH_PAT}"
    npmRegistryServer: "https://npm.pkg.github.com"
```

Once you do this, you will need to create an environment variable `GH_PAT` containing your Github Personal Access Token (**PAT**).

Run `yarn add @unlockre/components-library` to add the **Keyway Components Library** to the package dependencies.

> In case you don't have a PAT yet, you can create it following the steps described [here](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token).

### Peer Dependencies

Install [@fontsource-variable/inter package](https://www.npmjs.com/package/@fontsource-variable/inter) that contains the font we use in the theme.

Add an import statement to the component root of your project to load the inter variable font.

```typescript
import "@fontsource-variable/inter";
```

## Components

### AutocompleteField

This component is based on Material UI Autocomplete.

In addition to the main component, these are 2 are required to configure an autocomplete experience:
- [AutocompleteItem](src/autocomplete-field/autocomplete-item.tsx)
- [AutocompleteText](src/autocomplete-field/autocomplete-text.tsx)

See [source](src/autocomplete-field).

### Counter Field

This components is customized input type number whith the posibility to increment and decrement the value.

See [source](src/counter-field).

### DateField

This component is based on Material UI DateField (Labs) combined with the [TextField](src/text-field) component.

See [source](src/date-field).

### DataTable

This component is based on Material UI [DataGrid](https://mui.com/x/api/data-grid/data-grid).

It renders a Table given an array column definitions and rows with data.

See [source](src/data-table).

### Map/*

These components can be used to visualize a map from google and interact with it.

See [source](src/map).

### Pill

This component renders a label and an optional right element inside a rounded box with the desired color variant.

See [source](src/pill).

### PillDropdown

This component is a dropdown but using a [Pill](#pill) component for the ItemList toggler.

See [source](src/pill-dropdown).

### PlacesAutocompleteField

This component is built on top of [AutocompleteField](src/autocomplete-field) integrating google places to get the suggestions.

See [source](src/places-autocomplete-field).

### Modal*

Here you can find a [Modal](src/modal/modal.tsx) and a [ModalHeader](src/modal/modal-header.tsx) components to be used to provide a modal experience.

See [source](src/modal).

### RangeSlider

This component is based on Material UI [Slider](https://mui.com/material-ui/api/slider).

See [source](src/range-slider).

### Select

This component is based on Material UI [Select](https://mui.com/material-ui/api/select).

See [source](src/select).

### TextField

This component is based on Material UI [TextField](https://mui.com/material-ui/api/text-field).

See [source](src/text-field).

### Unstyled*

These components can be used as the building blocks of others given that are completely unstyled.

See [source](src/unstyled).

## Development

To start the project in **development** mode, simply run `yarn dev` and a new build will be triggered each time a file changes.

### Test changes on a project

In case you are modifying a component or creating a new one here at the same time you are working on another project that uses this package, once you are done with the addition or modification of the component(s), you can do the following to add it to the project that needs it to test it before releasing:

```
$ cd <components-library-folder>
$ yarn pack
$ cd <project-folder>/node_modules/unlockre
$ yarn add <components-library-folder>/package.tgz
```

Once you are done with the development in the components library and in the other project, you will have to first release the components library, and once it's done, grab the new version of it and place it in the other project's `package.json` so this way you will get the latest updates you were working on.

## Release

- Update develop branch (git checkout develop && git pull)
- Update main branch (git checkout main && git pull)
- Merge develop into main branch (git merge --ff-only develop)
- Push changes merged into main to origin (git push origin main)

## License

[MIT](LICENSE.txt) :copyright: **Keyway Real Estate, Inc**
