// TODO: Move this to a package

import fs from "node:fs/promises";

import {dereference} from "@apidevtools/json-schema-ref-parser";

const resolveJsonWithRefs = async (
  inputFilename: string,
  outputFilename: string
) => {
  const jsonWithoutRefs = await dereference(inputFilename);

  fs.writeFile(outputFilename, JSON.stringify(jsonWithoutRefs, null, 2));
};

const [inputFilename, outputFilename] = process.argv.slice(2);

if (!inputFilename) {
  throw new Error("Missing argument #1: no input filename");
}

if (!outputFilename) {
  throw new Error("Missing argument #2: no output filename");
}

resolveJsonWithRefs(inputFilename, outputFilename);
