import {MenuGroup} from "@szhsin/react-menu";
import {useFuzzySearch} from "@unlockre/utils-react/dist";
import type {UseFuzzySearchOptions} from "@unlockre/utils-react/dist";
import {Fragment, useMemo, useState} from "react";
import type {ComponentProps, ReactElement} from "react";
import styled from "styled-components";

import {Menu} from "./menu";
import {SearchMenuEntry} from "./search-menu-entry";

type MenuProps<TItem> = ComponentProps<typeof Menu<TItem>>;

type ExposedMenuProps<TItem> = Omit<MenuProps<TItem>, "onItemSelect">;

type OnClose<TItem> = MenuProps<TItem>["onClose"];

type OnItemSelect<TItem> = MenuProps<TItem>["onItemSelect"];

type SearchKeys<TItem> = UseFuzzySearchOptions<TItem>["keys"];

type Props<TItem> = ExposedMenuProps<TItem> & {
  getItemId: (item: TItem) => number | string;
  items: TItem[];
  onItemSelect: OnItemSelect<TItem>;
  renderMenuEntry: (item: TItem) => ReactElement;
  searchKeys?: SearchKeys<TItem>;
};

// We need this to compensate the `overflow: auto`, so we can see the outline
// around menu entries
const StyledMenuGroup = styled(MenuGroup)`
  padding: 1px;
`;

const SearchableMenu = <TItem,>({
  getItemId,
  items,
  onClose,
  renderMenuEntry,
  searchKeys,
  ...rest
}: Props<TItem>) => {
  const [searchTerm, setSearchTerm] = useState("");

  const fuzzySearchOptions = useMemo(
    () => ({...(searchKeys && {keys: searchKeys}), threshold: 0.2}),
    [searchKeys]
  );

  const filteredItems = useFuzzySearch({
    items,
    options: fuzzySearchOptions,
    searchTerm
  });

  const handleMenuClose: OnClose<TItem> = event => {
    onClose?.(event);

    setSearchTerm("");
  };

  return (
    <Menu {...rest} onClose={handleMenuClose}>
      <SearchMenuEntry {...{searchTerm}} onSearchTermChange={setSearchTerm} />
      <StyledMenuGroup takeOverflow>
        {filteredItems?.map(item => (
          <Fragment key={getItemId(item)}>{renderMenuEntry(item)}</Fragment>
        ))}
      </StyledMenuGroup>
    </Menu>
  );
};

export {SearchableMenu};
