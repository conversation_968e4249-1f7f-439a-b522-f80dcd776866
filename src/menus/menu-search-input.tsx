import {MagnifyingGlass} from "@phosphor-icons/react";
import {forwardRef} from "react";
import styled, {css} from "styled-components";

import {IconWithColor} from "@/icons";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledInput} from "@/unstyled";

type Props = {
  onSearchTermChange: (searchTerm: string) => unknown;
  searchTerm: string;
};

const backgroundColorCss = css`
  background-color: ${getColorByAlias("bgSecondary")};
`;

const StyledInput = styled(UnstyledInput)`
  ${backgroundColorCss};

  ${getTypography("latest", "bodyM")}

  color: ${getColorByAlias("textMuted")};
  min-width: 0;
  flex: 1;
`;

const Container = styled.div`
  ${backgroundColorCss};

  align-items: center;
  border: 1px solid ${getColorByAlias("borderStrong")};
  border-radius: 8px;
  display: flex;
  gap: 6px;
  padding: 6px 8px;
`;

const MenuSearchInput = forwardRef<HTMLInputElement, Props>(
  ({onSearchTermChange, searchTerm}, ref) => (
    <Container>
      <IconWithColor
        getColor={getColorByAlias("iconMuted")}
        icon={MagnifyingGlass}
        size={16}
        weight="regular"
      />
      <StyledInput
        {...{ref}}
        autoFocus
        onChange={event => onSearchTermChange(event.target.value)}
        placeholder="Search"
        spellCheck="false"
        value={searchTerm}
      />
    </Container>
  )
);

export {MenuSearchInput};
