import {FocusableItem} from "@szhsin/react-menu";
import type {ComponentPropsWithoutRef} from "react";
import styled from "styled-components";

import {MenuSearchInput} from "./menu-search-input";

type MenuSearchInputProps = ComponentPropsWithoutRef<typeof MenuSearchInput>;

type Props = MenuSearchInputProps;

const Container = styled(FocusableItem)`
  padding-bottom: 8px;
`;

const SearchMenuEntry = (props: Props) => (
  <Container>{({ref}) => <MenuSearchInput {...{ref}} {...props} />}</Container>
);

export {SearchMenuEntry};
