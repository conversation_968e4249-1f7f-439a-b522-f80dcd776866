import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentRef} from "react";

import {But<PERSON>} from "@/buttons";

import {MenuEntry, SearchableMenu, useMenuOnClick} from ".";

type Args = Record<string, never>;

type ButtonRef = ComponentRef<typeof Button>;

type Company = {
  id: string;
  name: string;
};

const companies: Company[] = [
  {id: "1", name: "Apple"},
  {id: "2", name: "Google"},
  {id: "3", name: "Meta"},
  {id: "4", name: "Company with a very long name"}
];

const companySearchKeys: (keyof Company)[] = ["name"];

const companyNames = companies.map(company => company.name);

const WithObjectItems: StoryFn<Args> = () => {
  const {anchorProps, menuProps} = useMenuOnClick<ButtonRef>();

  return (
    <>
      <Button {...anchorProps} size="medium" variant="primary">
        Open Menu
      </Button>
      <SearchableMenu
        {...menuProps}
        getItemId={item => item.id}
        items={companies}
        onItemSelect={item => console.log(item)}
        renderMenuEntry={item => <MenuEntry {...{item}} label={item.name} />}
        searchKeys={companySearchKeys}
      />
    </>
  );
};

const WithStringItems: StoryFn<Args> = () => {
  const {anchorProps, menuProps} = useMenuOnClick<ButtonRef>();

  return (
    <>
      <Button {...anchorProps} size="medium" variant="primary">
        Open Menu
      </Button>
      <SearchableMenu
        {...menuProps}
        getItemId={item => item}
        items={companyNames}
        onItemSelect={item => console.log(item)}
        renderMenuEntry={item => <MenuEntry {...{item}} label={item} />}
      />
    </>
  );
};

const meta: Meta<Args> = {
  title: "menus/searchable-menu"
};

export {meta as default, WithObjectItems, WithStringItems};
