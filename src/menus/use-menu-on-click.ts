import {useClick} from "@szhsin/react-menu";
import {useRef} from "react";

import {useMenuStateWithOptions} from "./use-menu-state-with-options";

const useMenuOnClick = <TAnchorElement extends HTMLElement>() => {
  const anchorRef = useRef<TAnchorElement>(null);

  const [menuState, toggleMenu] = useMenuStateWithOptions();

  const anchorPropsWithoutRef = useClick(menuState.state, toggleMenu);

  const anchorProps = {...anchorPropsWithoutRef, ref: anchorRef};

  const menuProps = {
    ...menuState,
    anchorRef,
    onClose: () => toggleMenu(false)
  };

  return {anchorProps, menuProps};
};

export {useMenuOnClick};
