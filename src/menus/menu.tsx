import {ControlledMenu} from "@szhsin/react-menu";
import {menuSelector} from "@szhsin/react-menu/style-utils";
import type {ComponentProps} from "react";
import styled, {keyframes} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

type MenuLayer = number | "portal";

type ControlledMenuProps = ComponentProps<typeof ControlledMenu>;

type ExposedControlledMenuProps = Pick<
  ControlledMenuProps,
  | "align"
  | "anchorRef"
  | "children"
  | "direction"
  | "endTransition"
  | "onClose"
  | "onMouseEnter"
  | "onMouseLeave"
  | "state"
>;

type Props<TItem = undefined> = ExposedControlledMenuProps & {
  layer?: MenuLayer;
  onItemSelect?: (selectedItem: TItem) => unknown;
};

const menuShow = keyframes`
  from {
    opacity: 0;
  }
`;

const menuHide = keyframes`
  to {
    opacity: 0;
  }
`;

const StyledControlledMenu = styled(ControlledMenu)`
  ${menuSelector.name} {
    background-color: ${getColorByAlias("bg")};
    border-radius: 8px;
    border: 1px solid ${getColorByAlias("borderSubtle")};
    box-shadow:
      0px 0px 0px 1px ${getColor("latest", "violetAlpha025")},
      0px 6px 12px -3px ${getColor("latest", "neutralAlpha025")},
      0px 6px 18px 0px ${getColor("latest", "neutralAlpha100")};
    list-style: none;
    max-width: 200px;
    padding: 8px;
  }

  ${menuSelector.name}:focus {
    outline: none;
  }

  ${menuSelector.stateOpening} {
    animation: ${menuShow} 0.2s ease-out;
  }

  // NOTE: animation-fill-mode: forwards is required to
  // prevent flickering with React 18 createRoot()
  ${menuSelector.stateClosing} {
    animation: ${menuHide} 0.2s ease-out forwards;
  }
`;

const Menu = <TItem,>({layer, onItemSelect, ...rest}: Props<TItem>) => (
  <StyledControlledMenu
    {...rest}
    gap={8}
    menuStyle={{zIndex: layer === "portal" ? undefined : layer}}
    onItemClick={e => onItemSelect?.(e.value)}
    overflow="auto"
    portal={layer === "portal"}
    setDownOverflow
  />
);

export {Menu};
