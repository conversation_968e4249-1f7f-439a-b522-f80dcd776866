import {Share, Trash} from "@phosphor-icons/react";
import type {Meta, StoryFn} from "@storybook/react";
import styled, {css} from "styled-components";

import * as withTheme from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {MenuEntryIcon} from "./menu-entry-icon";

import {Menu, MenuEntry, useMenuOnClick, useMenuOnHover} from ".";

type Args = Record<string, never>;

type Company = {
  id: string;
  name: string;
};

const menuAnchorCss = css`
  ${withTheme.getTypography("body", "s")};

  border: 1px solid ${withTheme.getColorByAlias("accentPrimary")};
  border-radius: 6px;
  color: ${withTheme.getColorByAlias("textPrimary")};
  padding: 12px 0;
  text-align: center;
`;

const OpenMenuBox = styled.div`
  ${menuAnchorCss};

  width: 100px;
`;

const OpenMenuButton = styled(UnstyledButton)`
  ${menuAnchorCss};

  width: 100px;
`;

const companies: Company[] = [
  {id: "1", name: "Apple"},
  {id: "2", name: "Google"},
  {id: "3", name: "Meta"}
];

const WithOnHover: StoryFn<Args> = () => {
  const {anchorProps, menuProps} = useMenuOnHover<HTMLDivElement>();

  return (
    <>
      <OpenMenuBox {...anchorProps}>Menu</OpenMenuBox>
      <Menu {...menuProps} onItemSelect={console.log}>
        {companies.map(company => (
          <MenuEntry item={company} key={company.id} label={company.name} />
        ))}
      </Menu>
    </>
  );
};

const WithOnClick: StoryFn<Args> = () => {
  const {anchorProps, menuProps} = useMenuOnClick<HTMLButtonElement>();

  return (
    <>
      <OpenMenuButton {...anchorProps}>Open Menu</OpenMenuButton>
      <Menu {...menuProps} align="start">
        <MenuEntry
          label="Share"
          left={<MenuEntryIcon icon={Share} />}
          onClick={() => console.log("Share")}
        />
        <MenuEntry
          label="Delete"
          left={<MenuEntryIcon icon={Trash} />}
          onClick={() => console.log("Delete")}
        />
      </Menu>
    </>
  );
};

const meta: Meta<Args> = {
  title: "menus/menu"
};

export {meta as default, WithOnClick, WithOnHover};
