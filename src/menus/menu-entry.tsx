import {MenuItem} from "@szhsin/react-menu";
import type {ComponentProps, ReactNode} from "react";
import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import {MenuEntryLabel} from "./menu-entry-label";

type MenuItemProps = ComponentProps<typeof MenuItem>;

type ExposedMenuItemProps = Pick<MenuItemProps, "onClick">;

type Props<TItem = undefined> = ExposedMenuItemProps & {
  item?: TItem;
  label: string;
  left?: ReactNode;
};

const Container = styled(MenuItem)`
  align-items: center;
  border-radius: 6px;
  display: flex;
  gap: 8px;
  padding: 6px 8px;

  &:focus {
    outline: 1px solid ${getColorByAlias("borderAccent")};
  }

  &:hover {
    background-color: ${getColorByAlias("bgTransparentHover")};
  }
`;

const MenuEntry = <TItem,>({item, label, left, onClick}: Props<TItem>) => (
  <Container {...{onClick}} value={item}>
    {left}
    <MenuEntryLabel>{label}</MenuEntryLabel>
  </Container>
);

export {MenuEntry};
