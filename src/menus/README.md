# Menus

- [Menu](#menu)
- [SearchableMenu](#searchablemenu)

## Menu

## About

This component renders a menu which could be opened on hovering an element or clicking on it.

> The Menu component has a `layer` prop which controls how the menu is rendered, it has no default value, meaning that the Menu by default won't use any of the following supported alternatives:
>
> - `number`: the menu is rendered in a layer (z-index) with the given number
> - `"portal"`: the menu is rendered in a portal

## Examples

### Example opening the menu on click and not using items (using click handlers directly on the menu entries)

```tsx
import {Share, Trash} from "@phosphor-icons/react";
import {Button} from "@unlockre/components-library/dist/buttons";
import {Menu, MenuEntry, MenuEntryIcon, useMenuOnClick} from "@unlockre/components-library/dist/menu";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import type {ComponentRef} from "react";

type ButtonRef = ComponentRef<typeof Button>;

const ExampleOnClick = () => {
  const {anchorProps, menuProps} = useMenuOnClick<ButtonRef>();

  return (
    <ThemeProvider>
      <Button {...anchorProps}>Open Menu</Button>
      <Menu {...menuProps}>
        <MenuEntry
          label="Share"
          left={<MenuEntryIcon icon={Share} />}
          onClick={() => console.log("Share")}
        />
        <MenuEntry
          left={<MenuEntryIcon icon={Trash} />}
          label="Delete"
          onClick={() => console.log("Delete")}
        />
      </Menu>
    </ThemeProvider>
  )
};

export {ExampleOnClick};
```

### Example opening the menu on hover and using items

```tsx
import {Button} from "@unlockre/components-library/dist/buttons";
import {Menu, MenuEntry, useMenuOnHover} from "@unlockre/components-library/dist/menu";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

type Company = {
  id: string;
  name: string;
};

const companies: Company[] = [
  {id: "1", name: "Apple"},
  {id: "2", name: "Google"},
  {id: "3", name: "Meta"}
];

const ExampleOnHover = () => {
  const {anchorProps, menuProps} = useMenuOnHover<HTMLDivElement>();

  return (
    <ThemeProvider>
      <div {...anchorProps}>Open Menu</div>
      <Menu {...menuProps} onItemSelect={item => console.log(item)}>
        {companies.map(company => (
          <MenuEntry item={company} key={company.id} label={company.name} />
        ))}
      </Menu>
    </ThemeProvider>
  )
};

export {ExampleOnHover};
```

## SearchableMenu

## About

This component renders a menu with a search input to filter the provided items.

Menu entries are rendered using a `renderMenuEntry` function that receives the current item as a parameter.

Items could be either objects or primitive types like string or numbers. In case of objects, a `searchKeys` prop must be provided to specify the keys to search on.

## Examples

### Example with object items

```tsx
import {Button} from "@unlockre/components-library/dist/buttons";
import {SearchableMenu, useMenuOnClick} from "@unlockre/components-library/dist/menu";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import type {ComponentRef} from "react";

type ButtonRef = ComponentRef<typeof Button>;

type Company = {
  id: string;
  name: string;
};

const companies: Company[] = [
  {id: "1", name: "Apple"},
  {id: "2", name: "Google"},
  {id: "3", name: "Meta"}
];

const companySearchKeys: (keyof Company)[] = ["name"];

const ExampleWithObjectItems = () => {
  const {anchorProps, menuProps} = useMenuOnClick<ButtonRef>();

  return (
    <ThemeProvider>
      <Button {...anchorProps} size="medium" variant="primary">
        Open Menu
      </Button>
      <SearchableMenu
        {...menuProps}
        getItemId={item => item.id}
        items={companies}
        onItemSelect={item => console.log(item)}
        renderMenuEntry={item => <MenuEntry {...{item}} label={item.name} />}
        // We need to provide the keys to search on since the items are objects
        searchKeys={companySearchKeys}
      />
    </ThemeProvider>
  )
};

export {ExampleWithObjectItems};
```

### Example with string items

```tsx
import {Button} from "@unlockre/components-library/dist/buttons";
import {SearchableMenu, useMenuOnClick} from "@unlockre/components-library/dist/menu";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import type {ComponentRef} from "react";

type ButtonRef = ComponentRef<typeof Button>;

const companyNames = ["Apple", "Google", "Meta"] as const;

const ExampleWithStringItems = () => {
  const {anchorProps, menuProps} = useMenuOnClick<ButtonRef>();

  return (
    <ThemeProvider>
      <Button {...anchorProps} size="medium" variant="primary">
        Open Menu
      </Button>
      <SearchableMenu
        {...menuProps}
        getItemId={item => item}
        items={companyNames}
        onItemSelect={item => console.log(item)}
        renderMenuEntry={item => <MenuEntry {...{item}} label={item} />}
      />
    </ThemeProvider>
  )
};

export {ExampleWithStringItems};
```