import {useHover} from "@szhsin/react-menu";
import {useRef} from "react";

import {useMenuStateWithOptions} from "./use-menu-state-with-options";

const useMenuOnHover = <TAnchorElement extends HTMLElement>() => {
  const anchorRef = useRef<TAnchorElement>(null);

  const [menuState, toggleMenu] = useMenuStateWithOptions();

  const {anchorProps: anchorPropsWithoutRef, hoverProps} = useHover(
    menuState.state,
    toggleMenu
  );

  const anchorProps = {...anchorPropsWithoutRef, ref: anchorRef};

  const menuProps = {
    ...menuState,
    ...hoverProps,
    anchorRef,
    onClose: () => toggleMenu(false)
  };

  return {anchorProps, menuProps};
};

export {useMenuOnHover};
