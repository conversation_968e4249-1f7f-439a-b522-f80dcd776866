# Shapes

Here you will find different shapes done using css.

## Components

- [Circle](#Circle)

## Circle

### About

A circle shape.

### Props

```ts
type Props = {
  className?: string;
  getColor: GetColor;
  size: number;
};
```

### Example

```tsx
import {Circle} from "@unlockre/components-library/dist/shapes";
import {getColor} from "@unlockre/components-library/dist/theme-provider/theme";
import {ComponentProps} from "react";

const BlueCircle = () => (
  <Circle getColor={getColor("blue", "500")} size={50} />
);
```
