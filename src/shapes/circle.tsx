import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

type Props = {
  className?: string;
  getColor: GetColor;
  size: number;
};

type ContainerStyledProps = {
  $getColor: GetColor;
  $size: number;
};

const Container = styled.div<ContainerStyledProps>`
  background-color: ${props => props.$getColor};
  border-radius: 50%;
  height: ${props => props.$size}px;
  width: ${props => props.$size}px;
`;

const Circle = ({getColor, size, ...rest}: Props) => (
  <Container {...rest} $getColor={getColor} $size={size} />
);

export {Circle};
