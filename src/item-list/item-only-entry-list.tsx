import {Fragment} from "react";
import type {ReactElement} from "react";

import ItemEntryListContainer from "./item-entry-list-container";

type RenderItemOnlyEntryParams<TItem> = {
  hasBorderBottom: boolean;
  isItemFocused: boolean;
  item: TItem;
  onHover?: () => unknown;
};

type RenderItemOnlyEntry<TItem> = (
  params: RenderItemOnlyEntryParams<TItem>
) => ReactElement;

type Props<TItem> = {
  className?: string;
  focusedItem?: TItem;
  getItemKey: (item: TItem) => number | string;
  itemOnlyEntryHasBorderBottom?: (
    item: TItem,
    index: number,
    items: TItem[]
  ) => boolean;
  items: TItem[];
  onItemHover?: (item: TItem) => unknown;
  renderItemOnlyEntry: RenderItemOnlyEntry<TItem>;
};

const ItemOnlyEntryList = <TItem,>({
  focusedItem,
  getItemKey,
  itemOnlyEntryHasBorderBottom,
  items,
  onItemHover,
  renderItemOnlyEntry
}: Props<TItem>) => (
  <ItemEntryListContainer>
    {items.map((item, index) => (
      <Fragment key={getItemKey(item)}>
        {renderItemOnlyEntry({
          hasBorderBottom:
            itemOnlyEntryHasBorderBottom !== undefined &&
            itemOnlyEntryHasBorderBottom(item, index, items),
          isItemFocused: item === focusedItem,
          item,
          onHover: () => onItemHover?.(item)
        })}
      </Fragment>
    ))}
  </ItemEntryListContainer>
);

export default ItemOnlyEntryList;

export type {
  Props as ItemOnlyEntryListProps,
  RenderItemOnlyEntryParams as RenderItemOnlyEntryParams
};
