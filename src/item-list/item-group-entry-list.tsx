import {Fragment} from "react";

import ItemEntryListContainer from "./item-entry-list-container";
import ItemGroupEntry from "./item-group-entry";
import type {ItemGroupEntryProps} from "./item-group-entry";
import type {ItemGroup} from "./types";

type ExposedItemGroupEntryProps<TItem> = Omit<
  ItemGroupEntryProps<TItem>,
  "itemGroup"
>;

type Props<TItem> = ExposedItemGroupEntryProps<TItem> & {
  itemGroupEntryHasBorderBottom?: (
    itemGroup: ItemGroup<TItem>,
    index: number,
    itemGroups: ItemGroup<TItem>[]
  ) => boolean;
  itemGroups: ItemGroup<TItem>[];
};

const ItemGroupEntryList = <TItem,>({
  itemGroupEntryHasBorderBottom,
  itemGroups,
  ...rest
}: Props<TItem>) => (
  <ItemEntryListContainer>
    {itemGroups.map((itemGroup, index) => (
      <Fragment key={itemGroup.name}>
        <ItemGroupEntry
          {...{itemGroup}}
          {...rest}
          hasBorderBottom={
            itemGroupEntryHasBorderBottom !== undefined &&
            itemGroupEntryHasBorderBottom(itemGroup, index, itemGroups)
          }
        />
      </Fragment>
    ))}
  </ItemEntryListContainer>
);

export default ItemGroupEntryList;

export type {Props as ItemGroupEntryListProps};
