import {forwardRef} from "react";
import type {ComponentRef, ForwardedRef, ReactElement, Ref} from "react";

import AnyItemList from "../any-item-list";
import type {
  AnyItemListProps,
  RenderItemOnlyEntryParams
} from "../any-item-list";

import SingleItemEntry from "./single-item-entry";
import type {SingleItemEntryProps} from "./single-item-entry";

type AnyItemListRefElement = ComponentRef<typeof AnyItemList>;

type RefElement = AnyItemListRefElement;

type ExposedSingleItemEntryProps<TItem> = Omit<
  SingleItemEntryProps<TItem>,
  keyof RenderItemOnlyEntryParams<TItem>
>;

type ExposedAnyItemListProps<TItem> = Omit<
  AnyItemListProps<TItem>,
  "getItemKey" | "renderItemOnlyEntry"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedAnyItemListProps<TItem>
  & ExposedSingleItemEntryProps<TItem>;

type SingleItemListComponent = <TItem>(
  props: Props<TItem> & {ref?: Ref<RefElement>}
) => ReactElement;

const SingleItemListRenderer = <TItem,>(
  {getItemLabel, isRequired, onChange, value, ...rest}: Props<TItem>,
  ref: ForwardedRef<RefElement>
) => (
  <AnyItemList
    {...{ref}}
    {...rest}
    getItemKey={getItemLabel}
    renderItemOnlyEntry={params => (
      <SingleItemEntry
        {...{getItemLabel, isRequired, onChange, value}}
        {...params}
      />
    )}
  />
);

const SingleItemList = forwardRef(
  SingleItemListRenderer
) as SingleItemListComponent;

export default Object.assign(SingleItemList, {
  changeValue: SingleItemEntry.changeValue,
  canChangeValue: SingleItemEntry.canChangeValue
});

export type {Props as SingleItemListProps};
