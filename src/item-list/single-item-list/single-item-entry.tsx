import styled from "styled-components";

import ItemLabelContainer from "../item-label-container";
import ItemOnlyEntryContainer from "../item-only-entry-container";

import CheckIcon from "./check-icon";

type Value<TItem> = TItem | null;

type Props<TItem> = {
  getItemLabel: (item: TItem) => string;
  hasBorderBottom?: boolean;
  isItemFocused?: boolean;
  isRequired?: boolean;
  item: TItem;
  onChange: (value: Value<TItem>) => unknown;
  value: Value<TItem>;
};

const StyledCheckIcon = styled(CheckIcon)`
  margin-left: 8px;
`;

const changeValue = <TItem,>(value: Value<TItem>, item: TItem) =>
  item === value ? null : item;

const canChangeValue = <TItem,>(
  value: Value<TItem>,
  item: TItem,
  isRequired: boolean
) => item !== value || !isRequired;

const SingleItemEntry = <TItem,>({
  getItemLabel,
  isItemFocused,
  isRequired = false,
  item,
  onChange,
  value,
  ...rest
}: Props<TItem>) => (
  <ItemOnlyEntryContainer
    {...rest}
    isFocused={isItemFocused}
    onClick={() => {
      if (canChangeValue(value, item, isRequired)) {
        onChange(changeValue(value, item));
      }
    }}
  >
    <ItemLabelContainer>{getItemLabel(item)}</ItemLabelContainer>
    {item === value && <StyledCheckIcon />}
  </ItemOnlyEntryContainer>
);

export default Object.assign(SingleItemEntry, {
  changeValue,
  canChangeValue
});

export type {Props as SingleItemEntryProps};
