import * as withArray from "@unlockre/utils-array/dist";
import {useMemo} from "react";

import useItemOnlyList from "../use-item-only-list";
import type {UseItemOnlyListResult} from "../use-item-only-list";

import type {TreeListItem} from "./types";
import useListTreeToggle from "./use-list-tree-toggle";
import type {UseListTreeToggleResult} from "./use-list-tree-toggle";

type OnParentChange<TItem> = (
  onChange: (items: TreeListItem<TItem>[]) => unknown,
  value: TreeListItem<TItem>[]
) => (item: TreeListItem<TItem>) => void;

type ItemsGroupedByParent<TItem> = Record<string, TreeListItem<TItem>[]>;

// prettier-ignore
type Result<TItem> =
  & UseItemOnlyListResult<TreeListItem<TItem>>
  & UseListTreeToggleResult<TItem>
  & {
      itemsGroupedByParent: ItemsGroupedByParent<TItem>;
      onParentChange: OnParentChange<TItem>;
    };

type Params<TItem> = {
  getItemLabel: (item: TreeListItem<TItem>) => string;
  items: TreeListItem<TItem>[];
  onItemSelect: (item: TreeListItem<TItem>, event: KeyboardEvent) => unknown;
};

const useTreeItemList = <TItem>({
  getItemLabel,
  items,
  onItemSelect
}: Params<TItem>): Result<TItem> => {
  const onRightArrow = (
    items: TreeListItem<TItem>[],
    setFocusedItem: (item: TreeListItem<TItem>) => unknown,
    focusedItemIndex: number
  ) => {
    const currentItem = items[focusedItemIndex];
    if (
      currentItem &&
      currentItem.hasChildren &&
      !toggledItems.includes(currentItem.id)
    ) {
      setToggledItems([...toggledItems, items[focusedItemIndex].id]);
    }
  };

  const onLeftArrow = (
    items: TreeListItem<TItem>[],
    setFocusedItem: (item: TreeListItem<TItem>) => unknown,
    focusedItemIndex: number
  ) => {
    const currentItem = items[focusedItemIndex];
    if (
      currentItem &&
      currentItem.hasChildren &&
      toggledItems.includes(currentItem.id)
    ) {
      setToggledItems(
        withArray.remove(toggledItems, toggledItems.indexOf(currentItem.id), 1)
      );
    }
  };

  const removeItemAndChildren = (
    selectedItem: TreeListItem<TItem>,
    currentValue: TreeListItem<TItem>[],
    onChange: (item: TreeListItem<TItem>[]) => unknown
  ) => {
    itemsGroupedByParent[selectedItem.id].forEach(item => {
      if (currentValue.includes(item)) {
        currentValue = withArray.remove(
          currentValue,
          currentValue.indexOf(item),
          1
        );
      }
    });
    onChange(
      withArray.remove(currentValue, currentValue.indexOf(selectedItem), 1)
    );
  };

  const addItemAndChildren = (
    selectedItem: TreeListItem<TItem>,
    currentValue: TreeListItem<TItem>[],
    onChange: (item: TreeListItem<TItem>[]) => unknown
  ) => {
    currentValue.push(selectedItem);
    itemsGroupedByParent[selectedItem.id].forEach(item => {
      !currentValue.includes(item) && currentValue.push(item);
    });
    onChange(currentValue);
  };

  const useListTreeToggleResult = useListTreeToggle<TItem>({
    items
  });

  const {setToggledItems, toggledItems} = useListTreeToggleResult;

  const useItemOnlyListResult = useItemOnlyList({
    customControls: {ArrowRight: onRightArrow, ArrowLeft: onLeftArrow},
    getItemLabel,
    items,
    onItemSelect
  });

  const itemsGroupedByParent = useMemo(
    () =>
      items.reduce<ItemsGroupedByParent<TItem>>((result, item) => {
        if (item.ancestorIds?.length) {
          item.ancestorIds.forEach(ancestorId => {
            result[ancestorId] = result[ancestorId]
              ? [...result[ancestorId], item]
              : [item];
          });
        }

        return result;
      }, {}),
    [items]
  );

  const onParentChange: OnParentChange<TItem> = (onChange, value) => item => {
    if (!itemsGroupedByParent[item.id]) {
      return;
    }

    const currentValue = [...value];

    if (value.includes(item)) {
      removeItemAndChildren(item, currentValue, onChange);
    } else {
      if (!toggledItems.includes(item.id)) {
        setToggledItems([...toggledItems, item.id]);
      }

      addItemAndChildren(item, currentValue, onChange);
    }
  };

  return {
    ...useItemOnlyListResult,
    ...useListTreeToggleResult,
    itemsGroupedByParent,
    onParentChange
  };
};

export default useTreeItemList;

export type {Params as UseTreeItemListParams, Result as UseTreeItemListResult};
