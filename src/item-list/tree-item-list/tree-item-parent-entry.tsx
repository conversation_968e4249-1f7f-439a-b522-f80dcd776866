import * as withArray from "@unlockre/utils-array/dist";
import {useEffect, useState} from "react";
import styled from "styled-components";

import Checkbox from "@/checkbox";
import {UnstyledButton} from "@/unstyled";

import VerticalChevronIcon from "../../select-field/vertical-chevron-icon";
import ItemOnlyEntryContainer from "../item-only-entry-container";

import type {TreeListItem} from "./types";

type Value<TItem> = TItem[];
type CheckedStatus = "empty" | "full" | "partial";

type Props<TItem> = {
  childrens?: TreeListItem<TItem>[];
  getItemLabel: (item: TreeListItem<TItem>) => string;
  hasBorderBottom?: boolean;
  isItemFocused?: boolean;
  isRequired?: boolean;
  isToggled?: boolean;
  item: TreeListItem<TItem>;
  onChange: (value: TreeListItem<TItem>[]) => unknown;
  onParentChange: (value: TreeListItem<TItem>) => unknown;
  onToggle: (id: string) => unknown;
  value: TreeListItem<TItem>[];
};

type StyledProps = {
  $ancestorsCount: number;
};
const changeValue = <TItem,>(value: Value<TItem>, item: TItem) =>
  value.includes(item)
    ? withArray.remove(value, value.indexOf(item), 1)
    : [...value, item];

const canChangeValue = <TItem,>(
  value: Value<TItem>,
  item: TItem,
  isRequired: boolean
) => value.length > 1 || !value.includes(item) || !isRequired;

const StyledItemEntryContainer = styled(ItemOnlyEntryContainer)<StyledProps>`
  align-items: center;
  justify-content: flex-start;
  padding-left: ${params => `${16 + 12 * params.$ancestorsCount}px`};
`;

const EntryCheckboxContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
`;

const ToggleButtonContainer = styled.div`
  margin-right: 20px;
`;

const StyledToggleButton = styled(UnstyledButton)`
  width: 42px;
  height: 42px;
`;

const doNothing = () => {};

const TreeItemParentEntry = <TItem,>({
  childrens,
  getItemLabel,
  isItemFocused,
  isRequired = false,
  isToggled,
  item,
  onChange,
  onParentChange,
  onToggle,
  value,
  ...rest
}: Props<TItem>) => {
  const [checkedStatus, setCheckedStatus] = useState<CheckedStatus>("empty");

  useEffect(() => {
    if (!value.includes(item)) {
      setCheckedStatus("empty");
      return;
    }
    const allChildrenIncluded = childrens?.every(child =>
      value.includes(child)
    );
    setCheckedStatus(allChildrenIncluded ? "full" : "partial");
  }, [value, childrens, item]);

  return (
    <StyledItemEntryContainer
      {...rest}
      $ancestorsCount={item.ancestorIds ? item.ancestorIds.length : 0}
      isFocused={isItemFocused}
      onClick={() => {
        if (canChangeValue(value, item, isRequired)) {
          onParentChange(item);
        }
      }}
    >
      <EntryCheckboxContainer>
        <Checkbox
          isChecked={checkedStatus === "full" || checkedStatus === "partial"}
          isIndeterminated={checkedStatus === "partial"}
          label={getItemLabel(item)}
          onChange={doNothing}
          size="small"
        />
      </EntryCheckboxContainer>
      <ToggleButtonContainer>
        <StyledToggleButton
          onClick={event => {
            event.stopPropagation();
            onToggle(item.id);
          }}
          type="button"
        >
          <VerticalChevronIcon direction={isToggled ? "up" : "down"} />
        </StyledToggleButton>
      </ToggleButtonContainer>
    </StyledItemEntryContainer>
  );
};

export default Object.assign(TreeItemParentEntry, {
  changeValue,
  canChangeValue
});

export type {Props as TreeItemEntryProps};
