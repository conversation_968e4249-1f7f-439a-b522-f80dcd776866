import type {ReactElement} from "react";
import styled from "styled-components";

import MultiItemEntry from "../multi-item-list/multi-item-entry";
import type {MultiItemEntryProps} from "../multi-item-list/multi-item-entry";

import type {TreeListItem} from "./types";

type Props<TItem> = {
  getItemLabel: (item: TreeListItem<TItem>) => string;
  hasBorderBottom?: boolean;
  isItemFocused?: boolean;
  isRequired?: boolean;
  item: TreeListItem<TItem>;
  onChange: (value: TreeListItem<TItem>[]) => unknown;
  value: TreeListItem<TItem>[];
};

type NestedMultiItemEntryStyledProps = {
  $ancestorsCount: number;
};

type NestedMultiItemEntryProps<TItem> = MultiItemEntryProps<TItem> &
  NestedMultiItemEntryStyledProps;

type NestedMultiItemEntryComponent = <TValue>(
  props: NestedMultiItemEntryProps<TValue>
) => ReactElement;

const NestedMultiItemEntry = styled(
  MultiItemEntry
)<NestedMultiItemEntryStyledProps>`
  padding-left: ${params => `${params.$ancestorsCount * 28}px`};
` as NestedMultiItemEntryComponent;

const TreeItemChildEntry = <TItem,>({item, ...otherProps}: Props<TItem>) =>
  item.ancestorIds?.length ? (
    <NestedMultiItemEntry
      item={item}
      {...otherProps}
      $ancestorsCount={item.ancestorIds.length}
    />
  ) : (
    <MultiItemEntry item={item} {...otherProps} />
  );

export default TreeItemChildEntry;
