import * as withArray from "@unlockre/utils-array/dist";
import {useMemo, useState} from "react";
import type {Dispatch, SetStateAction} from "react";

import type {TreeListItem} from "@/item-list/tree-item-list/types";

type Result<TItem> = {
  setToggledItems: Dispatch<SetStateAction<string[]>>;
  toggleItem: (itemId: string) => void;
  toggledItems: string[];
  visibleItems: TreeListItem<TItem>[];
};

type Params<TItem> = {
  items: TreeListItem<TItem>[];
};

const useListTreeToggle = <TItem>({items}: Params<TItem>): Result<TItem> => {
  const [toggledItems, setToggledItems] = useState<string[]>([]);

  const visibleItems = useMemo(
    () =>
      items.filter(
        item =>
          !item.ancestorIds ||
          item.ancestorIds.every(ancestor => toggledItems.includes(ancestor))
      ),
    [toggledItems, items]
  );

  const toggleItem = (itemId: string) => {
    setToggledItems(
      toggledItems.includes(itemId)
        ? withArray.remove(toggledItems, toggledItems.indexOf(itemId), 1)
        : [...toggledItems, itemId]
    );
  };

  return {visibleItems, toggleItem, toggledItems, setToggledItems};
};

export default useListTreeToggle;

export type {Result as UseListTreeToggleResult};
