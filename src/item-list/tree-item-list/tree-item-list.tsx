import {forwardRef} from "react";
import type {ComponentRef, ForwardedRef, ReactElement, Ref} from "react";

import AnyItemOnlyList from "../any-item-only-list";
import type {
  AnyItemOnlyListProps,
  RenderItemOnlyEntryParams
} from "../any-item-only-list";

import TreeItemChildEntry from "./tree-item-child-entry";
import TreeItemEntry from "./tree-item-parent-entry";
import type {TreeItemEntryProps} from "./tree-item-parent-entry";
import type {TreeListItem} from "./types";

type AnyItemOnlyListRefElement = ComponentRef<typeof AnyItemOnlyList>;

type RefElement = AnyItemOnlyListRefElement;

type ExposedTreeItemEntryProps<TItem> = Omit<
  TreeItemEntryProps<TItem>,
  keyof RenderItemOnlyEntryParams<TItem> | "isIndeterminate" | "isToggled"
>;

type ExposedAnyItemOnlyList<TItem> = Omit<
  AnyItemOnlyListProps<TItem>,
  "focusedItem" | "getItemKey" | "items" | "onItemHover" | "renderItemOnlyEntry"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedAnyItemOnlyList<TItem>
  & ExposedTreeItemEntryProps<TItem>
  & {
      focusedItem?: TreeListItem<TItem>;
      groupedItems: Record<string, TreeListItem<TItem>[]>;
      items: TreeListItem<TItem>[];
      onItemHover?: (item: TreeListItem<TItem>) => unknown;
      onParentChange: (item: TreeListItem<TItem>) => unknown;
      onToggle: (id: string) => unknown;
      toggledItemIds: string[];
    };

type TreeItemListComponent = <TItem>(
  props: Props<TItem> & {ref?: Ref<RefElement>}
) => ReactElement;

const TreeItemListRenderer = <TItem,>(
  {
    className,
    focusedItem,
    footer,
    getItemLabel,
    groupedItems,
    items,
    onItemHover,
    onParentChange,
    onToggle,
    style,
    toggledItemIds,
    ...rest
  }: Props<TItem>,
  ref: ForwardedRef<RefElement>
) => (
  <AnyItemOnlyList
    {...{className, focusedItem, footer, items, onItemHover, ref, style}}
    getItemKey={getItemLabel}
    renderItemOnlyEntry={params => {
      if (params.item.hasChildren) {
        return (
          <TreeItemEntry
            {...{getItemLabel}}
            {...rest}
            {...params}
            childrens={groupedItems[params.item.id]}
            isToggled={toggledItemIds.includes(params.item.id)}
            onParentChange={onParentChange}
            onToggle={onToggle}
          />
        );
      }

      return <TreeItemChildEntry {...{getItemLabel}} {...rest} {...params} />;
    }}
  />
);

const TreeItemList = forwardRef(TreeItemListRenderer) as TreeItemListComponent;

export default Object.assign(TreeItemList, {
  changeValue: TreeItemEntry.changeValue,
  canChangeValue: TreeItemEntry.canChangeValue
});

export type {Props as TreeItemListProps};
