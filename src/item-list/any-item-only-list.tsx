import {forwardRef} from "react";
import type {CSSProperties, ForwardedRef, ReactElement, Ref} from "react";

import ItemListContainer from "./item-list-container";
import type {ItemListContainerRefElement} from "./item-list-container";
import ItemOnlyEntryList from "./item-only-entry-list";
import type {ItemOnlyEntryListProps} from "./item-only-entry-list";

type ExposedItemEntryListProps<TItem> = Omit<
  ItemOnlyEntryListProps<TItem>,
  "itemOnlyEntryHasBorderBottom"
>;

type Props<TItem> = ExposedItemEntryListProps<TItem> & {
  footer?: ReactElement;
  style?: CSSProperties;
};

type RefElement = ItemListContainerRefElement;

type AnyItemOnlyListComponent = <TItem>(
  props: Props<TItem> & {ref?: Ref<RefElement>}
) => ReactElement;

const hasFooterOrItemIsNotLast =
  <TItem,>(hasFooter: boolean) =>
  (item: TItem, index: number, items: TItem[]) =>
    index !== items.length - 1 || hasFooter;

const AnyItemOnlyListRenderer = <TItem,>(
  {className, footer, style, ...itemOnlyEntryListProps}: Props<TItem>,
  ref: ForwardedRef<RefElement>
) => (
  <ItemListContainer {...{className, ref, style}}>
    <ItemOnlyEntryList
      {...itemOnlyEntryListProps}
      itemOnlyEntryHasBorderBottom={hasFooterOrItemIsNotLast(
        footer !== undefined
      )}
    />
    {footer}
  </ItemListContainer>
);

const AnyItemOnlyList = forwardRef(
  AnyItemOnlyListRenderer
) as AnyItemOnlyListComponent;

export default AnyItemOnlyList;

export type {Props as AnyItemOnlyListProps};

export type {RenderItemOnlyEntryParams} from "./item-only-entry-list";
