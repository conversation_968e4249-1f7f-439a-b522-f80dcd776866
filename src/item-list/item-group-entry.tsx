import styled, {css} from "styled-components";

import * as withTheme from "@/theme-provider/theme";

import ItemListContainer from "./item-list-container";
import ItemOnlyEntryList from "./item-only-entry-list";
import type {ItemOnlyEntryListProps} from "./item-only-entry-list";
import type {ItemGroup} from "./types";

type ExposedItemOnlyEntryListProps<TItem> = Omit<
  ItemOnlyEntryListProps<TItem>,
  "className" | "itemEntryHasBorderBottom" | "items"
>;

type Props<TItem> = ExposedItemOnlyEntryListProps<TItem> & {
  hasBorderBottom?: boolean;
  itemGroup: ItemGroup<TItem>;
};

type ContainerStyledProps = {
  $hasBorderBottom?: boolean;
};

const ItemGroupLabelContainer = styled.div`
  ${withTheme.getTypography("body", "xs")}

  color: ${withTheme.getColorByAlias("textSecondary")};
  padding: 16px;
`;

const renderItemGroupLabel = (itemGroupLabel: string) => (
  <ItemGroupLabelContainer>{itemGroupLabel}</ItemGroupLabelContainer>
);

const renderItemGroupLabelIfNeeded = <TItem,>({label}: ItemGroup<TItem>) =>
  label && renderItemGroupLabel(label);

const containerBorderBottomCss = css`
  border-bottom: 1px solid ${ItemListContainer.getBorderColor};
`;

const Container = styled.li<ContainerStyledProps>`
  ${props => props.$hasBorderBottom && containerBorderBottomCss}
`;

const ItemGroupEntry = <TItem,>({
  hasBorderBottom,
  itemGroup,
  ...rest
}: Props<TItem>) => (
  <Container $hasBorderBottom={hasBorderBottom}>
    {renderItemGroupLabelIfNeeded(itemGroup)}
    <ItemOnlyEntryList {...rest} items={itemGroup.items} />
  </Container>
);

export default ItemGroupEntry;

export type {Props as ItemGroupEntryProps};
