import {ensureIsDefined} from "@unlockre/utils-validation/dist";
import {forwardRef} from "react";
import type {CSSProperties, ForwardedRef, ReactElement, Ref} from "react";

import ItemGroupEntryList from "./item-group-entry-list";
import type {ItemGroupEntryListProps} from "./item-group-entry-list";
import ItemListContainer from "./item-list-container";
import type {ItemListContainerRefElement} from "./item-list-container";
import ItemOnlyEntryList from "./item-only-entry-list";
import type {ItemOnlyEntryListProps} from "./item-only-entry-list";
import type {ItemGroup} from "./types";

type ExposedItemOnlyEntryListProps<TItem> = Omit<
  ItemOnlyEntryListProps<TItem>,
  "itemOnlyEntryHasBorderBottom" | "items"
>;

type ExposedItemGroupEntryListProps<TItem> = Omit<
  ItemGroupEntryListProps<TItem>,
  "itemGroups"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedItemGroupEntryListProps<TItem>
  & ExposedItemOnlyEntryListProps<TItem>
  & {
      footer?: ReactElement;
      itemGroups?: ItemGroup<TItem>[];
      items?: TItem[];
      style?: CSSProperties;
    };

type RefElement = ItemListContainerRefElement;

type AnyItemListComponent = <TItem>(
  props: Props<TItem> & {ref?: Ref<RefElement>}
) => ReactElement;

const hasFooterOrItemIsNotLast =
  <TItem,>(hasFooter: boolean) =>
  (item: TItem, index: number, items: TItem[]) =>
    index !== items.length - 1 || hasFooter;

const renderItemOnlyEntryList = <TItem,>({
  focusedItem,
  footer,
  getItemKey,
  items,
  onItemHover,
  renderItemOnlyEntry
}: Props<TItem>) => (
  <ItemOnlyEntryList
    {...{
      focusedItem,
      getItemKey,
      onItemHover,
      renderItemOnlyEntry
    }}
    itemOnlyEntryHasBorderBottom={hasFooterOrItemIsNotLast(
      footer !== undefined
    )}
    items={ensureIsDefined(items, "No items")}
  />
);

const hasFooterOrItemGroupIsNotLast =
  <TItem,>(hasFooter: boolean) =>
  (
    itemGroup: ItemGroup<TItem>,
    index: number,
    itemGroups: ItemGroup<TItem>[]
  ) =>
    index !== itemGroups.length - 1 || hasFooter;

const renderItemGroupEntryList = <TItem,>({
  focusedItem,
  footer,
  getItemKey,
  itemGroups,
  onItemHover,
  renderItemOnlyEntry
}: Props<TItem>) => (
  <ItemGroupEntryList
    {...{
      focusedItem,
      getItemKey,
      onItemHover,
      renderItemOnlyEntry
    }}
    itemGroupEntryHasBorderBottom={hasFooterOrItemGroupIsNotLast(
      footer !== undefined
    )}
    itemGroups={ensureIsDefined(itemGroups, "No item groups")}
  />
);

const ensureOnlyOneProp = <TItem,>(props: Props<TItem>) => {
  if (props.items && props.itemGroups) {
    throw new Error(
      "Expected either items or itemGroups prop, but both were given"
    );
  }

  return props;
};

const AnyItemListRenderer = <TItem,>(
  props: Props<TItem>,
  ref: ForwardedRef<RefElement>
) => (
  <ItemListContainer {...{ref}} className={props.className} style={props.style}>
    {ensureOnlyOneProp(props).itemGroups
      ? renderItemGroupEntryList(props)
      : renderItemOnlyEntryList(props)}
    {props.footer}
  </ItemListContainer>
);

const AnyItemList = forwardRef(AnyItemListRenderer) as AnyItemListComponent;

export default AnyItemList;

export type {Props as AnyItemListProps};

export type {RenderItemOnlyEntryParams} from "./item-only-entry-list";
