import {useMemo} from "react";

import type {ItemGroup} from "./types";
import useItemOnlyList from "./use-item-only-list";
import type {
  UseItemOnlyListParams,
  UseItemOnlyListResult
} from "./use-item-only-list";

type CommonItemListProps<TItem> = Omit<
  UseItemOnlyListResult<TItem>["itemListProps"],
  "items"
>;

type ItemListProps<TItem> = CommonItemListProps<TItem> & {
  itemGroups?: ItemGroup<TItem>[];
  items?: TItem[];
};

type CommonResult<TItem> = Omit<UseItemOnlyListResult<TItem>, "itemListProps">;

type Result<TItem> = CommonResult<TItem> & {
  itemListProps: ItemListProps<TItem>;
};

type CommonParams<TItem> = Omit<
  UseItemOnlyListParams<TItem>,
  "customControls" | "items"
>;

type Params<TItem> = CommonParams<TItem> & {
  itemGroups?: ItemGroup<TItem>[];
  items?: TItem[];
};

const useItemList = <TItem>({itemGroups, items, ...rest}: Params<TItem>) => {
  const itemGroupsItems = useMemo(
    () => itemGroups && itemGroups.flatMap(curItemGroup => curItemGroup.items),
    [itemGroups]
  );

  const {
    itemListProps: {items: consolidatedItems, ...otherItemOnlyListProps},
    ...otherResult
  } = useItemOnlyList({
    items: itemGroupsItems || items || [],
    ...rest
  });

  return {
    itemListProps: {itemGroups, items, ...otherItemOnlyListProps},
    ...otherResult
  };
};

export default useItemList;

export type {Params as UseItemListParams, Result as UseItemListResult};
