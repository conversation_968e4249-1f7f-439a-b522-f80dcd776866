import colorAlpha from "color-alpha";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

// We should be using ComponentRef to get this one, but it doesn't work well
// with styled components
type ItemListContainerRefElement = HTMLDivElement;

const containerBorderRadius = 10;

const getBorderColor = getColor("gray", "100");

const getBackgroundColor = getColor("gray", "000");

const ItemListContainer = styled.div`
  background-color: ${getBackgroundColor};
  border: 1px solid ${getBorderColor};
  border-radius: ${containerBorderRadius}px;
  box-shadow: 0px 6px 16px
    ${props => colorAlpha(getColor("gray", "040")(props), 0.4)};
  padding: ${containerBorderRadius}px 0;
  z-index: 1000;
`;

export default Object.assign(ItemListContainer, {
  getBackgroundColor,
  getBorderColor
});

export type {ItemListContainerRefElement};
