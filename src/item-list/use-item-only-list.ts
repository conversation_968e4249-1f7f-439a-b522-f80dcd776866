import {autoUpdate, flip, offset, useFloating} from "@floating-ui/react-dom";
import {useCallback, useState} from "react";
import type {ComponentRef, ReactElement, Ref} from "react";

import type AnyItemOnlyList from "./any-item-only-list";
import type {AnyItemOnlyListProps} from "./any-item-only-list";
import useItemListClickOutside from "./use-item-list-click-outside";
import useItemOnlyListControl from "./use-item-only-list-control";
import type {UseItemOnlyListControlParams} from "./use-item-only-list-control";

type ControlElement = HTMLElement;

type SetControlElement = (controlElement: ControlElement | null) => unknown;

type AnchorageElement = HTMLElement;

type SetAnchorageElement = (
  anchorageElement: AnchorageElement | null
) => unknown;

type ItemOnlyListElement = ComponentRef<typeof AnyItemOnlyList>;

type ItemOnlyListRef = Ref<ItemOnlyListElement>;

type ToggleItemList = () => void;

type ExposedItemOnlyListProps<TItem> = Pick<
  AnyItemOnlyListProps<TItem>,
  "focusedItem" | "footer" | "items" | "onItemHover" | "style"
>;

type ProvidedItemOnlyListProps<TItem> = ExposedItemOnlyListProps<TItem> & {
  ref: ItemOnlyListRef;
};

type Result<TItem> = {
  isItemListOpened: boolean;
  itemListProps: ProvidedItemOnlyListProps<TItem>;
  setAnchorageElement: SetAnchorageElement;
  setControlElement: SetControlElement;
  toggleItemList: ToggleItemList;
};

type ItemListElement = ItemOnlyListElement;

type RenderFooterParams = {
  toggleItemList: ToggleItemList;
};

type ExposedUseItemListControlParams<TItem> = Pick<
  UseItemOnlyListControlParams<TItem>,
  "customControls" | "getItemLabel" | "onItemSelect"
>;

type Params<TItem> = ExposedUseItemListControlParams<TItem> & {
  items: TItem[];
  renderFooter?: (params: RenderFooterParams) => ReactElement;
};

const useItemOnlyList = <TItem>({
  customControls,
  getItemLabel,
  items,
  onItemSelect,
  renderFooter
}: Params<TItem>): Result<TItem> => {
  const [isItemListOpened, setIsItemListOpened] = useState(false);

  const toggleItemList = useCallback(
    () => setIsItemListOpened(currentState => !currentState),
    [setIsItemListOpened]
  );

  const {focusedItem, setControlElement, setFocusedItem} =
    useItemOnlyListControl({
      customControls,
      getItemLabel,
      isItemListOpened,
      items,
      onItemListToggle: toggleItemList,
      onItemSelect
    });

  const {
    floating,
    reference,
    refs,
    strategy: position,
    x,
    y
  } = useFloating({
    placement: "bottom-start",
    middleware: [flip(), offset(4)],
    strategy: "fixed",
    whileElementsMounted: autoUpdate
  });

  const {setAnchorageElement, setItemListElement} = useItemListClickOutside({
    isItemListOpened,
    onItemListClose: toggleItemList
  });

  const handleItemListRef = useCallback(
    (itemListElement: ItemListElement | null) => {
      floating(itemListElement);

      setItemListElement(itemListElement);
    },
    [floating, setItemListElement]
  );

  const footer = renderFooter?.({toggleItemList});

  const itemListProps = {
    focusedItem,
    footer,
    items,
    onItemHover: setFocusedItem,
    ref: handleItemListRef,
    style: {
      position,
      top: y ?? undefined,
      left: x ?? undefined,
      minWidth: refs.reference.current?.getBoundingClientRect().width
    }
  };

  const setAllAnchorageElements = useCallback(
    (anchorageElement: HTMLElement | null) => {
      setAnchorageElement(anchorageElement);

      reference(anchorageElement);
    },
    [setAnchorageElement, reference]
  );

  return {
    isItemListOpened,
    itemListProps,
    setAnchorageElement: setAllAnchorageElements,
    setControlElement,
    toggleItemList
  };
};

export default useItemOnlyList;

export type {
  Params as UseItemOnlyListParams,
  Result as UseItemOnlyListResult,
  SetAnchorageElement,
  SetControlElement
};
