import {useEffect, useState} from "react";

type KeyDownHandler = (event: KeyboardEvent) => unknown;

const useKeyDown = <THTMLElement extends HTMLElement>(
  onKeyDown: KeyDownHandler
) => {
  const [element, setElement] = useState<THTMLElement | null>(null);

  useEffect(() => {
    if (!element) {
      return;
    }

    element.addEventListener("keydown", onKeyDown);

    return () => {
      element.removeEventListener("keydown", onKeyDown);
    };
  }, [element, onKeyDown]);

  return setElement;
};

export default useKeyDown;
