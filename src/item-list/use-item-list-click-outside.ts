import {useClickOutside} from "@unlockre/utils-react/dist";
import {useCallback, useState} from "react";

import type {ItemListContainerRefElement} from "./item-list-container";

type AnchorageElement = HTMLElement;

type Params = {
  isItemListOpened: boolean;
  onItemListClose: () => unknown;
};

const useItemListClickOutside = ({
  isItemListOpened,
  onItemListClose
}: Params) => {
  const [anchorageElement, setAnchorageElement] =
    useState<AnchorageElement | null>(null);

  const [itemListElement, setItemListElement] =
    useState<ItemListContainerRefElement | null>(null);

  const shouldCloseItemList = useCallback(
    (mouseEvent: MouseEvent) =>
      !anchorageElement?.contains(mouseEvent.target as Node | null),
    [anchorageElement]
  );

  const handleClickOutside = useCallback(
    (mouseEvent: MouseEvent) => {
      if (isItemListOpened && shouldCloseItemList(mouseEvent)) {
        onItemListClose();
      }
    },
    [isItemListOpened, onItemListClose, shouldCloseItemList]
  );

  useClickOutside(itemListElement, handleClickOutside);

  return {setAnchorageElement, setItemListElement};
};

export default useItemListClickOutside;
