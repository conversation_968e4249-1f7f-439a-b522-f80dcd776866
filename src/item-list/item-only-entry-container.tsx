import {useEffect} from "react";
import type {ComponentProps} from "react";
import {useInView} from "react-intersection-observer";
import styled, {css} from "styled-components";

import {getColor} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import ItemListContainer from "./item-list-container";

type UnstyledButtonProps = ComponentProps<typeof UnstyledButton>;

type ExposedUnstyledButtonProps = Pick<
  UnstyledButtonProps,
  "children" | "className" | "onClick"
>;

type Props = ExposedUnstyledButtonProps & {
  hasBorderBottom?: boolean;
  isFocused?: boolean;
  onHover?: () => unknown;
};

type ContainerStyledProps = {
  $hasBorderBottom?: boolean;
  $isFocused?: boolean;
};

const getFocusedBackgroundColor = getColor("gray", "040");

const containerBorderBottomCss = css`
  border-bottom: 1px solid ${ItemListContainer.getBorderColor};
`;

const StyledButton = styled(UnstyledButton)<ContainerStyledProps>`
  ${props => props.$hasBorderBottom && containerBorderBottomCss}

  background-color: ${props =>
    props.$isFocused
      ? getFocusedBackgroundColor
      : ItemListContainer.getBackgroundColor};
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  width: 100%;
`;

const ItemOnlyEntryContainer = ({
  hasBorderBottom,
  isFocused,
  onHover,
  ...rest
}: Props) => {
  const {entry, inView, ref} = useInView({threshold: 0.5});

  const liElement = entry?.target;

  useEffect(() => {
    if (liElement && isFocused && !inView) {
      liElement.scrollIntoView({block: "nearest"});
    }
  }, [liElement, isFocused, inView]);

  return (
    <li {...{ref}} onMouseEnter={onHover}>
      <StyledButton
        {...rest}
        $hasBorderBottom={hasBorderBottom}
        $isFocused={isFocused}
        type="button"
      />
    </li>
  );
};

export default ItemOnlyEntryContainer;
