import {forwardRef} from "react";
import type {ComponentRef, ForwardedRef, ReactElement, Ref} from "react";

import AnyItemList from "../any-item-list";
import type {
  AnyItemListProps,
  RenderItemOnlyEntryParams
} from "../any-item-list";

import MultiItemEntry from "./multi-item-entry";
import type {MultiItemEntryProps} from "./multi-item-entry";

type AnyItemListRefElement = ComponentRef<typeof AnyItemList>;

type RefElement = AnyItemListRefElement;

type ExposedMultiItemEntryProps<TItem> = Omit<
  MultiItemEntryProps<TItem>,
  keyof RenderItemOnlyEntryParams<TItem>
>;

type ExposedAnyItemListProps<TItem> = Omit<
  AnyItemListProps<TItem>,
  "getItemKey" | "renderItemOnlyEntry"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedAnyItemListProps<TItem>
  & ExposedMultiItemEntryProps<TItem>;

type MultiItemListComponent = <TItem>(
  props: Props<TItem> & {ref?: Ref<RefElement>}
) => ReactElement;

const MultiItemListRenderer = <TItem,>(
  {getItemLabel, isRequired, onChange, value, ...rest}: Props<TItem>,
  ref: ForwardedRef<RefElement>
) => (
  <AnyItemList
    {...{ref}}
    {...rest}
    getItemKey={getItemLabel}
    renderItemOnlyEntry={params => (
      <MultiItemEntry
        {...{getItemLabel, isRequired, onChange, value}}
        {...params}
      />
    )}
  />
);

const MultiItemList = forwardRef(
  MultiItemListRenderer
) as MultiItemListComponent;

export default Object.assign(MultiItemList, {
  changeValue: MultiItemEntry.changeValue,
  canChangeValue: MultiItemEntry.canChangeValue
});

export type {Props as MultiItemListProps};
