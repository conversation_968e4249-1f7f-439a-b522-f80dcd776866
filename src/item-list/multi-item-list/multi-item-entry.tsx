import * as withArray from "@unlockre/utils-array/dist";
import styled from "styled-components";

import Checkbox from "@/checkbox";

import ItemOnlyEntryContainer from "../item-only-entry-container";

type Value<TItem> = TItem[];

type Props<TItem> = {
  className?: string | undefined;
  getItemLabel: (item: TItem) => string;
  hasBorderBottom?: boolean;
  isItemFocused?: boolean;
  isRequired?: boolean;
  item: TItem;
  onChange: (value: Value<TItem>) => unknown;
  value: Value<TItem>;
};

const changeValue = <TItem,>(value: Value<TItem>, item: TItem) =>
  value.includes(item)
    ? withArray.remove(value, value.indexOf(item), 1)
    : [...value, item];

const canChangeValue = <TItem,>(
  value: Value<TItem>,
  item: TItem,
  isRequired: boolean
) => value.length > 1 || !value.includes(item) || !isRequired;

const Container = styled(ItemOnlyEntryContainer)`
  align-items: center;
  justify-content: flex-start;
`;

const doNothing = () => {};

const MultiItemEntry = <TItem,>({
  getItemLabel,
  isItemFocused,
  isRequired = false,
  item,
  onChange,
  value,
  ...rest
}: Props<TItem>) => (
  <Container
    {...rest}
    isFocused={isItemFocused}
    onClick={() => {
      if (canChangeValue(value, item, isRequired)) {
        onChange(changeValue(value, item));
      }
    }}
  >
    <Checkbox
      isChecked={value.includes(item)}
      label={getItemLabel(item)}
      onChange={doNothing}
      size="small"
    />
  </Container>
);

export default Object.assign(MultiItemEntry, {changeValue, canChangeValue});

export type {Props as MultiItemEntryProps};
