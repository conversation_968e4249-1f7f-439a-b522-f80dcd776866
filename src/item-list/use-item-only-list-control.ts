import {useEffect, useState} from "react";

import useKeyDown from "./use-key-down";

type GetItemLabel<TItem> = (item: TItem) => string;

type CustomControls<TItem> = Record<
  string,
  (
    items: TItem[],
    setFocusedItem: (item: TItem) => unknown,
    focusedItemIndex: number
  ) => unknown
>;

type Params<TItem> = {
  customControls?: CustomControls<TItem>;
  getItemLabel: (item: TItem) => string;
  isItemListOpened: boolean;
  items: TItem[];
  onItemListToggle: () => unknown;
  onItemSelect: (item: TItem, event: KeyboardEvent) => unknown;
};

type SplitByFirstLetterResult = {
  after: number[];
  before: number[];
};

type SplitByFirstLetterParams<TItem> = {
  firstLetter: string;
  focusedItemIndex: number;
  getItemLabel: GetItemLabel<TItem>;
  items: TItem[];
};

const splitByFirstLetter = <TItem>({
  firstLetter,
  focusedItemIndex,
  getItemLabel,
  items
}: SplitByFirstLetterParams<TItem>) =>
  items.reduce<SplitByFirstLetterResult>(
    (result, item, index) => {
      const startsWithFirstLetter = getItemLabel(item)
        .toLowerCase()
        .startsWith(firstLetter);

      if (index === focusedItemIndex || !startsWithFirstLetter) {
        return result;
      }

      return index > focusedItemIndex
        ? {...result, after: [...result.after, index]}
        : {...result, before: [...result.before, index]};
    },
    {before: [], after: []}
  );

const isLetterRegExp = /^[a-zA-Z]$/;

const isLetter = (str: string) => isLetterRegExp.test(str);

const oobFocusedItemIndex = -1;

// eslint-disable-next-line max-statements
const useItemOnlyListControl = <TItem>({
  customControls,
  getItemLabel,
  isItemListOpened,
  items,
  onItemListToggle,
  onItemSelect
}: Params<TItem>) => {
  const [focusedItemIndex, setFocusedItemIndex] =
    useState<number>(oobFocusedItemIndex);

  const setFocusedItem = (focusedItem?: TItem) =>
    setFocusedItemIndex(
      focusedItem === undefined
        ? oobFocusedItemIndex
        : items.indexOf(focusedItem)
    );

  const onLetter = (letter: string) => {
    const {after, before} = splitByFirstLetter({
      firstLetter: letter,
      focusedItemIndex,
      getItemLabel,
      items
    });

    if (after.length > 0) {
      setFocusedItemIndex(after[0]);
    } else if (before.length > 0) {
      setFocusedItemIndex(before[0]);
    }
  };

  const onOtherKey = (event: KeyboardEvent) => {
    if (isItemListOpened && isLetter(event.key)) {
      onLetter(event.key);
    }
  };

  const closeItemList = () => {
    if (isItemListOpened) {
      onItemListToggle();
    }
  };

  const onEnter = (event: KeyboardEvent) => {
    if (focusedItemIndex > oobFocusedItemIndex) {
      onItemSelect(items[focusedItemIndex], event);
    }
  };

  const getPrevFocusedItemIndex = (focusedItemIndex: number) =>
    Math.max(focusedItemIndex - 1, 0);

  const onArrowUp = (event: KeyboardEvent) => {
    event.preventDefault();

    if (isItemListOpened) {
      setFocusedItemIndex(getPrevFocusedItemIndex);
    }
  };

  const getNextFocusedItemIndex = (focusedItemIndex: number) =>
    Math.min(focusedItemIndex + 1, items.length - 1);

  const onArrowDown = (event: KeyboardEvent) => {
    event.preventDefault();

    if (!isItemListOpened) {
      onItemListToggle();
    } else {
      setFocusedItemIndex(getNextFocusedItemIndex);
    }
  };

  const resetFocusedItemIndex = () => {
    if (focusedItemIndex > oobFocusedItemIndex) {
      setFocusedItemIndex(oobFocusedItemIndex);
    }
  };

  useEffect(() => {
    if (isItemListOpened) {
      setFocusedItemIndex(getNextFocusedItemIndex);
    } else {
      resetFocusedItemIndex();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isItemListOpened]);

  // eslint-disable-next-line complexity
  const setControlElement = useKeyDown(event => {
    if (customControls && customControls[event.key]) {
      return customControls[event.key](items, setFocusedItem, focusedItemIndex);
    }
    switch (event.key) {
      case "ArrowDown":
        onArrowDown(event);
        break;

      case "ArrowUp":
        onArrowUp(event);
        break;

      case "Enter":
        onEnter(event);
        break;

      case "Escape":
        closeItemList();
        break;

      case "Tab":
        closeItemList();
        break;

      default:
        onOtherKey(event);
        break;
    }
  });

  const focusedItem =
    focusedItemIndex > oobFocusedItemIndex
      ? items[focusedItemIndex]
      : undefined;

  return {
    focusedItem,
    setControlElement,
    setFocusedItem
  };
};

export default useItemOnlyListControl;

export type {Params as UseItemOnlyListControlParams, CustomControls};
