# Number Range Field

## About

This component allows users to pick a number between two of them (From, To).

## Example

```tsx
import {useState} from "react";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

import type {NumberRage} from "@unlockre/components-library/dist/number-range-field";

import NumberRangeField, {NumberSymbol} from "@unlockre/components-library/dist/number-range-field";

const MyApp = () => {
  const [numberRange, setNumberRange] = useState<NumberRage | null>(null);

  return (
    <ThemeProvider>
      <NumberRangeField
        allowNegative={false}
        decimalScale={2}
        errorMessageFrom="Greater than number to"
        errorMessageTo="Smaller than number from"
        left={<NumberSymbol>$</NumberSymbol>}
        onChange={setNumberRange}
        placeholderFrom="From"
        placeholderTo="To"
        thousandSeparator
        value={numberRange}
      />
    </ThemeProvider>
  );
};

export default MyApp;
```
