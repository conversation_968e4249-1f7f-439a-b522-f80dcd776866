import type {ComponentProps} from "react";
import styled from "styled-components";

import {RangeFieldContainer} from "@/field";
import NumberField from "@/number-field";

type NumberRange = {
  numberFrom: number | null;
  numberTo: number | null;
};

type NumberFieldProps = ComponentProps<typeof NumberField>;

type RangeFieldContainerProps = ComponentProps<typeof RangeFieldContainer>;

type ExposedNumberFieldProps = Omit<
  NumberFieldProps,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "label"
  | "onChange"
  | "placeholder"
  | "value"
>;

type ExposedRangeFieldContainerProps = Omit<
  RangeFieldContainerProps,
  "fieldHeight" | "fromField" | "toField"
>;

type Props = ExposedNumberFieldProps &
  ExposedRangeFieldContainerProps & {
    errorMessageFrom?: string;
    errorMessageTo?: string;
    infoMessageFrom?: string;
    infoMessageTo?: string;
    labelFrom?: string;
    labelTo?: string;
    onChange: (value: NumberRange | null) => unknown;
    placeholderFrom?: string;
    placeholderTo?: string;
    value: NumberRange | null;
  };

const StyledNumberField = styled(NumberField)`
  flex: 1;
  min-width: 0;
`;

const isValidNumberTo = (
  numberTo: number | null,
  numberRange: NumberRange | null
) => {
  if (!numberRange || !numberRange.numberFrom) {
    return true;
  }

  return numberTo === null || numberRange.numberFrom <= numberTo;
};

const isValidNumberFrom = (
  numberFrom: number | null,
  numberRange: NumberRange | null
) => {
  if (!numberRange || !numberRange.numberTo) {
    return true;
  }

  return numberFrom === null || numberRange.numberTo >= numberFrom;
};

const NumberRangeField = ({
  className,
  errorMessageFrom,
  errorMessageTo,
  infoMessageFrom,
  infoMessageTo,
  isDisabled,
  isRequired,
  labelFrom,
  labelTo,
  layout,
  onChange,
  placeholderFrom,
  placeholderTo,
  value,
  ...rest
}: Props) => {
  const valueNumberFrom = value?.numberFrom ?? null;
  const valueNumberTo = value?.numberTo ?? null;

  const onNumberFromChange = (numberFrom: number | null) => {
    onChange({
      numberTo: valueNumberTo,
      numberFrom
    });
  };

  const onNumberToChange = (numberTo: number | null) => {
    onChange({
      numberFrom: valueNumberFrom,
      numberTo
    });
  };

  return (
    <RangeFieldContainer
      {...{className, layout}}
      fieldHeight={NumberField.numberInputHeight}
      fromField={
        <StyledNumberField
          {...rest}
          errorMessage={errorMessageFrom}
          hasError={!isValidNumberFrom(valueNumberFrom, value)}
          infoMessage={infoMessageFrom}
          isDisabled={isDisabled}
          isRequired={isRequired}
          label={labelFrom}
          onChange={onNumberFromChange}
          placeholder={placeholderFrom}
          value={valueNumberFrom}
        />
      }
      toField={
        <StyledNumberField
          {...rest}
          errorMessage={errorMessageTo}
          hasError={!isValidNumberTo(valueNumberTo, value)}
          infoMessage={infoMessageTo}
          isDisabled={isDisabled}
          isRequired={isRequired}
          label={labelTo}
          onChange={onNumberToChange}
          placeholder={placeholderTo}
          value={valueNumberTo}
        />
      }
    />
  );
};

export default NumberRangeField;

export type {NumberRange};
