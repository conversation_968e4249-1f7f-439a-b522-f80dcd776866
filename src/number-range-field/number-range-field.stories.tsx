import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {rangeFieldLayouts} from "@/field/range-field-layout";

import NumberRangeField from "./number-range-field";

import {NumberSymbol} from "./";

type NumberRangeFieldProps = ComponentProps<typeof NumberRangeField>;

type StoryArgs = Pick<
  NumberRangeFieldProps,
  "isDisabled" | "isRequired" | "labelFrom" | "labelTo" | "layout" | "value"
>;

const Default: StoryFn<StoryArgs> = args => {
  const [, updateArgs] = useArgs();

  return (
    <NumberRangeField
      {...args}
      allowNegative={false}
      decimalScale={2}
      errorMessageFrom="Greater than number to"
      errorMessageTo="Smaller than number from"
      left={<NumberSymbol>$</NumberSymbol>}
      onChange={value => updateArgs({value})}
      placeholderFrom="From"
      placeholderTo="To"
    />
  );
};

const meta: Meta<StoryArgs> = {
  title: "number-range-field",
  argTypes: {
    value: {
      control: "object"
    },
    isDisabled: {
      control: "boolean"
    },
    labelFrom: {
      control: "text"
    },
    labelTo: {
      control: "text"
    },
    isRequired: {
      control: "boolean"
    },
    layout: {
      control: "radio",
      options: Object.values(rangeFieldLayouts)
    }
  },
  args: {
    value: {numberFrom: 0, numberTo: 10}
  }
};

export {meta as default, Default};
