import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {chevronDirections} from "@/icons/chevron-icon";

import ChevronButton from "./chevron-button";

type ChevronButtonProps = ComponentProps<typeof ChevronButton>;

type Args = Pick<ChevronButtonProps, "direction" | "isElevated" | "size">;

const Default: StoryFn<Args> = args => (
  <ChevronButton {...args} onClick={() => console.log("clicked")} />
);

const meta: Meta<Args> = {
  title: "chevron-button",
  argTypes: {
    direction: {
      options: Object.values(chevronDirections),
      control: "select"
    },
    isElevated: {
      control: "boolean"
    },
    size: {
      control: "number"
    }
  },
  args: {
    direction: chevronDirections.right,
    size: 48
  }
};

export {meta as default, Default};
