# ArrowButton

## About

This component renders a button with a chevron icon inside.

It allows to change the chevron icon direction and render a border and a shadow if needed.

## Types

- **direction.ts**

```ts
const directions = {
  down: "down",
  left: "left",
  right: "right",
  up: "up"
} as const;

type Direction = typeof directions[keyof typeof directions];
```

- **chevron-button.ts**

```ts
type Props = {
  className?: string;
  direction: Direction;
  hasBorder?: boolean;
  hasShadow?: boolean;
  onClick: () => unknown;
  size: number;
};
```

## Example

```tsx
import ChevronButton from "@unlockre/components-library/dist/chevron-button";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

const Example = () => (
  <ThemeProvider>
    <ChevronButton
      direction="left"
      isElevated
      onClick={() => console.log("left clicked")}
      size={40}
    />
    <ChevronButton
      direction="right"
      isElevated
      onClick={() => console.log("right clicked")}
      size={40}
    />
  </ThemeProvider>
);

export default Example;
```
