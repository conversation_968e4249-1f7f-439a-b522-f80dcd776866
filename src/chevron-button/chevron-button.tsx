import styled, {css} from "styled-components";

import {ChevronIcon} from "@/icons";
import type {ChevronDirection} from "@/icons/chevron-icon";
import {getColor, getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type Props = {
  className?: string;
  direction: ChevronDirection;
  isDisabled?: boolean;
  isElevated?: boolean;
  onClick: () => unknown;
  size: number;
};

type ContainerStyledProps = {
  $isElevated?: boolean;
  $size: number;
};

const chevronIconHeightVsArrowButtonSize = 24 / 48;

const getChevronIconSize = (arrowButtonSize: number) =>
  arrowButtonSize * chevronIconHeightVsArrowButtonSize;

const getSizeCss = (size: number) => css`
  height: ${size}px;
  width: ${size}px;
`;

const elevatedCss = css`
  border: 1px solid ${getColor("gray", "070")};
  box-shadow: 0px 4px 58px rgba(7, 24, 84, 0.06);

  :hover {
    box-shadow: 0px 4px 30px 6px rgba(7, 24, 84, 0.14);
  }
`;

const Container = styled(UnstyledButton)<ContainerStyledProps>`
  ${props => props.$isElevated && elevatedCss};
  ${props => getSizeCss(props.$size)}

  align-items: center;
  background: ${getColorByAlias("backgroundWhite")};
  border-radius: 50%;
  display: flex;
  justify-content: center;
`;

const ChevronButton = ({
  direction,
  isDisabled,
  isElevated,
  size,
  ...rest
}: Props) => (
  <Container
    {...rest}
    $isElevated={isElevated}
    $size={size}
    disabled={isDisabled}
  >
    <ChevronIcon
      {...{direction}}
      getColor={
        isDisabled
          ? getColorByAlias("textDisabled")
          : getColorByAlias("accentSecondary")
      }
      height={getChevronIconSize(size)}
    />
  </Container>
);

export default ChevronButton;
