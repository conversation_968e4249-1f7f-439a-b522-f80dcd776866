import type {ComponentProps} from "react";
import styled from "styled-components";

import {fieldBoxTextCss} from "@/field/field-styles";
import NumberField from "@/number-field";

type NumberFieldProps = ComponentProps<typeof NumberField>;

type Props = Omit<NumberFieldProps, "decimalScale" | "placeholder" | "right">;

const SymbolContainer = styled.div`
  ${fieldBoxTextCss}
`;

const CurrencyField = (props: Props) => (
  <NumberField
    {...props}
    allowNegative={false}
    decimalScale={2}
    left={<SymbolContainer disabled={props.isDisabled}>$</SymbolContainer>}
    placeholder="0"
  />
);

export default CurrencyField;
