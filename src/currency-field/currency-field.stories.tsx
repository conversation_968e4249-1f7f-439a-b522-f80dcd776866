import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import CurrencyField from "./currency-field";

type CurrencyFieldProps = ComponentProps<typeof CurrencyField>;

type Args = Pick<
  CurrencyFieldProps,
  "errorMessage" | "hasError" | "infoMessage" | "isDisabled" | "label" | "value"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return <CurrencyField {...args} onChange={value => updateArgs({value})} />;
};

const meta: Meta<Args> = {
  title: "currency-field",
  argTypes: {
    hasError: {
      control: "boolean"
    },
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    value: {
      control: "text"
    }
  },
  args: {
    value: null
  }
};

export {meta as default, Default};
