import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {getColor} from "@/theme-provider/theme";

import {SingleProgressBar} from "./single-progress-bar";

type SingleProgressBarProps = ComponentProps<typeof SingleProgressBar>;

type Args = Pick<SingleProgressBarProps, "height" | "progress">;

const Default: StoryFn<Args> = args => (
  <SingleProgressBar {...args} getProgressColor={getColor("blue", "450")} />
);

const meta: Meta<Args> = {
  title: "single-progress-bar",
  argTypes: {
    height: {
      control: "number"
    },
    progress: {
      control: "number"
    }
  },
  args: {
    height: 16,
    progress: 25
  }
};

export {meta as default, Default};
