import {type ComponentProps, forwardRef} from "react";

import type {GetColor} from "@/theme-provider/theme";

import {ProgressBar} from "./progress-bar";
import type {RenderProgressItem} from "./progress-bar";
import {ProgressBlock} from "./progress-block";
import type {ProgressItem} from "./types";

type MultiProgressItem = {
  getColor: GetColor;
  value: number;
};

type ProgressBarProps = ComponentProps<typeof ProgressBar>;

type ExposedProgressBarProps = Pick<ProgressBarProps, "className" | "height">;

type Props = ExposedProgressBarProps & {
  multiProgressItems: MultiProgressItem[];
};

const getTotalValue = (multiProgressItems: MultiProgressItem[]) =>
  multiProgressItems.reduce(
    (result, multiProgressItem) => result + multiProgressItem.value,
    0
  );

const getProgressItems = (multiProgressItems: MultiProgressItem[]) => {
  const totalValue = getTotalValue(multiProgressItems);

  return multiProgressItems.map<ProgressItem>(multiProgressItem => ({
    getColor: multiProgressItem.getColor,
    percentage: (multiProgressItem.value * 100) / totalValue
  }));
};

const renderProgressItem: RenderProgressItem = (progressItem, height) => (
  <ProgressBlock $height={height} $progressItem={progressItem} />
);

const MultiProgressBar = forwardRef<HTMLUListElement, Props>(
  ({multiProgressItems, ...rest}, ref) => (
    <ProgressBar
      ref={ref}
      {...{renderProgressItem}}
      {...rest}
      progressItems={getProgressItems(multiProgressItems)}
    />
  )
);

export {MultiProgressBar};
