import type {ComponentProps} from "react";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";

import {ProgressBar} from "./progress-bar";
import type {RenderProgressItem} from "./progress-bar";
import {ProgressBlock} from "./progress-block";

type ProgressBarProps = ComponentProps<typeof ProgressBar>;

type ExposedProgressBarProps = Pick<ProgressBarProps, "className" | "height">;

type Props = ExposedProgressBarProps & {
  getProgressColor: GetColor;
  progress: number;
};

const renderProgressItem: RenderProgressItem = (
  progressItem,
  height,
  index
) => (
  <ProgressBlock
    $hasRoundedBorders={index === 0}
    $height={height}
    $progressItem={progressItem}
  />
);

const StyledProgressBar = styled(ProgressBar)`
  background-color: ${getColor("gray", "070")};
`;

const SingleProgressBar = ({getProgressColor, progress, ...rest}: Props) => (
  <StyledProgressBar
    {...{renderProgressItem}}
    {...rest}
    progressItems={[
      {
        getColor: getProgressColor,
        percentage: progress
      }
    ]}
  />
);

export {SingleProgressBar};
