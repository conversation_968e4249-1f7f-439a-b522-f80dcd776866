import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {getColor} from "@/theme-provider/theme";

import {MultiProgressBar} from "./multi-progress-bar";

type MultiProgressBarProps = ComponentProps<typeof MultiProgressBar>;

type ExposedMultiProgressBarProps = Pick<MultiProgressBarProps, "height">;

type Args = ExposedMultiProgressBarProps & {
  multiProgressItemValues: number[];
};

const multiProgressItemColorGetters = [
  getColor("blue", "500"),
  getColor("green", "500"),
  getColor("lila", "500"),
  getColor("orange", "500"),
  getColor("pink", "550"),
  getColor("purple", "550"),
  getColor("red", "500"),
  getColor("blue", "100"),
  getColor("green", "100"),
  getColor("lila", "50")
];

const getMultiProgressItem = (value: number, index: number) => ({
  getColor: multiProgressItemColorGetters[index],
  value
});

const Default: StoryFn<Args> = ({multiProgressItemValues, ...rest}) => (
  <MultiProgressBar
    {...rest}
    multiProgressItems={multiProgressItemValues.map(getMultiProgressItem)}
  />
);

const meta: Meta<Args> = {
  title: "multi-progress-bar",
  argTypes: {
    height: {
      control: "number"
    },
    multiProgressItemValues: {
      control: "object"
    }
  },
  args: {
    height: 16,
    multiProgressItemValues: [10, 30, 60]
  }
};

export {meta as default, Default};
