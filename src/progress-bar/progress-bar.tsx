import {Fragment, type ReactElement, forwardRef} from "react";
import styled from "styled-components";

import {UnstyledUl} from "@/unstyled";

import type {ProgressItem} from "./types";

type RenderProgressItem = (
  progressItem: ProgressItem,
  height: number,
  index: number
) => ReactElement;

type Props = {
  className?: string;
  height: number;
  progressItems: ProgressItem[];
  renderProgressItem: RenderProgressItem;
};

type ContainerStyledProps = {
  $height: number;
};

const Container = styled(UnstyledUl)<ContainerStyledProps>`
  display: flex;
  border-radius: ${props => props.$height / 2}px;
  overflow: hidden;
  height: ${props => props.$height}px;
`;

const ProgressBar = forwardRef<HTMLUListElement, Props>(
  ({className, height, progressItems, renderProgressItem}, ref) => (
    <Container ref={ref} {...{className}} $height={height}>
      {progressItems.map((progressItem, index) => (
        <Fragment key={index}>
          {renderProgressItem(progressItem, height, index)}
        </Fragment>
      ))}
    </Container>
  )
);

export {ProgressBar};
export type {RenderProgressItem};
