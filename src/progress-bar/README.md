# ProgressBar

## Components

### SingleProgressBar

This component renders a progress bar from a received progress (`percentage = 0..100`).

#### About

#### Props

```ts
import type {GetColor} from "@unlockre/components-library/dist/theme-provider/theme";

type Props = {
  className?: string;
  getProgressColor: GetColor;
  height: number;
  progress: number;
};
```

#### Example

```tsx
import {getColor} from "@unlockre/components-library/dist/theme-provider/theme";
import {SingleProgressBar} from "@unlockre/components-library/dist/progress-bar";

const SomeComponent = () => (
  <SingleProgressBar
    getProgressColor={getColor("blue", "450")}
    height={16}
    progress={25}
  />
);
```

### MultiProgressBar

#### About

This component can be used to represent multiple values in a progress bar.

> The values don't need to be percentages, they are used to calculate the total, so then the component can know the percentage of each of them.

#### Props

```ts
import type {GetColor} from "@unlockre/components-library/dist/theme-provider/theme";

type MultiProgressItem = {
  getColor: GetColor;
  value: number;
};

type Props = {
  className?: string;
  height: number;
  multiProgressItems: MultiProgressItem[];
};
```

#### Example

```tsx
import {getColor} from "@unlockre/components-library/dist/theme-provider/theme";
import {MultiProgressBar} from "@unlockre/components-library/dist/progress-bar";

const SomeComponent = () => (
  <MultiProgressBar
    height={16}
    multiProgressItems={[
      {
        getColor: getColor("blue", "450"),
        // the percentage of this multi-progress-item will be:
        // (350 * 100) / total = (350 * 100) / (350 + 150) = 70
        value: 350
      },
      {
        getColor: getColor("blue", "800"),
        // the percentage of this multi-progress-item will be:
        // (150 * 100) / total = (150 * 100) / (350 + 150) = 30
        value: 150
      }
    ]}
  />
)
```