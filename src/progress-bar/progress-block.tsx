import styled, {css} from "styled-components";

import type {ProgressItem} from "./types";

type StyledProps = {
  $hasRoundedBorders?: boolean;
  $height: number;
  $progressItem: ProgressItem;
};

const getRoundedBordersCss = (height: number) => css`
  border-bottom-right-radius: ${height / 2}px;
  border-top-right-radius: ${height / 2}px;
`;

const ProgressBlock = styled.li<StyledProps>`
  ${props => props.$hasRoundedBorders && getRoundedBordersCss(props.$height)}

  background-color: ${props => props.$progressItem.getColor};
  height: 100%;
  transition: width 0.5s ease-in-out;
  width: ${props => props.$progressItem.percentage}%;
`;

export {ProgressBlock};
