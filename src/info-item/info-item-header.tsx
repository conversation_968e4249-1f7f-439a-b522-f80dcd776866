import styled from "styled-components";

import {Circle} from "@/shapes";
import type {GetColor} from "@/theme-provider/theme";

import {InfoItemHelp} from "./info-item-help";
import {InfoItemLabel} from "./info-item-label";
import type {InfoItemSize} from "./info-item-size";
import type {InfoItemVariant} from "./info-item-variant";

type Props = {
  getCircleColor?: GetColor;
  help?: string;
  label: string;
  size: InfoItemSize;
  variant: InfoItemVariant;
};

const Container = styled.div`
  align-items: center;
  display: flex;
  gap: 4px;
`;

const InfoItemHeader = ({getCircleColor, help, label, ...rest}: Props) => (
  <Container>
    {getCircleColor && <Circle getColor={getCircleColor} size={8} />}
    <InfoItemLabel {...rest}>{label}</InfoItemLabel>
    {help && <InfoItemHelp {...rest}>{help}</InfoItemHelp>}
  </Container>
);

export {InfoItemHeader};
