import styled from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import {infoItemMessageTypes} from "./info-item-message-type";
import type {InfoItemMessageType} from "./info-item-message-type";
import {infoItemSizes} from "./info-item-size";
import type {InfoItemSize} from "./info-item-size";

type Props = {
  children: string[] | string;
  size: InfoItemSize;
  type: InfoItemMessageType;
};

type ContainerStyledProps = {
  $size: InfoItemSize;
  $type: InfoItemMessageType;
};

const containerColorGetters = {
  [infoItemMessageTypes.bad]: getColorByAlias("feedbackError"),
  [infoItemMessageTypes.good]: getColorByAlias("feedbackSuccess"),
  [infoItemMessageTypes.neutral]: getColor("gray", "400")
};

const containerTypographyGetters = {
  [infoItemSizes.small]: getTypography("body", "xs"),
  [infoItemSizes.medium]: getTypography("body", "xs"),
  [infoItemSizes.large]: getTypography("body", "s")
};

const Container = styled.div<ContainerStyledProps>`
  ${props => containerTypographyGetters[props.$size]}

  color: ${props => containerColorGetters[props.$type]};
`;

const InfoItemMessage = ({children, size, type}: Props) => (
  <Container $size={size} $type={type}>
    {children}
  </Container>
);

export {InfoItemMessage};
