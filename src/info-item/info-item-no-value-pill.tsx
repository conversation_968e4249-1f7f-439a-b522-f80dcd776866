import styled, {css} from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import {type InfoItemSize, infoItemSizes} from "./info-item-size";
import {type InfoItemVariant, infoItemVariants} from "./info-item-variant";

type Props = {
  isRightAligned?: boolean;
  size: InfoItemSize;
  variant: InfoItemVariant;
};

type ContainerStyledProps = {
  $size: InfoItemSize;
  $variant: InfoItemVariant;
};

const noValue = "—";

const containerDarkCss = css`
  background-color: ${getColor("gray", "100")};
  color: ${getColor("gray", "550")};
`;

const containerLightCss = css`
  background-color: ${getColor("gray", "900")};
  color: ${getColorByAlias("textInverted")};
`;

const getContainerVariantCss = <TProps extends ContainerStyledProps>(
  props: TProps
) => {
  switch (props.$variant) {
    case infoItemVariants.dark:
      return containerDarkCss;

    case infoItemVariants.light:
      return containerLightCss;
  }
};

const containerVerticalMargins = {
  [infoItemSizes.small]: 0.5,
  [infoItemSizes.medium]: 2.5,
  [infoItemSizes.large]: 9
};

const Container = styled.div<ContainerStyledProps>`
  ${getContainerVariantCss}
  ${getTypography("body", "xxs")}

  border-radius: 4px;
  margin-top: ${props => containerVerticalMargins[props.$size]}px;
  margin-bottom: ${props => containerVerticalMargins[props.$size]}px;
  min-width: 32px;
  padding: 2px 5px;
  text-align: center;
`;

const InfoItemNoValuePill = ({size, variant}: Props) => (
  <Container $size={size} $variant={variant}>
    {noValue}
  </Container>
);

export {InfoItemNoValuePill};
