import type {ComponentProps, ReactNode} from "react";
import styled from "styled-components";

import * as withInfoItemAlignment from "./info-item-alignment";
import type {InfoItemAlignment} from "./info-item-alignment";
import {InfoItemHeader} from "./info-item-header";
import {infoItemSizes} from "./info-item-size";
import type {InfoItemSize} from "./info-item-size";
import type {InfoItemVariant} from "./info-item-variant";

type InfoItemHeaderProps = ComponentProps<typeof InfoItemHeader>;

type Props = InfoItemHeaderProps & {
  alignment?: InfoItemAlignment;
  children: ReactNode;
  className?: string;
  size: InfoItemSize;
  variant: InfoItemVariant;
};

type ContainerStyledProps = {
  $alignment: InfoItemAlignment;
  $size: InfoItemSize;
};

const {infoItemAlignments} = withInfoItemAlignment;

const containerGaps = {
  [infoItemSizes.small]: 4,
  [infoItemSizes.medium]: 4,
  [infoItemSizes.large]: 8
};

const Container = styled.div<ContainerStyledProps>`
  align-items: ${props => withInfoItemAlignment.getFlexValue(props.$alignment)};
  display: flex;
  flex-direction: column;
  gap: ${props => containerGaps[props.$size]}px;
`;

const InfoItemLayout = ({
  alignment = infoItemAlignments.left,
  children,
  className,
  size,
  variant,
  ...infoItemHeaderProps
}: Props) => (
  <Container {...{className}} $alignment={alignment} $size={size}>
    <InfoItemHeader {...{size, variant}} {...infoItemHeaderProps} />
    {children}
  </Container>
);

export {InfoItemLayout};
