import styled from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import {infoItemSizes} from "./info-item-size";
import type {InfoItemSize} from "./info-item-size";
import {infoItemVariants} from "./info-item-variant";
import type {InfoItemVariant} from "./info-item-variant";

type Props = {
  children: string;
  size: InfoItemSize;
  variant: InfoItemVariant;
};

type VariantStyledProps = {
  $variant: InfoItemVariant;
};

type ContainerStyledProps = VariantStyledProps & {
  $size: InfoItemSize;
};

const getInfoItemLabelColor = <TProps extends VariantStyledProps>(
  props: TProps
) => {
  switch (props.$variant) {
    case infoItemVariants.dark:
      return getColorByAlias("textSecondary");

    case infoItemVariants.light:
      return getColor("gray", "250");
  }
};

const typographyGetters = {
  [infoItemSizes.small]: getTypography("body", "xs"),
  [infoItemSizes.medium]: getTypography("body", "xs"),
  [infoItemSizes.large]: getTypography("body", "s")
};

const Container = styled.div<ContainerStyledProps>`
  ${props => typographyGetters[props.$size]}

  color: ${getInfoItemLabelColor};
`;

const InfoItemLabel = ({children, size, variant}: Props) => (
  <Container $size={size} $variant={variant}>
    {children}
  </Container>
);

export {getInfoItemLabelColor, InfoItemLabel};
