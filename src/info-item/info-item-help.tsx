import {Info} from "@phosphor-icons/react";
import styled from "styled-components";

import {IconWithColor} from "@/icons/icon-with-color";
import Tooltip, {useTooltip} from "@/tooltip";
import type {TooltipVariant} from "@/tooltip/tooltip-variant";

import {getInfoItemLabelColor} from "./info-item-label";
import {type InfoItemSize, infoItemSizes} from "./info-item-size";
import {type InfoItemVariant, infoItemVariants} from "./info-item-variant";

type Props = {
  children: string;
  size: InfoItemSize;
  variant: InfoItemVariant;
};

const tooltipVariants: Record<InfoItemVariant, TooltipVariant> = {
  [infoItemVariants.dark]: "dark",
  [infoItemVariants.light]: "white"
};

const iconSizes: Record<InfoItemSize, number> = {
  [infoItemSizes.large]: 18,
  [infoItemSizes.medium]: 15,
  [infoItemSizes.small]: 15
};

const IconContainer = styled.div`
  cursor: pointer;
  /* we need the following because the Icon is inline */
  display: flex;
`;

const InfoItemHelp = ({children, size, variant}: Props) => {
  const {hideTooltip, isTooltipOpened, reference, showTooltip, tooltipProps} =
    useTooltip("bottom");

  return (
    <>
      <IconContainer
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        ref={reference}
      >
        <IconWithColor
          getColor={getInfoItemLabelColor({$variant: variant})}
          icon={Info}
          size={iconSizes[size]}
          weight="regular"
        />
      </IconContainer>
      {isTooltipOpened && (
        <Tooltip
          {...tooltipProps}
          size="small"
          variant={tooltipVariants[variant]}
        >
          {children}
        </Tooltip>
      )}
    </>
  );
};

export {InfoItemHelp};
