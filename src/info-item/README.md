# info-item/*

These components can be used whenever you need to show a piece of information with label with a value below.

## Types

```ts
type InfoItemAlignment = "center" | "left" | "right"

type InfoItemMessageType = "bad" | "good"

type InfoItemSize = "small" | "medium" | "large"

type InfoItemVariant = "dark" | "light"
```

## Components

- [InfoItem](#InfoItem)
- [InfoItemLayout](#InfoItemLayout)
- [InfoItemMessage](#InfoItemMessage)

## InfoItem

### About

This component renders a header with an optional circle, label and a help icon + tooltip; and a value below. If the value is valid URL, it's rendered as a `Link`.

> This component is composed by an `InfoItemLayout` (see below) with an `InfoItemValue` inside.

### Props

```ts
import type {ReactElement} from "react";

import type {GetColor} from "@/theme-provider/theme";

import type {InfoItemAlignment} from "./info-item-alignment";
import type {InfoItemSize} from "./info-item-size";
import type {InfoItemVariant} from "./info-item-variant";

type Props = {
  alignment?: InfoItemAlignment;
  bottom?: ReactElement;
  className?: string;
  getCircleColor?: GetColor;
  help?: string;
  label: string;
  size: InfoItemSize;
  value: string;
  valueLeft?: ReactElement;
  valueRight?: ReactElement;
  valueSize?: InfoItemSize;
  variant: InfoItemVariant;
};
```

### Example

```tsx
import {formatCurrency} from "@unlockre/utils-formatting/dist";
import {InfoItem} from "@unlockre/components-library/dist/info-item";

type Props = {
  propertyPrice: number;
};

const PropertyPriceInfoItem = ({propertyPrice}: Props) => (
  <InfoItem
    help="This value shows the property price"
    label="Property Price"
    size="medium"
    value={formatCurrency(propertyPrice)}
    variant="dark"
  />
);
```

## InfoItemLayout

### About

This component renders a header with an optional circle, label and a help icon + tooltip; and whatever is passed inside the `children` prop below.

### Props

```ts
import type {ReactNode} from "react";

import type {GetColor} from "@/theme-provider/theme";

import type {InfoItemAlignment} from "./info-item-alignment";
import type {InfoItemSize} from "./info-item-size";
import type {InfoItemVariant} from "./info-item-variant";

type Props = {
  alignment?: InfoItemAlignment;
  children: ReactNode;
  className?: string;
  getCircleColor?: GetColor;
  help?: string;
  label: string;
  size: InfoItemSize;
  variant: InfoItemVariant;
};
```

### Example

```tsx
import {InfoItemLayout} from "@unlockre/components-library/dist/info-item";
import {UnstyledUl} from "@unlockre/components-library/dist/unstyled";

type Props = {
  amenities: string[];
};

const AmenitiesInfoItem = ({amenities}: Props) => (
  <InfoItemLayout
    label="Amenities"
    size="medium"
    variant="dark"
  >
    <UnstyledUl>
      {amenities.map(amenity => (
        <li key={amenity}>{amenity}</li>
      ))}
    </UnstyledUl>
  </InfoItemLayout>
):
```

## InfoItemMessage

### About

This component renders a message that indicates that the value rendered inside the `InfoItem` is be something `good` or `bad`.

> This component has to be passed inside the `bottom` prop of the `InfoItem` component.

### Props

```ts
import type {ReactNode} from "react";

import type {GetColor} from "@/theme-provider/theme";

import type {InfoItemMessageType} from "./info-item-message-type";
import type {InfoItemSize} from "./info-item-size";

type Props = {
  children: string[] | string;
  size: InfoItemSize;
  type: InfoItemMessageType;
};
```

### Example

```tsx
import {formatPercentage} from "@unlockre/utils-formatting/dist";
import {InfoItem, InfoItemMessage} from "@unlockre/components-library/dist/info-item";

type Props = {
  unitMinRentPrice: number;
  unitRentPrice: number;
};

const getUnitRentPricePercentage = ({unitMinRentPrice, unitRentPrice}: Props) =>
  (unitRentPrice / unitMinRentPrice - 1) * 100

const renderUnitRentPricePercentageMessage = (props: Props) =>
  <InfoItemMessage
    size="medium"
    type={props.unitPrice < props.unitMinRentPrice ? "good" : "bad"}
  >
    {formatPercentage(getUnitRentPricePercentage(props), {
      signDisplay: "exceptZero"
    })}
  </InfoItemMessage>

const UnitRentPriceInfoItem = (props: Props) => (
  <InfoItem
    bottom={renderUnitRentPricePercentageMessage(props)}
    label="Unit Rent Price"
    size="medium"
    value={formatCurrency(props.unitRentPrice)}
    variant="dark"
  />
);
```
