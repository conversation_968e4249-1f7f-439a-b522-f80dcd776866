import {isUrl} from "@unlockre/utils-validation/dist";
import styled from "styled-components";

import {Link} from "@/link";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import {infoItemSizes} from "./info-item-size";
import type {InfoItemSize} from "./info-item-size";
import {type InfoItemVariant, infoItemVariants} from "./info-item-variant";

type Props = {
  children: string;
  isRightAligned?: boolean;
  size: InfoItemSize;
  variant: InfoItemVariant;
};

type ContainerStyledProps = {
  $size: InfoItemSize;
  $variant: InfoItemVariant;
};

const renderLink = (children: string, variant: InfoItemVariant) => (
  <Link {...{variant}} href={children}>
    {children}
  </Link>
);

const getContainerColor = <TProps extends ContainerStyledProps>(
  props: TProps
) => {
  switch (props.$variant) {
    case infoItemVariants.dark:
      return getColorByAlias("textPrimary");

    case infoItemVariants.light:
      return getColorByAlias("textInverted");
  }
};

const typographyGetters = {
  [infoItemSizes.small]: getTypography("body", "s"),
  [infoItemSizes.medium]: getTypography("body", "l"),
  [infoItemSizes.large]: getTypography("title", "l")
};

const Container = styled.div<ContainerStyledProps>`
  ${props => typographyGetters[props.$size]}

  color: ${getContainerColor};
`;

const InfoItemValue = ({children, size, variant}: Props) => (
  <Container $size={size} $variant={variant}>
    {isUrl(children) ? renderLink(children, variant) : children}
  </Container>
);

export {InfoItemValue};
