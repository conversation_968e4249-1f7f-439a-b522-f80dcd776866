const infoItemAlignments = {
  center: "center",
  left: "left",
  right: "right"
} as const;

type InfoItemAlignment =
  (typeof infoItemAlignments)[keyof typeof infoItemAlignments];

const flexValues = {
  [infoItemAlignments.center]: "center",
  [infoItemAlignments.left]: "flex-start",
  [infoItemAlignments.right]: "flex-end"
};

const getFlexValue = (infoItemAlignment: InfoItemAlignment) =>
  flexValues[infoItemAlignment];

export {getFlexValue, infoItemAlignments};

export type {InfoItemAlignment};
