import type {FormatValue} from "@unlockre/utils-formatting/dist";
import type {ComponentProps, ReactElement} from "react";
import styled from "styled-components";

import {InfoItemLayout} from "./info-item-layout";
import {InfoItemNoValuePill} from "./info-item-no-value-pill";
import type {InfoItemSize} from "./info-item-size";
import {InfoItemValue} from "./info-item-value";
import type {InfoItemVariant} from "./info-item-variant";

type InfoItemLayoutProps = ComponentProps<typeof InfoItemLayout>;

type ExposedInfoItemLayoutProps = Omit<InfoItemLayoutProps, "children">;

type Props<TOptionalValue> = ExposedInfoItemLayoutProps & {
  bottom?: ReactElement;
  formatValue?: FormatValue<NonNullable<TOptionalValue>>;
  size: InfoItemSize;
  value?: TOptionalValue;
  valueLeft?: ReactElement;
  valueRight?: ReactElement;
  valueSize?: InfoItemSize;
  variant: InfoItemVariant;
};

const ValueAndOthersContainer = styled.div`
  align-items: center;
  display: flex;
  gap: 4px;
`;

const InfoItem = <TValue,>({
  bottom,
  formatValue = String,
  size,
  value,
  valueLeft,
  valueRight,
  valueSize,
  variant,
  ...infoItemLayoutProps
}: Props<TValue>) => (
  <InfoItemLayout {...{size, variant}} {...infoItemLayoutProps}>
    <ValueAndOthersContainer>
      {valueLeft}
      {value === undefined || value === null ? (
        <InfoItemNoValuePill {...{variant}} size={valueSize || size} />
      ) : (
        <InfoItemValue {...{variant}} size={valueSize || size}>
          {formatValue(value)}
        </InfoItemValue>
      )}
      {valueRight}
    </ValueAndOthersContainer>
    {bottom}
  </InfoItemLayout>
);

export {InfoItem};
