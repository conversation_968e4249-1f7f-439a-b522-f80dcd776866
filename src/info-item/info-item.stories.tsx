import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

import {InfoItem} from "./info-item";
import {infoItemAlignments} from "./info-item-alignment";
import {InfoItemMessage} from "./info-item-message";
import {infoItemMessageTypes} from "./info-item-message-type";
import type {InfoItemMessageType} from "./info-item-message-type";
import {infoItemSizes} from "./info-item-size";
import {infoItemVariants} from "./info-item-variant";

type InfoItemProps = ComponentProps<typeof InfoItem>;

type ExposedInfoItemProps = Pick<
  InfoItemProps,
  "alignment" | "help" | "label" | "size" | "value" | "valueSize" | "variant"
>;

type Args = ExposedInfoItemProps & {
  infoItemMessageContent?: string;
  infoItemMessageType?: InfoItemMessageType;
  withCircle: boolean;
};

const getCircleColor = getColor("blue", "500");

const Container = styled.div`
  width: fit-content;
`;

const Default: StoryFn<Args> = ({
  infoItemMessageContent,
  infoItemMessageType,
  value,
  withCircle,
  ...rest
}) => (
  <Container>
    <InfoItem
      {...rest}
      bottom={
        !infoItemMessageType || !infoItemMessageContent ? undefined : (
          <InfoItemMessage size={rest.size} type={infoItemMessageType}>
            {infoItemMessageContent}
          </InfoItemMessage>
        )
      }
      getCircleColor={withCircle ? getCircleColor : undefined}
      value={value === "" ? undefined : value}
    />
  </Container>
);

const meta: Meta<Args> = {
  title: "info-item",
  argTypes: {
    alignment: {
      control: "select",
      options: Object.values(infoItemAlignments)
    },
    help: {
      control: "text"
    },
    infoItemMessageContent: {
      control: "text"
    },
    infoItemMessageType: {
      control: "select",
      options: Object.values(infoItemMessageTypes)
    },
    label: {
      control: "text"
    },
    size: {
      control: "select",
      options: Object.values(infoItemSizes)
    },
    value: {
      control: "text"
    },
    valueSize: {
      control: "select",
      options: Object.values(infoItemSizes)
    },
    variant: {
      control: "select",
      options: Object.values(infoItemVariants)
    },
    withCircle: {
      control: "boolean"
    }
  },
  args: {
    alignment: infoItemAlignments.left,
    help: "This is some help",
    infoItemMessageContent: "Some message content",
    infoItemMessageType: infoItemMessageTypes.good,
    label: "Some price",
    size: infoItemSizes.large,
    value: "$1000",
    valueSize: infoItemSizes.large,
    variant: infoItemVariants.dark,
    withCircle: true
  }
};

export {meta as default, Default};
