import {DataGrid} from "@mui/x-data-grid";
import type {ComponentProps} from "react";
import styled from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider";

type DataGridProps = ComponentProps<typeof DataGrid>;

type ExposedDataGridProps = Omit<
  DataGridProps,
  | "autoHeight"
  | "checkboxSelection"
  | "disableColumnMenu"
  | "disableSelectionOnClick"
  | "rowHeight"
>;

type Props = ExposedDataGridProps & {className?: string};

const Container = styled.div`
  && .MuiDataGrid-root {
    border: 0;
  }

  && .MuiDataGrid-columnHeader {
    background: transparent;
    color: rgba(0, 0, 0, 0.55);
    font-size: 12px;
    height: 47px;
  }

  && .MuiDataGrid-columnHeader .MuiDataGrid-columnSeparator {
    display: none;
  }

  && .MuiDataGrid-cell {
    border-bottom: 0;
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
    line-height: 18px;

    &:focus {
      outline: none;
    }
  }

  && .MuiDataGrid-row {
    background-color: rgb(255, 255, 255);

    &:hover {
      background-color: rgb(246, 249, 255);
    }
  }
`;

/**
 * @deprecated Use Table component instead
 */
const DataTable = ({className, ...dataGridProps}: Props) => (
  <Container {...{className}}>
    <MuiThemeProvider>
      <DataGrid
        {...dataGridProps}
        checkboxSelection={false}
        disableColumnMenu
        disableSelectionOnClick
        rowHeight={64}
      />
    </MuiThemeProvider>
  </Container>
);

export default DataTable;
