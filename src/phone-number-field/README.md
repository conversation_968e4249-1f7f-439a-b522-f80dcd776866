# PhoneNumberField

## About

This component provides an input to enter phone numbers with a mask that formats numbers to E.164 standard

### Example

```tsx
import {useState} from "react";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import PhoneNumberField from "@unlockre/components-library/dist/phone-number-field";

const MyApp = () => {
 const [phoneNumber, setPhoneNumber] = useState("");

 return (
	<ThemeProvider>
    <PhoneNumberField
      label="Client Phone Number"
      onChange={setPhoneNumber}
      placeholder="Enter a valid Phone Number"
      value={phoneNumber}
    />
	</ThemeProvider>
 );
}

export default MyApp;
```