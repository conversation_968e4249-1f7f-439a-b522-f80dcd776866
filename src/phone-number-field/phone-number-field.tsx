import {AsYouType, isValidPhoneNumber} from "libphonenumber-js";
import type {ComponentProps} from "react";

import TextField from "@/text-field";

type TextFieldProps = ComponentProps<typeof TextField>;

type ExposedTextFieldProps = Omit<TextFieldProps, "onChange">;

type Props = ExposedTextFieldProps & {
  onChange: (phoneNumber: string) => unknown;
};

const formatPhoneNumber = (phoneNumber: string) =>
  new AsYouType().input(phoneNumber);

const addPlusIfNecesary = (phoneNumber: string) =>
  phoneNumber && !phoneNumber.startsWith("+") ? `+${phoneNumber}` : phoneNumber;

const getPhoneNumberFrom = (formattedPhoneNumber: string) => {
  const asYouType = new AsYouType();

  asYouType.input(formattedPhoneNumber);

  const numberInstance = asYouType.getNumber();

  return numberInstance
    ? (numberInstance.number as string)
    : addPlusIfNecesary(formattedPhoneNumber);
};

const PhoneNumberField = ({onChange, value, ...rest}: Props) => (
  <TextField
    {...rest}
    onChange={event => onChange(getPhoneNumberFrom(event.target.value))}
    value={value && formatPhoneNumber(value)}
  />
);

export default Object.assign(PhoneNumberField, {isValidPhoneNumber});
