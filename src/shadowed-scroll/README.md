# Drawer

## About

This component provides a container with shadows that appear or disappear depending on the behavior of the scroll

## Example

```tsx
import ShadowedScroll from "@unlockre/components-library/dist/shadowed-scroll";
import styled from "styled-components";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {useState} from "react";

const StyledShadowedScroll = styled(ShadowedScroll)`
  height: 310px;
  width: 300px;
  border: 1px solid gray;
`;

const Container = styled.div`
  padding: 0 32px;
`;

const MyApp = () => {
  return (
    <ThemeProvider>
      <StyledShadowedScroll>
        <Container>
          <p>
            Lorem Ipsum is simply dummy text of the printing and typesetting
            industry. Lorem Ipsum has been the industrys standard dummy text ever
            since the 1500s, when an unknown printer took a galley of type and
            scrambled it to make a type specimen book. It has survived not only
            five centuries, but also the leap into electronic typesetting,
            remaining essentially unchanged. It was popularised in the 1960s with
            the release of Letraset sheets containing Lorem Ipsum passages, and
            more recently with desktop publishing software like Aldus PageMaker
            including versions of Lorem Ipsum
          </p>
          <p>
            Lorem Ipsum is simply dummy text of the printing and typesetting
            industry. Lorem Ipsum has been the industrys standard dummy text ever
            since the 1500s, when an unknown printer took a galley of type and
            scrambled it to make a type specimen book. It has survived not only
            five centuries, but also the leap into electronic typesetting,
            remaining essentially unchanged. It was popularised in the 1960s with
            the release of Letraset sheets containing Lorem Ipsum passages, and
            more recently with desktop publishing software like Aldus PageMaker
            including versions of Lorem Ipsum
          </p>
        </Container>
      </StyledShadowedScroll>
    </ThemeProvider>
  );
};

export default MyApp;
```
