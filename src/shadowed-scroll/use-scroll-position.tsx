import {useRef, useState} from "react";
import type {UIEvent} from "react";
import useResizeObserver from "use-resize-observer";

import type {ScrollPosition} from "./types";

const isScrollAtTheBottom = (element: HTMLElement) =>
  element.scrollHeight <=
  element.getBoundingClientRect().height + Math.round(element.scrollTop);

const isScrollAtTheTop = (element: HTMLElement) => element.scrollTop === 0;

const hasScroll = (element: HTMLElement) =>
  element.scrollHeight > element.clientHeight;

const getScrollPosition = (element: HTMLElement) => {
  if (!hasScroll(element)) {
    return undefined;
  }

  if (isScrollAtTheTop(element)) {
    return "top";
  }

  if (isScrollAtTheBottom(element)) {
    return "bottom";
  }

  return "middle";
};

const useScrollPosition = <THTMLElement extends HTMLElement>() => {
  const [scrollPosition, setScrollPosition] = useState<
    ScrollPosition | undefined
  >(undefined);

  const elementRef = useRef<THTMLElement>(null);

  useResizeObserver({
    ref: elementRef,
    onResize: () => {
      if (elementRef.current) {
        setScrollPosition(getScrollPosition(elementRef.current));
      }
    }
  });

  const onScroll = (event: UIEvent<HTMLElement>) => {
    setScrollPosition(getScrollPosition(event.currentTarget));
  };

  return {
    scrollPosition,
    onScroll,
    elementRef
  };
};

export default useScrollPosition;
