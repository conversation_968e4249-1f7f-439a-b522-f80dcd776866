import type {ReactNode} from "react";
import styled from "styled-components";

import type {ScrollPosition} from "./types";
import useScrollPosition from "./use-scroll-position";

type Props = {
  children: ReactNode;
  className?: string;
};

type ContainerElement = HTMLDivElement;

type ShadowStyledProps = {
  $scrollPosition?: ScrollPosition;
};

const Container = styled.div`
  overflow: auto;
`;

const Shadow = styled.div<ShadowStyledProps>`
  position: sticky;
  left: 0;
  right: 0;
  height: 1px;
  box-shadow: 0px 4px 30px 6px rgba(7, 24, 84, 0.14);
`;

const ShadowTop = styled(Shadow)`
  top: 0;
  display: ${props =>
    !props.$scrollPosition || props.$scrollPosition === "top"
      ? "none"
      : "block"};
`;

const ShadowBottom = styled(Shadow)`
  bottom: 0;
  display: ${props =>
    !props.$scrollPosition || props.$scrollPosition === "bottom"
      ? "none"
      : "block"};
`;

const ShadowedScroll = ({children, ...rest}: Props) => {
  const {elementRef, onScroll, scrollPosition} =
    useScrollPosition<ContainerElement>();

  return (
    <Container {...rest} onScroll={onScroll} ref={elementRef}>
      <ShadowTop $scrollPosition={scrollPosition} />
      {children}
      <ShadowBottom $scrollPosition={scrollPosition} />
    </Container>
  );
};

export default ShadowedScroll;
