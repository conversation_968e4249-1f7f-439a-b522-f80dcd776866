import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import styled from "styled-components";

import Button from "@/button";

import Portal from "./portal";

type Args = Record<string, never>;

const ButtonsContainer = styled.div`
  margin-bottom: 20px;
`;

const StyledButton = styled(Button)`
  margin-right: 10px;
`;

const renderPortal = (element: unknown, index: number) => (
  <Portal key={index}>Hola soy el portal {index}</Portal>
);

const Default: StoryFn<Args> = () => {
  const [portalsCount, setPortalsCount] = useState<number>(0);

  return (
    <>
      {Array.from({length: portalsCount}).map(renderPortal)}
      <ButtonsContainer>
        <StyledButton
          onClick={() => setPortalsCount(portalsCount + 1)}
          size="medium"
          variant="primary"
        >
          Add Portal
        </StyledButton>
        <Button
          disabled={portalsCount === 0}
          onClick={() => setPortalsCount(portalsCount - 1)}
          size="medium"
          variant="secondary"
        >
          Remove Portal
        </Button>
      </ButtonsContainer>
    </>
  );
};

const meta: Meta<Args> = {
  title: "portal"
};

export {meta as default, Default};
