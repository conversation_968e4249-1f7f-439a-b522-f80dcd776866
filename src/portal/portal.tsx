import {useElementStyle} from "@unlockre/utils-react/dist/use-element-style";
import {useEffect, useMemo} from "react";
import type {ReactNode} from "react";
import {createPortal} from "react-dom";

type Props = {
  children: ReactNode;
  getContainer?: () => HTMLElement;
  parent?: HTMLElement;
};

// All portals use this zIndex so we can leave the values
// from 1 to 99 to the rest of elements
const containerStyle = {
  zIndex: "100",
  position: "relative"
};

const createDiv = () => document.createElement("div");

const Portal = ({
  children,
  getContainer = createDiv,
  parent = document.body
}: Props) => {
  const container = useMemo(getContainer, [getContainer]);

  useElementStyle({
    element: container,
    style: containerStyle
  });

  useEffect(() => {
    parent.appendChild(container);

    return () => {
      parent.removeChild(container);
    };
  }, [container, parent]);

  return createPortal(children, container);
};

export default Portal;
