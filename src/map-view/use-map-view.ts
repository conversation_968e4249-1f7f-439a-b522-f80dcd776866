import areDeepEqual from "fast-deep-equal";
import {useState} from "react";

import type {CameraState} from "./types";

type Params = {
  initialCameraState?: Partial<CameraState>;
};

const useMapView = ({initialCameraState}: Params) => {
  const [map, setMap] = useState<google.maps.Map | null>(null);

  const [zoom, setZoom] = useState<number | undefined>(
    initialCameraState?.zoom
  );

  const [center, setCenter] = useState<google.maps.LatLngLiteral | undefined>(
    initialCameraState?.center
  );
  const [bounds, setBounds] = useState<
    google.maps.LatLngBoundsLiteral | undefined
  >(initialCameraState?.bounds);

  const onCameraChange = (cameraState: CameraState) => {
    setZoom(cameraState.zoom);
    setCenter(prevCenter =>
      areDeepEqual(prevCenter, cameraState.center)
        ? prevCenter
        : cameraState.center
    );
    setBounds(prevBounds =>
      areDeepEqual(prevBounds, cameraState.bounds)
        ? prevBounds
        : cameraState.bounds
    );
  };

  return {
    onCameraChange,
    map,
    setMap,
    setZoom,
    setCenter,
    center,
    zoom,
    bounds,
    setBounds
  };
};

export {useMapView};
