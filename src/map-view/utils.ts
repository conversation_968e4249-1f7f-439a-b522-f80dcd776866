const worldBounds = {
  north: 90,
  east: 180,
  south: -90,
  west: -180
};

const defaultMapCameraState = {
  center: {lat: 0, lng: 0},
  zoom: 0,
  bounds: worldBounds
};

// eslint-disable-next-line complexity
const getCameraState = (map: google.maps.Map) => {
  const center = map.getCenter();
  const zoom = map.getZoom();
  const bounds = map.getBounds();

  if (!center || !bounds || !Number.isFinite(zoom)) {
    console.warn(
      "Failed to get camera state. Falling back to default camera state."
    );
  }

  return {
    center: center?.toJSON() ?? defaultMapCameraState.center,
    zoom: zoom ?? defaultMapCameraState.zoom,
    bounds: bounds?.toJSON() ?? defaultMapCameraState.bounds
  };
};

const getSize = (
  map: google.maps.Map
): {
  height: number;
  width: number;
} => {
  const mapDiv = map.getDiv();

  return {
    height: mapDiv.offsetHeight,
    width: mapDiv.offsetWidth
  };
};

const getPointsBoundingBox = (points: google.maps.LatLngLiteral[]) => {
  if (!points.length) {
    return undefined;
  }

  // Any valid point will have north >= south and east >= west.
  const invertedWorldBounds = {
    north: -90,
    east: -180,
    south: 90,
    west: 180
  };

  return points.reduce(
    (bounds, point) => ({
      north: Math.max(bounds.north, point.lat),
      east: Math.max(bounds.east, point.lng),
      south: Math.min(bounds.south, point.lat),
      west: Math.min(bounds.west, point.lng)
    }),
    invertedWorldBounds
  );
};

export {getCameraState, getSize, getPointsBoundingBox};
