import useResizeObserver from "use-resize-observer";

import type {MapSize} from "./types";

const useMapResizeHandler = (
  map: google.maps.Map | null,
  onResize?: (size: MapSize) => void
) =>
  useResizeObserver({
    ref: map?.getDiv(),
    onResize: ({height, width}) => {
      if (typeof height === "number" && typeof width === "number") {
        onResize?.({width, height});
      }
    }
  });

export {useMapResizeHandler};
