import areDeepEqual from "fast-deep-equal";
import {useEffect} from "react";

type CameraProps = {
  bounds?: google.maps.LatLngBoundsLiteral;
  center?: google.maps.LatLngLiteral;
  zoom?: number;
};

const useMapCameraSync = (
  map: google.maps.Map | null,
  {bounds, center, zoom}: CameraProps
) => {
  useEffect(() => {
    if (!map || typeof zoom === "undefined" || zoom === map.getZoom()) {
      return;
    }

    map.setZoom(zoom);
  }, [map, zoom]);

  useEffect(() => {
    if (!map || !center || areDeepEqual(center, map.getCenter()?.toJSON())) {
      return;
    }

    map.setCenter(center);
  }, [map, center]);

  useEffect(() => {
    if (!map || !bounds || areDeepEqual(bounds, map.getBounds()?.toJSON())) {
      return;
    }

    map.fitBounds(bounds, 0);
  }, [map, bounds]);
};

export {useMapCameraSync};
