import {useEffect} from "react";
import {useDebouncedCallback} from "use-debounce";

import type {CameraState} from "./types";
import * as withGoogleMap from "./utils";

const useMapCameraChangeHandler = (
  map: google.maps.Map | null,
  onCameraChanged?: (camera: CameraState) => void
) => {
  const debouncedOnBoundsChanged = useDebouncedCallback(() => {
    if (map) {
      const cameraState = withGoogleMap.getCameraState(map);
      onCameraChanged?.(cameraState);
    }
  }, 500);

  useEffect(() => {
    let listener: google.maps.MapsEventListener;

    if (map) {
      listener = map.addListener("bounds_changed", () => {
        google.maps.event.addListenerOnce(map, "idle", () => {
          debouncedOnBoundsChanged();
        });
      });
    }

    return () => {
      listener?.remove();
    };
  }, [map, debouncedOnBoundsChanged]);
};

export {useMapCameraChangeHandler};
