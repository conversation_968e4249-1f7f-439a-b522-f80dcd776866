import {useEffect} from "react";
import type {ReactElement} from "react";
import {render} from "react-dom";

type Props = {
  children: ReactElement;
  map: google.maps.Map;
  position: google.maps.ControlPosition;
};

const MapControl = ({children, map, position}: Props) => {
  useEffect(() => {
    const controlContainer = document.createElement("div");

    render(children, controlContainer);

    map.controls[position].push(controlContainer);

    return () => {
      const controlContainerIndex = map.controls[position]
        .getArray()
        .indexOf(controlContainer);

      map.controls[position].removeAt(controlContainerIndex);
    };
  }, [children, map, position]);

  return null;
};

export {MapControl};
