import {GoogleMap} from "@react-google-maps/api";
import {useState} from "react";
import type {ComponentProps, ReactNode} from "react";

import type {CameraState, MapSize} from "./types";
import {useMapCameraChangeHandler} from "./use-map-camera-change-handler";
import {useMapCameraSync} from "./use-map-camera-sync";
import {useMapResizeHandler} from "./use-map-resize-handler";

type ChildrenRenderer = (map: google.maps.Map) => ReactNode;

type GoogleMapProps = ComponentProps<typeof GoogleMap>;

type ExposedGoogleMapProps = Omit<
  GoogleMapProps,
  | "center"
  | "children"
  | "onBoundsChanged"
  | "onLoad"
  | "onResize"
  | "onUnmount"
  | "zoom"
>;

type Props = ExposedGoogleMapProps & {
  bounds?: google.maps.LatLngBoundsLiteral;
  center?: google.maps.LatLngLiteral;
  children?: Children<PERSON>enderer;
  onCameraChange?: (cameraState: CameraState) => void;
  onResize?: (size: MapSize) => void;
  setMap?: (map: google.maps.Map | null) => void;
  zoom?: number;
};

const MapView = (props: Props) => {
  const {
    center,
    children,
    onCameraChange,
    onResize,
    options,
    zoom,
    ...restMapProps
  } = props;

  const [map, setMap] = useState<google.maps.Map | null>(null);

  useMapCameraSync(map, {
    bounds: props.bounds,
    center: props.center,
    zoom: props.zoom
  });

  useMapResizeHandler(map, onResize);
  useMapCameraChangeHandler(map, onCameraChange);

  return (
    <GoogleMap
      {...restMapProps}
      onLoad={map => {
        setMap(map);
        props.setMap?.(map);
      }}
      onUnmount={() => {
        setMap(null);
        props.setMap?.(null);
      }}
      options={options}
    >
      {map && children?.(map)}
    </GoogleMap>
  );
};

export {MapView};
