import type {ReactNode} from "react";
import styled from "styled-components";

import {getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type Props = {
  className?: string;
  label: string;
  left?: ReactNode;
  onClick: () => unknown;
};

const labelColor = "rgba(0, 0, 0, 0.9)";

const LabelContainer = styled.span`
  color: ${labelColor};
  ${getTypography("body", "s", 600)}
`;

const Container = styled(UnstyledButton)`
  align-items: center;
  background: #ffffff;
  border-radius: 5px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  padding: 10px 12px;
`;

const MapButton = ({label, left, ...rest}: Props) => (
  <Container {...rest}>
    {left}
    <LabelContainer>{label}</LabelContainer>
  </Container>
);

export default Object.assign(MapButton, {labelColor});
