import {UnstyledUl} from "@/unstyled";

import type {InfoEntry} from "./info-entry";
import type {InfoListVariant} from "./info-list-variant";
import {InfoRow} from "./info-row";

type Props = {
  className?: string;
  infoEntries: InfoEntry[];
  variant: InfoListVariant;
};

const InfoList = ({className, infoEntries, variant}: Props) => (
  <UnstyledUl {...{className}}>
    {infoEntries.map((infoEntry, index) => (
      <InfoRow {...{infoEntry, variant}} key={index} />
    ))}
  </UnstyledUl>
);

export {InfoList};
