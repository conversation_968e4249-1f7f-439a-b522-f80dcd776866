import type {ReactElement, ReactText} from "react";

type InfoEntryOf<TValue> = {
  description?: string;
  descriptionTooltipWidth?: number;
  isCollapsible?: boolean;
  title: string;
  value: TValue;
};

type TextInfoEntry = InfoEntryOf<ReactText>;

type ElementInfoEntry = InfoEntryOf<ReactElement>;

type InfoEntry = ElementInfoEntry | TextInfoEntry;

// TODO: Move to @unlockre/utils-react package
const isReactText = (value: unknown): value is ReactText =>
  typeof value === "string" || typeof value === "number";

const isTextInfoEntry = (infoItem: InfoEntry): infoItem is TextInfoEntry =>
  isReactText(infoItem.value);

export {isTextInfoEntry};
export type {ElementInfoEntry, InfoEntry, TextInfoEntry};
