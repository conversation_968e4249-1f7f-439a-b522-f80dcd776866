import {CaretDown} from "@phosphor-icons/react/dist/ssr";
import styled from "styled-components";

import {IconWithColor} from "@/icons";
import {getColor, getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {type InfoListVariant, infoListVariants} from "../info-list-variant";

import {rotatableCss} from "./rotatable-css";

type Props = {
  isCollapsed?: boolean;
  onClick: () => unknown;
  variant: InfoListVariant;
};

const iconColorGetters = {
  [infoListVariants.gray]: getColorByAlias("accentPrimary"),
  [infoListVariants.white]: getColor("gray", "000")
};

const RotatableIconWithColor = styled(IconWithColor)`
  ${rotatableCss}
`;

const Container = styled(UnstyledButton)`
  align-items: center;
  display: flex;
  height: 32px;
  justify-content: center;
  width: 32px;
`;

const ToggleInfoRowButton = ({isCollapsed, onClick, variant}: Props) => (
  <Container {...{onClick}}>
    <RotatableIconWithColor
      $rotation={isCollapsed ? 0 : 180}
      getColor={iconColorGetters[variant]}
      icon={CaretDown}
      size={14}
      weight="bold"
    />
  </Container>
);

export {ToggleInfoRowButton};
