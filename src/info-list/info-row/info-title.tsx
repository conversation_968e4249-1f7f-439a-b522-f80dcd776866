import styled from "styled-components";

import * as withTheme from "@/theme-provider/theme";

import {infoListVariants} from "../info-list-variant";
import type {InfoListVariant} from "../info-list-variant";

type StyledProps = {
  variant: InfoListVariant;
};

const colors = {
  [infoListVariants.gray]: withTheme.getColorByAlias("textSecondary"),
  [infoListVariants.white]: withTheme.getColor("gray", "000")
};

const InfoTitle = styled.div<StyledProps>`
  ${withTheme.getTypography("body", "xs")}

  color: ${props => colors[props.variant]};
  white-space: nowrap;
`;

export {InfoTitle};
