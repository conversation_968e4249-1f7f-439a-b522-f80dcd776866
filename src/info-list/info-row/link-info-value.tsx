import {LinkSimple} from "@phosphor-icons/react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {IconWithColor} from "@/icons";
import {Link} from "@/link";
import * as withTheme from "@/theme-provider/theme";

import {infoListVariants} from "../info-list-variant";
import type {InfoListVariant} from "../info-list-variant";

type ExposedLinkProps = Omit<ComponentProps<typeof Link>, "variant">;

type Props = ExposedLinkProps & {
  variant: InfoListVariant;
};

type ContainerStyledProps = {
  $variant: InfoListVariant;
};

const containerColorGetters = {
  [infoListVariants.gray]: withTheme.getColor("blue", "800"),
  [infoListVariants.white]: withTheme.getColor("gray", "000")
};

const Text = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const StyledIcon = styled(IconWithColor)`
  flex-shrink: 0;
`;

const Container = styled(Link)<ContainerStyledProps>`
  ${withTheme.getTypography("body", "s")}

  align-items: center;
  color: ${props => containerColorGetters[props.$variant]};
  display: inline-flex;
  gap: 4px;
  min-width: 0;
`;

const LinkInfoValue = ({children, variant, ...rest}: Props) => (
  <Container {...rest} $variant={variant}>
    <StyledIcon
      getColor={containerColorGetters[variant]}
      icon={LinkSimple}
      size={16}
      weight="regular"
    />
    <Text>{children}</Text>
  </Container>
);

export {LinkInfoValue};
