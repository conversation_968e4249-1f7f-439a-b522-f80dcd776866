import type {InfoEntry} from "../info-entry";
import type {InfoListVariant} from "../info-list-variant";

import {CollapsibleInfoRow} from "./collapsible-info-row";
import {NonCollapsibleInfoRow} from "./non-collapsible-info-row";

type Props = {
  infoEntry: InfoEntry;
  variant: InfoListVariant;
};

const InfoRow = (props: Props) =>
  props.infoEntry.isCollapsible ? (
    <CollapsibleInfoRow {...props} />
  ) : (
    <NonCollapsibleInfoRow {...props} />
  );

export {InfoRow};
