import {Info} from "@phosphor-icons/react";
import styled from "styled-components";

import {IconWithColor} from "@/icons";
import {getColor} from "@/theme-provider/theme";
import Tooltip, {useTooltip} from "@/tooltip";
import {tooltipPlacements} from "@/tooltip/tooltip-placement";

import {infoListVariants} from "../info-list-variant";
import type {InfoListVariant} from "../info-list-variant";

type Props = {
  description: string;
  descriptionTooltipWidth?: number;
  variant: InfoListVariant;
};

type StyledTooltipStyledProps = {
  $width?: number;
};

const iconColorGetters = {
  [infoListVariants.gray]: getColor("blue", "800"),
  [infoListVariants.white]: getColor("gray", "000")
};

const StyledTooltip = styled(Tooltip)<StyledTooltipStyledProps>`
  width: ${props => (props.$width ? `${props.$width}px` : "auto")};
`;

const StyledIconWithColor = styled(IconWithColor)`
  margin-left: 4px;
`;

const InfoIcon = ({description, descriptionTooltipWidth, variant}: Props) => {
  const {isTooltipOpened, reference, toggleTooltip, tooltipProps} = useTooltip(
    tooltipPlacements.top
  );

  return (
    <>
      <StyledIconWithColor
        getColor={iconColorGetters[variant]}
        icon={Info}
        onMouseEnter={toggleTooltip}
        onMouseLeave={toggleTooltip}
        ref={reference}
        size={16}
        weight="regular"
      />
      {isTooltipOpened && (
        <StyledTooltip
          {...tooltipProps}
          $width={descriptionTooltipWidth}
          size="small"
          variant="dark"
        >
          {description}
        </StyledTooltip>
      )}
    </>
  );
};

export {InfoIcon};
