import type {InfoEntry} from "../info-entry";
import type {InfoListVariant} from "../info-list-variant";

import {InfoRowLayout} from "./info-row-layout";
import {InfoValue} from "./info-value";

type Props = {
  infoEntry: InfoEntry;
  variant: InfoListVariant;
};

const NonCollapsibleInfoRow = (props: Props) => (
  <InfoRowLayout {...props} topRight={<InfoValue {...props} />} />
);

export {NonCollapsibleInfoRow};
