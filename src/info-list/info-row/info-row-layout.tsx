import type {ReactNode} from "react";
import styled from "styled-components";

import * as withTheme from "@/theme-provider/theme";

import type {InfoEntry} from "../info-entry";
import type {InfoListVariant} from "../info-list-variant";

import {InfoIcon} from "./info-icon";
import {InfoTitle} from "./info-title";

type Props = {
  children?: ReactNode;
  infoEntry: InfoEntry;
  topRight: ReactNode;
  variant: InfoListVariant;
};

const TopLeftContainer = styled.div`
  align-items: center;
  display: flex;
  margin-right: 40px;
`;

const renderTopLeft = ({infoEntry, variant}: Props) => (
  <TopLeftContainer>
    <InfoTitle {...{variant}}>{infoEntry.title}</InfoTitle>
    {infoEntry.description !== undefined && (
      <InfoIcon
        {...{variant}}
        description={infoEntry.description}
        descriptionTooltipWidth={infoEntry.descriptionTooltipWidth}
      />
    )}
  </TopLeftContainer>
);

const TopContainer = styled.div`
  display: flex;
  justify-content: space-between;
`;

const renderTop = (props: Props) => (
  <TopContainer>
    {renderTopLeft(props)}
    {props.topRight}
  </TopContainer>
);

const Container = styled.li`
  border-bottom-width: 1px;
  border-bottom-style: solid;
  padding: 12px 0;

  :last-child {
    border-bottom-color: transparent;
  }

  :not(:last-child) {
    border-bottom-color: ${withTheme.getColor("gray", "070")};
  }
`;

const InfoRowLayout = (props: Props) => (
  <Container>
    {renderTop(props)}
    {props.children}
  </Container>
);

export {InfoRowLayout};
