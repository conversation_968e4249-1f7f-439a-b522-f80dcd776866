import {useState} from "react";

import type {InfoEntry} from "../info-entry";
import type {InfoListVariant} from "../info-list-variant";

import {InfoRowLayout} from "./info-row-layout";
import {InfoValue} from "./info-value";
import {ToggleInfoRowButton} from "./toggle-info-row-button";

type Props = {
  infoEntry: InfoEntry;
  variant: InfoListVariant;
};

const CollapsibleInfoRow = (props: Props) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const toggle = () => setIsCollapsed(isCollapsed => !isCollapsed);

  return (
    <InfoRowLayout
      {...props}
      topRight={
        <ToggleInfoRowButton
          {...{isCollapsed}}
          onClick={toggle}
          variant={props.variant}
        />
      }
    >
      {!isCollapsed && <InfoValue {...props} />}
    </InfoRowLayout>
  );
};

export {CollapsibleInfoRow};
