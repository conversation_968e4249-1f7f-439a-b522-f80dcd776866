import {isUrl} from "@unlockre/utils-validation/dist";

import * as withInfoEntry from "../info-entry";
import type {InfoEntry} from "../info-entry";
import type {InfoListVariant} from "../info-list-variant";

import {LinkInfoValue} from "./link-info-value";
import {TextInfoValue} from "./text-info-value";

type Props = {
  infoEntry: InfoEntry;
  variant: InfoListVariant;
};

const InfoValue = ({infoEntry, ...rest}: Props) => {
  if (!withInfoEntry.isTextInfoEntry(infoEntry)) {
    return infoEntry.value;
  }

  return isUrl(String(infoEntry.value)) ? (
    <LinkInfoValue variant={rest.variant}>{infoEntry.value}</LinkInfoValue>
  ) : (
    <TextInfoValue
      {...rest}
      noWrap={!infoEntry.isCollapsible}
      title={String(infoEntry.value)}
    >
      {infoEntry.value}
    </TextInfoValue>
  );
};
export {InfoValue};
