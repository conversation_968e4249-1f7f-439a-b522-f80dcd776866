import styled, {css} from "styled-components";

import * as withTheme from "@/theme-provider/theme";

import {infoListVariants} from "../info-list-variant";
import type {InfoListVariant} from "../info-list-variant";

type StyledProps = {
  noWrap?: boolean;
  variant: InfoListVariant;
};

const colors = {
  [infoListVariants.gray]: withTheme.getColorByAlias("textPrimary"),
  [infoListVariants.white]: withTheme.getColor("gray", "000")
};

const noWrapCss = css`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const TextInfoValue = styled.div<StyledProps>`
  ${props => props.noWrap && noWrapCss}

  ${withTheme.getTypography("body", "s")}

  color: ${props => colors[props.variant]};
`;

export {TextInfoValue};
