import type {Meta, StoryFn} from "@storybook/react";
import styled from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

import type {TextInfoEntry} from "./info-entry";
import {InfoList} from "./info-list";
import {infoListVariants} from "./info-list-variant";
import type {InfoListVariant} from "./info-list-variant";

type Args = {
  infoEntries: TextInfoEntry[];
  variant: InfoListVariant;
};

type ContainerStyledProps = {
  $variant: InfoListVariant;
};

const containerBackgroundColors = {
  [infoListVariants.gray]: getColorByAlias("backgroundWhite"),
  [infoListVariants.white]: getColor("blue", "450")
};

const Container = styled.div<ContainerStyledProps>`
  background-color: ${props => containerBackgroundColors[props.$variant]};
  border: 1px solid ${getColor("gray", "100")};
  padding: 16px;
`;

const Default: StoryFn<Args> = args => (
  <Container $variant={args.variant}>
    <InfoList {...args} />
  </Container>
);

Default.parameters = {
  // TODO: Point this to the design in the components library once we have it there
  design: {
    type: "figma",
    url: "https://www.figma.com/file/C3waJMAyXf5Lt1pqOkHZq0/Admin?node-id=5670%3A216425"
  }
};

const meta: Meta<Args> = {
  title: "info-list",
  argTypes: {
    infoEntries: {
      control: "object"
    },
    variant: {
      control: "radio",
      options: Object.values(infoListVariants)
    }
  },
  args: {
    infoEntries: [
      {
        title: "Option",
        value: "3, 5 - year"
      },
      {
        isCollapsible: true,
        title: "Explanation",
        value:
          "This is a very, very, very, very, very, very, very, very, very, very, very, very, very, very, very large text"
      },
      {
        description:
          "Type of guarantee backing the lease. This could be personal or corporate.",
        title: "Offer Earnest Money Deposit",
        value: "$20,000"
      }
    ],
    variant: infoListVariants.gray
  }
};

export {meta as default, Default};
