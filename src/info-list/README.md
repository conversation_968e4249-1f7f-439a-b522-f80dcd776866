# InfoList

## About

This component renders a list of `InfoEntry` objects.

```ts
import type {ReactElement, ReactText} from "react";

type InfoEntryOf<TValue> = {
  description?: string;
  isCollapsible?: boolean;
  title: string;
  value: TValue;
};

type TextInfoEntry = InfoEntryOf<ReactText>;

type ElementInfoEntry = InfoEntryOf<ReactElement>;

type InfoEntry = ElementInfoEntry | TextInfoEntry;
```

An additional component `TextInfoValue` is available for cases where the text needs to be wrapped with some other component.

## Example

```tsx
import {InfoList, TextInfoItemValue} from "@unlockre/components-library/dist/info-list";
import {Link} from "@unlockre/components-library/dist/link";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {getColor, getColorByAlias} from "@unlockre/components-library/dist/theme-provider/theme";

type ContainerStyledProps = {
  $variant: Variant;
};

const infoEntries = [
  {
    title: "Option",
    value: "3, 5 - year"
  },
  {
    isCollapsible: true,
    title: "Explanation",
    value:
      "This is a very, very, very, very, very, very, very, very, very, very, very, very, very, very, very large text"
  },
  {
    title: "Offer Earnest Money Deposit",
    description: "Type of guarantee backing the lease. This could be personal or corporate.",
    value: "$20,000"
  },
  {
    title: "Business Website",
    value: (
      <Link href="https://www.some-business.com">
        <TextInfoItemValue>https://www.some-business.com</TextInfoItemValue>
      </Link>
    )
  }
];

const containerBackgroundColors = {
  [variants.gray]: getColorByAlias("backgroundWhite"),
  [variants.white]: getColor("blue", "450")
};

const Container = styled.div<ContainerStyledProps>`
  background-color: ${props => containerBackgroundColors[props.$variant]};
  border: 1px solid ${getColor("gray", 100)};
  padding: 16px;
`;

const Example = () => (
  <ThemeProvider>
    <Container variant="gray">
      <InfoItemList {...{infoEntries}} variant="gray" />
    </Container>
    <Container variant="white">
      <InfoItemList {...{infoEntries}} variant="white" />
    </Container>
  </ThemeProvider>
);

export {Example};
```
