import Slider from "@mui/material/Slider";
import {useEffect, useState} from "react";
import type {SyntheticEvent} from "react";
import styled from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider";

type Props = {
  formatValue?: (value: number) => number | string;
  limits: [number, number];
  onChange: (value: [number, number]) => void;
  value: [number, number];
};

const Container = styled.div`
  & .MuiSlider-root {
    margin-bottom: 8px;
  }

  & .MuiSlider-thumb {
    background: #ffffff;
    border: 2px solid #0d43a5;
    box-shadow: 0px 0px 14px rgba(18, 113, 255, 0.3);
  }

  & .MuiSlider-track {
    border: 1px solid #0d43a5;
  }

  & .MuiSlider-rail {
    border: 1px solid #a0c6ff;
    opacity: 1;
  }
`;

const Values = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  line-height: 15px;
  color: rgba(0, 0, 0, 0.9);
`;

const identity = <TValue,>(value: TValue) => value;

const RangeSlider = ({
  formatValue = identity,
  limits,
  onChange,
  value
}: Props) => {
  const [instantValue, setInstantValue] = useState<[number, number]>(value);

  useEffect(() => {
    setInstantValue(value);
  }, [value]);

  const handleChange = (event: Event, newValue: number[] | number) => {
    setInstantValue(newValue as [number, number]);
  };

  const handleChangeCommitted = (
    event: Event | SyntheticEvent,
    newValue: number[] | number
  ) => {
    onChange(newValue as [number, number]);
  };

  return (
    <Container>
      <MuiThemeProvider>
        <Slider
          max={limits[1]}
          min={limits[0]}
          onChange={handleChange}
          onChangeCommitted={handleChangeCommitted}
          value={instantValue}
          valueLabelDisplay="off"
        />
      </MuiThemeProvider>
      <Values>
        <span>{formatValue(instantValue[0])}</span>
        <span>{formatValue(instantValue[1])}</span>
      </Values>
    </Container>
  );
};

export default RangeSlider;
