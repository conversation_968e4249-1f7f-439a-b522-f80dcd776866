import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import RangeSlider from "./range-slider";

type RangeSliderProps = ComponentProps<typeof RangeSlider>;

type OnChange = RangeSliderProps["onChange"];

type Args = Pick<RangeSliderProps, "limits" | "value">;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  const onChange: OnChange = value => updateArgs({value});

  return <RangeSlider {...args} {...{onChange}} />;
};

const meta: Meta<Args> = {
  title: "range-slider",
  argTypes: {
    limits: {
      control: "object"
    },
    value: {
      control: "object"
    }
  }
};

export {meta as default, Default};
