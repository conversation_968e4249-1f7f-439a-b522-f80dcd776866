import {ExpandLess, ExpandMore} from "@styled-icons/material";
import {forwardRef} from "react";
import type {ComponentProps, ForwardedRef, ReactElement, Ref} from "react";
import styled from "styled-components";

import type {SingleItemListProps} from "@/item-list";
import Pill from "@/pill";
import type {PillType, PillVariant} from "@/pill/pill";
import {UnstyledButton} from "@/unstyled";

type PillButtonElement = HTMLButtonElement;

type IconName = "expandLess" | "expandMore";

type ExposedSingleItemListProps<TItem> = Pick<
  SingleItemListProps<TItem>,
  "getItemLabel" | "value"
>;

type PillProps = ComponentProps<typeof Pill>;

type ExposedPillProps = Pick<PillProps, "heightVariant">;

type Props<TItem> = ExposedPillProps &
  ExposedSingleItemListProps<TItem> & {
    className?: string;
    iconName: IconName;
    onClick: () => unknown;
    placeholder?: string;
    type: PillType;
    variant: PillVariant;
  };

type PillButtonComponent = <TItem>(
  props: Props<TItem> & {ref?: Ref<PillButtonElement>}
) => ReactElement;

const iconSize = 16;

const renderIcon = (iconName: IconName) => {
  switch (iconName) {
    case "expandLess":
      return <ExpandLess size={iconSize} />;

    case "expandMore":
      return <ExpandMore size={iconSize} />;
  }
};

const StyledButton = styled(UnstyledButton)`
  white-space: nowrap;
`;

const PillButtonRenderer = <TItem,>(
  {
    getItemLabel,
    iconName,
    onClick,
    placeholder,
    type = "information",
    value,
    variant = "primary",
    ...rest
  }: Props<TItem>,
  ref: ForwardedRef<PillButtonElement>
) => (
  <StyledButton {...{onClick, ref}}>
    <Pill
      {...rest}
      label={value ? getItemLabel(value) : placeholder ?? ""}
      rightElement={renderIcon(iconName)}
      type={type}
      variant={variant}
    />
  </StyledButton>
);

const PillButton = forwardRef(PillButtonRenderer) as PillButtonComponent;

export default PillButton;
