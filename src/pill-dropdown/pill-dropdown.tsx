import type {ComponentProps} from "react";
import styled from "styled-components";

import {SingleItemList, useSingleItemList} from "@/item-list";
import type {SingleItemListProps, UseSingleItemListParams} from "@/item-list";

import PillButton from "./pill-button";

type ExposedUseSingleItemListParams<TItem> = Pick<
  UseSingleItemListParams<TItem>,
  "renderFooter"
>;

type ExposedSingleItemListProps<TItem> = Pick<
  SingleItemListProps<TItem>,
  "getItemLabel" | "itemGroups" | "items" | "onChange" | "value"
>;

type PillButtonProps = ComponentProps<typeof PillButton>;

type ExposedPillButtonProps = Pick<
  PillButtonProps,
  "heightVariant" | "type" | "variant"
>;

type Props<TItem> = ExposedPillButtonProps &
  ExposedSingleItemListProps<TItem> &
  ExposedUseSingleItemListParams<TItem> & {
    className?: string;
    placeholder?: string;
  };

type ItemListOnChange<TItem> = SingleItemListProps<TItem>["onChange"];

const Container = styled.div`
  position: relative;
`;

const PillDropdown = <TItem,>({
  className,
  getItemLabel,
  itemGroups,
  items,
  onChange,
  placeholder,
  renderFooter,
  value,
  ...rest
}: Props<TItem>) => {
  const handleItemSelect: ItemListOnChange<TItem> = item => {
    toggleItemList();

    onChange(item);
  };

  const {isItemListOpened, itemListProps, setAnchorageElement, toggleItemList} =
    useSingleItemList({
      getItemLabel,
      itemGroups,
      items,
      onItemSelect: handleItemSelect,
      renderFooter
    });

  const renderItemList = () => (
    <SingleItemList
      {...{getItemLabel, items, value}}
      {...itemListProps}
      onChange={handleItemSelect}
    />
  );

  return (
    <Container className={className}>
      <PillButton
        {...rest}
        {...{placeholder, getItemLabel, value}}
        iconName={isItemListOpened ? "expandLess" : "expandMore"}
        onClick={toggleItemList}
        ref={setAnchorageElement}
      />
      {isItemListOpened && renderItemList()}
    </Container>
  );
};

export default PillDropdown;

export type {Props as PillDropdownProps};
