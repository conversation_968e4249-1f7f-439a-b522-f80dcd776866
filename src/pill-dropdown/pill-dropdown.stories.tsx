import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import PillDropdown from "./pill-dropdown";
import type {PillDropdownProps} from "./pill-dropdown";

const inputTypes = {
  itemGroups: "itemGroups",
  items: "items"
} as const;

type InputType = (typeof inputTypes)[keyof typeof inputTypes];

type PillDropdownItem = {
  label: string;
  value: number;
};

type PillDropdownArgs = Pick<
  PillDropdownProps<PillDropdownItem>,
  "heightVariant" | "itemGroups" | "items" | "type" | "variant"
>;

type StoryArgs = PillDropdownArgs & {
  inputType: InputType;
};

const getPillDropdownItemLabel = ({label}: PillDropdownItem) => label;

const Default: StoryFn<StoryArgs> = ({
  inputType,
  itemGroups,
  items,
  ...rest
}) => {
  const [value, setValue] = useState<PillDropdownItem | null>(null);

  const inputArgs =
    inputType === inputTypes.itemGroups ? {itemGroups} : {items};

  return (
    <PillDropdown
      {...{value}}
      {...inputArgs}
      {...rest}
      getItemLabel={getPillDropdownItemLabel}
      onChange={setValue}
      placeholder="Select an option"
    />
  );
};

const meta: Meta<StoryArgs> = {
  title: "pill-dropdown",
  argTypes: {
    type: {
      control: "radio",
      options: ["error", "information", "success", "warning"]
    },
    variant: {
      control: "radio",
      options: ["primary", "secondary", "inverted"]
    },
    heightVariant: {
      control: "radio",
      options: ["short", "tall"]
    },
    inputType: {
      control: "radio",
      options: Object.values(inputTypes)
    },
    itemGroups: {
      control: "object"
    },
    items: {
      control: "object"
    }
  },
  args: {
    type: "information",
    variant: "primary",
    heightVariant: "short",
    inputType: inputTypes.items,
    itemGroups: [
      {
        name: "group1",
        label: "Group 1",
        items: [
          {label: "one", value: 1},
          {label: "two", value: 2},
          {label: "three", value: 3},
          {label: "four", value: 4},
          {label: "five", value: 5},
          {label: "six", value: 6}
        ]
      },
      {
        name: "group2",
        label: "Group 2",
        items: [
          {label: "seven", value: 7},
          {label: "eight", value: 8},
          {label: "nine", value: 9},
          {label: "ten", value: 10},
          {label: "eleven", value: 11},
          {label: "twelve", value: 12}
        ]
      },
      {
        name: "group3",
        label: "Group 3",
        items: [
          {label: "thirteen", value: 13},
          {label: "fourteen", value: 14},
          {label: "fifteen", value: 15},
          {label: "sixteen", value: 16},
          {label: "seventeen", value: 17},
          {label: "eighteen", value: 18}
        ]
      }
    ],
    items: [
      {label: "one", value: 1},
      {label: "two", value: 2},
      {label: "three", value: 3},
      {label: "four", value: 4},
      {label: "five", value: 5},
      {label: "six", value: 6},
      {label: "seven", value: 7},
      {label: "eight", value: 8},
      {label: "nine", value: 9},
      {label: "ten", value: 10},
      {label: "eleven", value: 11},
      {label: "twelve", value: 12},
      {label: "thirteen", value: 13},
      {label: "fourteen", value: 14},
      {label: "fifteen", value: 15},
      {label: "sixteen", value: 16},
      {label: "seventeen", value: 17},
      {label: "eighteen", value: 18},
      {label: "nineteen", value: 19},
      {label: "twenty", value: 20}
    ]
  }
};

export {meta as default, Default};
