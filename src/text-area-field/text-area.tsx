import {forwardRef} from "react";
import type {ChangeEventHandler} from "react";
import styled from "styled-components";

import {FieldBoxContainer} from "@/field";
import {fieldBoxTextCss, fieldPlaceholderCss} from "@/field/field-styles";
import {UnstyledTextArea} from "@/unstyled";

import {textAreaFieldClassNames} from "./class-names";

type Props = {
  className?: string;
  defaultValue?: string;
  hasError?: boolean;
  isDisabled?: boolean;
  onChange?: ChangeEventHandler<HTMLTextAreaElement>;
  placeholder?: string;
  value?: string;
};

const StyledTextArea = styled(UnstyledTextArea)`
  ${fieldBoxTextCss}

  flex: 1;
  resize: none;

  &::placeholder {
    ${fieldPlaceholderCss};
  }
`;

const StyledFieldBoxContainer = styled(FieldBoxContainer)`
  display: flex;
  height: 112px;
  padding-bottom: ${FieldBoxContainer.paddingHorizontal}px;
  padding-top: ${FieldBoxContainer.paddingHorizontal}px;
`;

const TextArea = forwardRef<HTMLTextAreaElement, Props>(
  ({className, hasError, isDisabled, ...rest}, ref) => (
    <StyledFieldBoxContainer
      {...{className}}
      $hasError={hasError}
      $isDisabled={isDisabled}
    >
      <StyledTextArea
        {...{ref}}
        {...rest}
        className={textAreaFieldClassNames.textArea}
        disabled={isDisabled}
      />
    </StyledFieldBoxContainer>
  )
);

export default TextArea;
