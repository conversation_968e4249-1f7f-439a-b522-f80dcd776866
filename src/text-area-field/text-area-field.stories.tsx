import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import TextAreaField from "./text-area-field";

type TextAreaFieldProps = ComponentProps<typeof TextAreaField>;

type Args = Pick<
  TextAreaFieldProps,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "label"
  | "placeholder"
  | "value"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return (
    <TextAreaField
      {...args}
      onChange={event => updateArgs({value: event.target.value})}
    />
  );
};

const meta: Meta<Args> = {
  title: "text-area-field",
  argTypes: {
    hasError: {
      control: "boolean"
    },
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    },
    value: {
      control: "text"
    }
  }
};

export {meta as default, Default};
