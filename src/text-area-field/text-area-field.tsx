import {forwardRef} from "react";
import type {ComponentProps, ComponentRef} from "react";

import {FieldContainer} from "@/field";

import {textAreaFieldClassNames} from "./class-names";
import TextArea from "./text-area";

type TextAreaRef = ComponentRef<typeof TextArea>;

type TextAreaProps = ComponentProps<typeof TextArea>;

type FieldContainerProps = ComponentProps<typeof FieldContainer>;

type ExposedFieldContainerProps = Omit<FieldContainerProps, "children">;

type Props = ExposedFieldContainerProps & TextAreaProps;

const TextAreaField = forwardRef<TextAreaRef, Props>(
  (
    {
      defaultValue,
      hasError,
      isDisabled,
      onChange,
      placeholder,
      value,
      ...containerProps
    },
    ref
  ) => (
    <FieldContainer {...{hasError, isDisabled}} {...containerProps}>
      <TextArea
        {...{
          defaultValue,
          hasError,
          isDisabled,
          onChange,
          placeholder,
          ref,
          value
        }}
        className={textAreaFieldClassNames.textAreaContainer}
      />
    </FieldContainer>
  )
);

export default TextAreaField;
