import type {Meta, StoryFn} from "@storybook/react";

import ErrorInfo from "./error-info";
import ErrorInfoDescription from "./error-info-description";

type Args = {
  description: string;
};

const Default: StoryFn<Args> = args => (
  <ErrorInfo>
    <ErrorInfoDescription>{args.description}</ErrorInfoDescription>
  </ErrorInfo>
);

const meta: Meta<Args> = {
  title: "error-info",
  argTypes: {
    description: {
      control: "text"
    }
  },
  args: {
    description: "Some error description"
  }
};

export {meta as default, Default};
