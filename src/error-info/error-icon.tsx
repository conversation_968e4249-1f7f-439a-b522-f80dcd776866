import styled from "styled-components";

import * as withTheme from "@/theme-provider/theme";

type Props = {
  className?: string;
  size: number;
};

const StyledPath = styled.path`
  fill: ${withTheme.getColor("blue", "500")};
`;

const ErrorIcon = ({size, ...rest}: Props) => (
  <svg
    {...rest}
    fill="none"
    height={size}
    viewBox="0 0 140 140"
    xmlns="http://www.w3.org/2000/svg"
  >
    <StyledPath d="M70 0C31.4043 0 0 31.4043 0 70C0 81.457 3.02148 92.1758 7.91602 101.719L0.328125 128.898C-1.43555 135.215 4.79883 141.449 11.1152 139.672L38.2949 132.084C47.8516 136.979 58.543 140 70 140C108.596 140 140 108.596 140 70C140 31.4043 108.596 0 70 0ZM70 10.5C102.922 10.5 129.5 37.0781 129.5 70C129.5 102.922 102.922 129.5 70 129.5C59.4863 129.5 49.6699 126.766 41.1113 121.994C39.8945 121.311 38.4727 121.146 37.1328 121.516L11.2793 128.734L18.498 102.895C18.8672 101.555 18.7031 100.133 18.0195 98.9297C13.248 90.3438 10.5 80.5137 10.5 70C10.5 37.0781 37.0781 10.5 70 10.5ZM69.918 31.4316C67.0195 31.4727 64.709 33.8516 64.75 36.75V78.75C64.7227 80.6367 65.7207 82.4004 67.3477 83.3574C68.9883 84.3145 71.0117 84.3145 72.6523 83.3574C74.2793 82.4004 75.2773 80.6367 75.25 78.75V36.75C75.2637 35.3281 74.7168 33.9609 73.7051 32.9629C72.707 31.9512 71.3398 31.4043 69.918 31.4316ZM70 94.5C66.1309 94.5 63 97.6309 63 101.5C63 105.369 66.1309 108.5 70 108.5C73.8691 108.5 77 105.369 77 101.5C77 97.6309 73.8691 94.5 70 94.5Z" />
  </svg>
);

export default ErrorIcon;
