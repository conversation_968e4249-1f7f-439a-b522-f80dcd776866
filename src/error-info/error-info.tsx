import type {ReactNode} from "react";
import styled from "styled-components";

import * as withTheme from "@/theme-provider/theme";

import ErrorIcon from "./error-icon";

type Props = {
  children?: ReactNode;
};

const Title = styled.h2`
  ${withTheme.getTypography("title", "m", 600)}

  color: ${withTheme.getColor("gray", "900")};
  margin-bottom: 12px;
  margin-top: 60px;
`;

const InnerContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  width: 368px;
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
`;

const ErrorInfo = ({children}: Props) => (
  <Container>
    <InnerContainer>
      <ErrorIcon size={140} />
      <Title>Oops, something went wrong.</Title>
      {children}
    </InnerContainer>
  </Container>
);

export default ErrorInfo;
