# ErrorInfo

## About

This component can be rendered when an error occurs to show something to the user.

## Examples

### Example with just a description

```tsx
import ErrorInfo, {ErrorInfoDescription} from "@unlockre/components-library/dist/error-info";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

const SomeErrorInfo = () => (
  <ThemeProvider>
    <ErrorInfo>
      <ErrorInfoDescription>Some Error description</ErrorInfoDescription>
    </ErrorInfo>
  </ThemeProvider>
);

export default SomeErrorInfo;
```

### Example with a description and additional stuff

```tsx
import Button from "@unlockre/components-library/dist/button";
import ErrorInfo, {ErrorInfoDescription} from "@unlockre/components-library/dist/error-info";
import styled from "styled-components";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

const RefreshButton = styled(Button)`
  width: 206px;
`;

const SomeErrorInfo = () => (
  <ThemeProvider>
    <ErrorInfo>
      <ErrorInfoDescription>Some Error description</ErrorInfoDescription>
      <RefreshButton
        onClick={() => window.location.reload()}
        size="medium"
        variant="primary"
      >
        Refresh
      </RefreshButton>
    </ErrorInfo>
  </ThemeProvider>
);

export default SomeErrorInfo;
```
