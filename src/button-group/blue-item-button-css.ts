import {getColor, getColorByAlias} from "@/theme-provider/theme";

import {
  createActiveItemButtonCss,
  createInactiveItemButtonCss
} from "./item-button-css";

const getInactiveBlueItemButtonBackgroundColor = getColor("blue", "100");

const inactiveBlueItemButtonCss = createInactiveItemButtonCss(
  getColorByAlias("accentTertiary"),
  getInactiveBlueItemButtonBackgroundColor,
  getColor("blue", "200")
);

const activeBlueItemButtonCss = createActiveItemButtonCss(
  getColor("gray", "000"),
  getColorByAlias("accentTertiary"),
  getColorByAlias("accentTertiary")
);

export {
  activeBlueItemButtonCss,
  getInactiveBlueItemButtonBackgroundColor,
  inactiveBlueItemButtonCss
};
