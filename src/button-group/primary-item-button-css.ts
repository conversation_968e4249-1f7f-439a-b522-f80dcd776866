import {getColorByAlias} from "@/theme-provider/theme";

import {
  createActiveItemButtonCss,
  createInactiveItemButtonCss
} from "./item-button-css";

const getInactivePrimaryItemButtonBackgroundColor =
  getColorByAlias("bgAccentSecondary");

const inactivePrimaryItemButtonCss = createInactiveItemButtonCss(
  getColorByAlias("textOnAccentSecondary"),
  getInactivePrimaryItemButtonBackgroundColor,
  getColorByAlias("bgAccentSecondaryHover")
);

const activePrimaryItemButtonCss = createActiveItemButtonCss(
  getColorByAlias("textOnAccent"),
  getColorByAlias("bgAccent"),
  getColorByAlias("bgAccentHover")
);

export {
  activePrimaryItemButtonCss,
  getInactivePrimaryItemButtonBackgroundColor,
  inactivePrimaryItemButtonCss
};
