import {ButtonGroupContainer} from "./button-group-container";
import {ItemButton} from "./item-button";
import type {ItemButtonProps} from "./item-button";
import {variants} from "./variant";
import type {Variant} from "./variant";

type ExposedItemButtonProps<TItem> = Omit<ItemButtonProps<TItem>, "item">;

type Props<TItem> = ExposedItemButtonProps<TItem> & {
  className?: string;
  getItemLabel: (item: TItem) => string;
  isExpanded?: boolean;
  items: TItem[];
  variant?: Variant;
};

const ButtonGroup = <TItem,>({
  className,
  getItemLabel,
  isExpanded,
  items,
  variant = variants.primary,
  ...rest
}: Props<TItem>) => (
  <ButtonGroupContainer
    {...{className}}
    $isExpanded={isExpanded}
    $variant={variant}
  >
    {items.map(item => (
      <ItemButton
        {...{getItemLabel, isExpanded, item, variant}}
        {...rest}
        key={getItemLabel(item)}
      />
    ))}
  </ButtonGroupContainer>
);

export {ButtonGroup};
export type {Props as ButtonGroupProps};
