import colorAlpha from "color-alpha";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

import {
  createActiveItemButtonCss,
  createInactiveItemButtonCss
} from "./item-button-css";

const getInactiveWhiteItemButtonBackgroundColor = getColor("gray", "000");

const inactiveWhiteItemButtonCss = createInactiveItemButtonCss(
  getColorByAlias("textSecondary"),
  getInactiveWhiteItemButtonBackgroundColor,
  props => colorAlpha(getColor("gray", "1000")(props), 0.05)
);

const activeWhiteItemButtonCss = createActiveItemButtonCss(
  getColor("gray", "000"),
  getColorByAlias("accentTertiary"),
  getColorByAlias("accentTertiary")
);

export {
  activeWhiteItemButtonCss,
  getInactiveWhiteItemButtonBackgroundColor,
  inactiveWhiteItemButtonCss
};
