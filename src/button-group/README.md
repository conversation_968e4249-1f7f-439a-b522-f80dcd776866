# ButtonGroup

## About

This component receives a list of items and renders a group of buttons contiguously to pick one of them.

## Variants

This component supports the following color variants:

- `blue` (deprecated)
- `primary`
- `white` (deprecated)

> `primary` variant is the only variant that fully aligns with the latest theme, so it's the only one not deprecated. More like this variant will come in the near future like: `secondary` and `tertiary`.

## Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {ButtonGroup} from "@unlockre/components-library/dist/button-group";

type MyItem = {
  label: string;
  name: string;
}

const items = [
  {
    label: "Item 1",
    name: "item1"
  },
  {
    label: "Item 2",
    name: "item2"
  }
];

const getItemLabel = (item: MyItem) => item.label;

const MyComponent = () => {
  const [selectedItem, setSelectedItem] = useState<MyItem | undefined>(undefined);

  return(
    <ThemeProvider>
      <ButtonGroup
        {...{getItemLabel, items, selectedItem}}
        onItemSelect={setSelectedItem}
        variant="white"
      />
    </ThemeProvider>
  )
};

export default MyComponent;
```
