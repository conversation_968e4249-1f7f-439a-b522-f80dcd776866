import {css, keyframes} from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

const slideInKeyframes = keyframes`
  0% {
    opacity: 0;
    translate: -24px 0;
  }

  100% {
    opacity: 1;
    translate: 0 0;
  }
`;

const slideOutKeyframes = keyframes`
  0% {
    opacity: 1;
    translate: 0 0;
  }

  100% {
    opacity: 0;
    translate: 24px 0;
  }
`;

const createInactiveItemButtonCss = (
  getVariantColor: GetColor,
  getVariantBackgroundColor: GetColor,
  getVariantHoveredBackgroundColor: GetColor
) => css`
  background: ${getVariantBackgroundColor};
  color: ${getVariantColor};

  ::before {
    animation: ${slideOutKeyframes} 150ms ease-in-out forwards;
    background: ${getVariantHoveredBackgroundColor};
  }

  :hover::before {
    animation: ${slideInKeyframes} 150ms ease-in-out forwards;
  }
`;

const createActiveItemButtonCss = (
  getVariantColor: GetColor,
  getVariantBackgroundColor: GetColor,
  getVariantHoveredBackgroundColor: GetColor
) => css`
  background: ${getVariantBackgroundColor};
  color: ${getVariantColor};

  ::before {
    background: ${getVariantHoveredBackgroundColor};
    opacity: 0;
  }
`;

export {createActiveItemButtonCss, createInactiveItemButtonCss};
