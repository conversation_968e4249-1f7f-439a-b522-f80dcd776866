import type {ComponentProps} from "react";
import styled, {css} from "styled-components";

import {getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {
  activeBlueItemButtonCss,
  inactiveBlueItemButtonCss
} from "./blue-item-button-css";
import {buttonGroupContainerBorderRadius} from "./button-group-container";
import {itemButtonStatuses} from "./item-button-status";
import type {ItemButtonStatus} from "./item-button-status";
import {
  activePrimaryItemButtonCss,
  inactivePrimaryItemButtonCss
} from "./primary-item-button-css";
import {variants} from "./variant";
import type {Variant} from "./variant";
import {
  activeWhiteItemButtonCss,
  inactiveWhiteItemButtonCss
} from "./white-item-button-css";

type Props<TItem> = {
  getItemLabel: (item: TItem) => string;
  isExpanded?: boolean;
  item: TItem;
  onItemSelect: (item: TItem) => unknown;
  selectedItem?: TItem;
  variant: Variant;
};

type ContainerStyledProps = {
  $isExpanded?: boolean;
  $itemButtonStatus: ItemButtonStatus;
  $variant: Variant;
};

type UnstyledButtonProps = ComponentProps<typeof UnstyledButton>;

type ContainerProps = ContainerStyledProps & UnstyledButtonProps;

const cssByVariant = {
  [variants.blue]: {
    [itemButtonStatuses.active]: activeBlueItemButtonCss,
    [itemButtonStatuses.inactive]: inactiveBlueItemButtonCss
  },
  [variants.primary]: {
    [itemButtonStatuses.active]: activePrimaryItemButtonCss,
    [itemButtonStatuses.inactive]: inactivePrimaryItemButtonCss
  },
  [variants.white]: {
    [itemButtonStatuses.active]: activeWhiteItemButtonCss,
    [itemButtonStatuses.inactive]: inactiveWhiteItemButtonCss
  }
};

const getVariantCss = (props: ContainerProps) =>
  cssByVariant[props.$variant][props.$itemButtonStatus];

const containerBorderRadius = buttonGroupContainerBorderRadius;

const expandedContainerCss = css`
  flex: 1;
`;

const getExpandedContainerCss = (props: ContainerProps) =>
  props.$isExpanded && expandedContainerCss;

const Container = styled(UnstyledButton)<ContainerStyledProps>`
  ${getTypography("body", "xs", 600)}

  ${getExpandedContainerCss}

  ${getVariantCss}

  border-radius: ${containerBorderRadius}px;
  overflow: hidden;
  padding: 0 12px;
  position: relative;
  white-space: nowrap;

  ::before {
    border-radius: ${containerBorderRadius}px;
    content: "";
    height: 100%;
    left: 0;
    mix-blend-mode: darken;
    position: absolute;
    top: 0;
    width: 100%;
  }
`;

const ItemButton = <TItem,>({
  getItemLabel,
  isExpanded,
  item,
  onItemSelect,
  selectedItem,
  variant
}: Props<TItem>) => (
  <Container
    $isExpanded={isExpanded}
    $itemButtonStatus={
      item === selectedItem
        ? itemButtonStatuses.active
        : itemButtonStatuses.inactive
    }
    $variant={variant}
    onClick={() => onItemSelect(item)}
  >
    {getItemLabel(item)}
  </Container>
);

export {ItemButton};
export type {Props as ItemButtonProps};
