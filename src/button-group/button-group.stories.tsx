import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import {ButtonGroup} from "./button-group";
import type {ButtonGroupProps} from "./button-group";
import {variants} from "./variant";

type ButtonGroupItem = {
  label: string;
  name: string;
};

type StoryArgs = Pick<
  ButtonGroupProps<ButtonGroupItem>,
  "isExpanded" | "items" | "variant"
>;

const getButtonGroupItemLabel = (buttonGroupItem: ButtonGroupItem) =>
  buttonGroupItem.label;

const Default: StoryFn<StoryArgs> = ({items, ...rest}) => {
  const [selectedItem, setSelectedItem] = useState<ButtonGroupItem>(
    items?.[0] as ButtonGroupItem
  );

  return (
    <ButtonGroup
      {...{items, selectedItem}}
      {...rest}
      getItemLabel={getButtonGroupItemLabel}
      onItemSelect={setSelectedItem}
    />
  );
};

const meta: Meta<StoryArgs> = {
  title: "button-group",
  argTypes: {
    isExpanded: {
      control: "boolean"
    },
    items: {
      control: "object"
    },
    variant: {
      control: "radio",
      options: Object.values(variants)
    }
  },
  args: {
    items: [
      {label: "Rent Roll", name: "rentRoll"},
      {label: "Overview", name: "overview"},
      {label: "File Manager", name: "fileManager"}
    ],
    variant: variants.blue
  }
};

export {meta as default, Default};
