import styled, {css} from "styled-components";

import {getInactiveBlueItemButtonBackgroundColor} from "./blue-item-button-css";
import {getInactivePrimaryItemButtonBackgroundColor} from "./primary-item-button-css";
import {variants} from "./variant";
import type {Variant} from "./variant";
import {getInactiveWhiteItemButtonBackgroundColor} from "./white-item-button-css";

type ContainerStyledProps = {
  $isExpanded?: boolean;
  $variant: Variant;
};

const height = 32;

const borderRadius = height / 2;

const getBackgroundColorByVariant = {
  [variants.blue]: getInactiveBlueItemButtonBackgroundColor,
  [variants.primary]: getInactivePrimaryItemButtonBackgroundColor,
  [variants.white]: getInactiveWhiteItemButtonBackgroundColor
};

const notExpandedCss = css`
  width: fit-content;
`;

const ButtonGroupContainer = styled.div<ContainerStyledProps>`
  ${props => !props.$isExpanded && notExpandedCss}

  background-color: ${props => getBackgroundColorByVariant[props.$variant]};
  border-radius: ${borderRadius}px;
  display: flex;
  height: ${height}px;
  overflow: hidden;
`;

export {
  ButtonGroupContainer,
  borderRadius as buttonGroupContainerBorderRadius,
  height as buttonGroupContainerHeight
};
