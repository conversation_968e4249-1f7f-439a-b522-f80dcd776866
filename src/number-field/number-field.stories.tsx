import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import NumberField from "./number-field";

type NumberFieldProps = ComponentProps<typeof NumberField>;

type Args = Pick<
  NumberFieldProps,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "label"
  | "prefix"
  | "suffix"
  | "value"
  | "withoutThousandsSeparator"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return <NumberField {...args} onChange={value => updateArgs({value})} />;
};

const meta: Meta<Args> = {
  title: "number-field",
  argTypes: {
    hasError: {
      control: "boolean"
    },
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    value: {
      control: "text"
    },
    withoutThousandsSeparator: {
      control: "boolean"
    },
    prefix: {
      control: "text"
    },
    suffix: {
      control: "text"
    }
  },
  args: {
    value: null
  }
};

export {meta as default, Default};
