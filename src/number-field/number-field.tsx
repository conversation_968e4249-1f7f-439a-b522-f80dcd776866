import type {ComponentProps} from "react";
import NumberFormat from "react-number-format";
import type {NumberFormatPropsBase} from "react-number-format";

import TextField from "@/text-field";

type TextFieldProps = ComponentProps<typeof TextField>;

type ExposedTextFieldProps = Omit<
  TextFieldProps,
  "defaultValue" | "onChange" | "ref" | "value"
>;

type NumberFormatProps = NumberFormatPropsBase<ExposedTextFieldProps>;

type ExposedNumberFormatProps = Omit<
  NumberFormatProps,
  | "allowLeadingZeros"
  | "customInput"
  | "defaultValue"
  | "isNumericString"
  | "onChange"
  | "onValueChange"
  | "thousandSeparator"
  | "type"
  | "value"
>;

// prettier-ignore
type Props =
  & ExposedNumberFormatProps
  & ExposedTextFieldProps
  & {
      onChange: (value: number | null) => unknown;
      value: number | null;
      withoutThousandsSeparator?: boolean;
    };

const NumberField = ({
  onChange,
  value,
  withoutThousandsSeparator,
  ...rest
}: Props) => (
  <NumberFormat
    {...rest}
    customInput={TextField}
    onValueChange={({floatValue}) => onChange(floatValue ?? null)}
    thousandSeparator={!withoutThousandsSeparator}
    type="text"
    value={value ?? ""}
  />
);

export default Object.assign(NumberField, {
  numberInputHeight: TextField.textInputHeight
});
