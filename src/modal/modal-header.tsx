import type {ReactNode} from "react";
import styled from "styled-components";

import CloseModalButton from "./close-modal-button";

type Props = {
  children?: ReactNode;
  className?: string;
  isModalNotClosable?: boolean;
  onModalClose?: () => unknown;
};

const CloseModalButtonContainer = styled.div`
  margin-left: auto;
  padding: 20px;
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
`;

const ModalHeader = ({
  children,
  className,
  isModalNotClosable,
  onModalClose
}: Props) => (
  <Container {...{className}}>
    {children}
    {!isModalNotClosable && (
      <CloseModalButtonContainer>
        <CloseModalButton onClick={onModalClose} />
      </CloseModalButtonContainer>
    )}
  </Container>
);

export default ModalHeader;
