import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import styled from "styled-components";

import Button from "@/button";

import Modal from "./modal";
import type {OnClickOutsideModal} from "./modal";
import ModalHeader from "./modal-header";

type Args = {
  hasLeftElement?: boolean;
  hasRightElement?: boolean;
  isNotClosable?: boolean;
  isOpened?: boolean;
  shouldHide?: boolean;
};

const storyBookNoElement = <>{false}</>;

const StyledButton = styled(Button)`
  margin: 0;
`;

const Container = styled.div`
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const renderSideElement = (onClickOutside: OnClickOutsideModal) => (
  <Container onClick={onClickOutside}>
    <StyledButton size="medium" variant="primary">
      Button
    </StyledButton>
  </Container>
);

const renderModal = (args: Args, onModalClose: () => unknown) => (
  <Modal
    isNotClosable={args.isNotClosable}
    onClose={onModalClose}
    renderLeft={args.hasLeftElement ? renderSideElement : undefined}
    renderRight={args.hasRightElement ? renderSideElement : undefined}
    shouldHide={args.shouldHide}
  >
    <ModalHeader {...{onModalClose}} />
  </Modal>
);

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  const onModalClose = () => updateArgs({isOpened: !args.isOpened});

  return args.isOpened ? renderModal(args, onModalClose) : storyBookNoElement;
};

const meta: Meta<Args> = {
  title: "modal",
  argTypes: {
    hasLeftElement: {
      control: "boolean"
    },
    hasRightElement: {
      control: "boolean"
    },
    isNotClosable: {
      control: "boolean"
    },
    isOpened: {
      control: "boolean"
    },
    shouldHide: {
      control: "boolean"
    }
  },
  args: {
    isOpened: true
  }
};

export {meta as default, Default};
