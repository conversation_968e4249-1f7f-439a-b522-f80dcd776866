import {useLockBodyScroll} from "@unlockre/utils-react/dist";
import colorAlpha from "color-alpha";
import type {MouseEvent, ReactElement, ReactNode} from "react";
import styled from "styled-components";

import Portal from "@/portal";
import {getColor} from "@/theme-provider/theme";

type OnClickOutsideModal = (event: MouseEvent) => void;

type SideRenderer = (onClickOutsideModal: OnClickOutsideModal) => ReactElement;

type Props = {
  children: ReactNode;
  className?: string;
  dataTestId?: string;
  isNotClosable?: boolean;
  onClose?: () => unknown;
  renderLeft?: SideRenderer;
  renderRight?: SideRenderer;
  shouldHide?: boolean;
};

type BackgroundStyledProps = {
  $shouldHide?: boolean;
};

const containerBorderRadius = 10;

const Container = styled.div`
  background-color: ${getColor("gray", "000")};
  border-radius: ${containerBorderRadius}px;
  max-height: 80%;
  max-width: 1280px;
  width: 80%;
`;

const ElementContainer = styled.div`
  flex: 1;
  height: 100%;
`;

const Background = styled.div<BackgroundStyledProps>`
  align-items: center;
  background-color: ${props =>
    colorAlpha(getColor("gray", "1000")(props), 0.55)};
  bottom: 0;
  display: ${props => (props.$shouldHide ? "none" : "flex")};
  justify-content: center;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1;
`;

const render = (
  {dataTestId, renderLeft, renderRight, shouldHide, ...rest}: Props,
  onClickOutsideModal: OnClickOutsideModal
) => (
  <Portal>
    <Background $shouldHide={shouldHide} onClick={onClickOutsideModal}>
      <ElementContainer onClick={onClickOutsideModal}>
        {renderLeft?.(onClickOutsideModal)}
      </ElementContainer>
      <Container {...rest} data-testid={dataTestId} />
      <ElementContainer onClick={onClickOutsideModal}>
        {renderRight?.(onClickOutsideModal)}
      </ElementContainer>
    </Background>
  </Portal>
);

/**
 * @deprecated Use AlertDialog, ModalDialog, or a custom Dialog instead
 */
const Modal = (props: Props) => {
  useLockBodyScroll();

  const onClickOutsideModal: OnClickOutsideModal = event => {
    if (event.target === event.currentTarget && !props.isNotClosable) {
      props.onClose?.();
    }
  };

  return render(props, onClickOutsideModal);
};

export default Object.assign(Modal, {containerBorderRadius});

export type {OnClickOutsideModal};
