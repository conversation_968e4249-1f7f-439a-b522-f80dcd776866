import {Close as CloseIcon} from "@mui/icons-material";
import styled from "styled-components";

import {UnstyledButton} from "@/unstyled";

type Props = {
  className?: string;
  onClick?: () => unknown;
};

const StyledCloseIcon = styled(CloseIcon)`
  /* to get rid of the extra space below */
  display: block;
  height: 24px;
  width: 24px;
`;

const Container = styled(UnstyledButton)`
  padding: 8px;
`;

const CloseModalButton = (props: Props) => (
  <Container {...props}>
    <StyledCloseIcon />
  </Container>
);

export default CloseModalButton;
