import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import {useRef} from "react";
import styled from "styled-components";

import Button from "@/button";
import {MultifamilyIcon} from "@/icons";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import Tab from "./tab";
import TabList from "./tab-list";
import TabPanel from "./tab-panel";
import {tabVariants} from "./tab-variant";
import type {TabVariant} from "./tab-variant";
import {TabsProvider, useTabsContext} from "./tabs-context";
import useTabSelection from "./use-tab-selection";

type Args = {
  selectedTabName: string;
  title: string;
  variant: TabVariant;
};

const tabNames = {
  one: "one",
  two: "two"
} as const;

type TabName = (typeof tabNames)[keyof typeof tabNames];

const SecondTabPanelContent = () => {
  const randomNumberRef = useRef(Math.random());

  return (
    <span>Second Panel with random number: {randomNumberRef.current}</span>
  );
};

const StyledTabPanel = styled(TabPanel)`
  ${getTypography("body", "m")};

  align-items: center;
  background-color: ${getColorByAlias("backgroundPrimary")};
  color: ${getColorByAlias("textPrimary")};
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 300px;
`;

const StyledButton = styled(Button)`
  margin-top: 20px;
`;

const Content = () => {
  const {tabSelection} = useTabsContext<TabName>();

  return (
    <>
      <TabList>
        <Tab
          label="Tab one"
          name={tabNames.one}
          renderIcon={({isTabSelected}) => (
            <MultifamilyIcon
              getColor={
                isTabSelected
                  ? getColor("blue", "500")
                  : getColor("gray", "400")
              }
              size={24}
            />
          )}
        />
        <Tab label="Tab two" name={tabNames.two} />
      </TabList>
      <StyledTabPanel tabName={tabNames.one}>
        First panel
        <StyledButton
          onClick={() => tabSelection.setSelectedTabName(tabNames.two)}
          size="medium"
          variant="primary"
        >
          Go to Second Panel
        </StyledButton>
      </StyledTabPanel>
      <StyledTabPanel keepMounted tabName={tabNames.two}>
        <SecondTabPanelContent />
      </StyledTabPanel>
    </>
  );
};

const initialTabName = tabNames.one;

const Default: StoryFn<Args> = () => {
  const [{variant}, setArgs] = useArgs<Args>();

  const onTabSelect = (selectedTabName: TabName) => setArgs({selectedTabName});

  const tabSelection = useTabSelection({initialTabName, onTabSelect});

  return (
    <TabsProvider {...{tabSelection, variant}}>
      <Content />
    </TabsProvider>
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=1249%3A2697"
  }
};

const meta: Meta<Args> = {
  title: "tabs",
  argTypes: {
    selectedTabName: {
      control: "select",
      options: Object.values(tabNames)
    },
    variant: {
      control: "select",
      options: Object.values(tabVariants)
    }
  },
  args: {
    selectedTabName: initialTabName,
    variant: tabVariants.blue
  }
};

export {meta as default, Default};
