import type {ReactElement} from "react";
import styled from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import TabList from "./tab-list";
import {type TabVariant, tabVariants} from "./tab-variant";
import {useTabsContext} from "./tabs-context";

type RenderIconParams = {
  isTabSelected: boolean;
};

type RenderIcon = (params: RenderIconParams) => ReactElement;

type Props = {
  label: string;
  name: string;
  renderIcon?: RenderIcon;
};

type TabLabelStyledProps = {
  $isTabSelected?: boolean;
  $variant: TabVariant;
};

type TabSelectedIndicatorProps = {
  $variant: TabVariant;
};

const variantStyles = {
  [tabVariants.blue]: {
    getSelectedTabLabelColor: getColor("blue", "500"),
    getDefaultTabLabelColor: getColor("gray", "400")
  },
  [tabVariants.accent]: {
    getSelectedTabLabelColor: getColorByAlias("bgAccent"),
    getDefaultTabLabelColor: getColorByAlias("textDisabled")
  }
};

const TabSelectedIndicator = styled.div<TabSelectedIndicatorProps>`
  background: ${props =>
    variantStyles[props.$variant].getSelectedTabLabelColor};
  border-radius: 5px 5px 0px 0px;
  bottom: -${TabList.borderBottom}px;
  height: 3px;
  position: absolute;
  width: 100%;
`;

const TabButton = styled(UnstyledButton)`
  align-items: center;
  display: flex;
  gap: 8px;
  height: 38px;
  padding: 0 24px;
`;

const TabLabel = styled.span<TabLabelStyledProps>`
  ${getTypography("body", "s", 600)};

  color: ${props =>
    props.$isTabSelected
      ? variantStyles[props.$variant].getSelectedTabLabelColor
      : variantStyles[props.$variant].getDefaultTabLabelColor};
  white-space: nowrap;
`;

const Container = styled.li`
  position: relative;
`;

const Tab = ({label, name, renderIcon}: Props) => {
  const {tabSelection, variant} = useTabsContext();
  const {selectedTabName, setSelectedTabName} = tabSelection;

  const isTabSelected = name === selectedTabName;

  return (
    <Container>
      <TabButton onClick={() => setSelectedTabName(name)}>
        <TabLabel $isTabSelected={isTabSelected} $variant={variant}>
          {label}
        </TabLabel>
        {renderIcon && renderIcon({isTabSelected})}
      </TabButton>
      {isTabSelected && <TabSelectedIndicator $variant={variant} />}
    </Container>
  );
};

export default Tab;
