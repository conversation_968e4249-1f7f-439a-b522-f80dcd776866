import type {ReactNode} from "react";
import styled, {css} from "styled-components";

import {useTabsContext} from "./tabs-context";

type Props = {
  children: ReactNode;
  className?: string;
  keepMounted?: boolean;
  tabName: string;
};

type ContainerStyledProps = {
  $isTabSelected: boolean;
};

const displayNoneCss = css`
  display: none !important;
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => !props.$isTabSelected && displayNoneCss};
`;

const TabPanel = ({children, className, keepMounted, tabName}: Props) => {
  const {tabSelection} = useTabsContext();

  const {selectedTabName} = tabSelection;

  const isTabSelected = tabName === selectedTabName;

  if (!isTabSelected && !keepMounted) {
    return null;
  }

  return (
    <Container $isTabSelected={isTabSelected} className={className}>
      {children}
    </Container>
  );
};

export default TabPanel;
