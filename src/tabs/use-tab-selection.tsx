import {useState} from "react";

type SetSelectedTabName<TTabName extends string> = (tabName: TTabName) => void;

type Props<TTabName extends string> = {
  initialTabName: TTabName;
  onTabSelect?: (tabName: TTabName) => unknown;
};

const useTabSelection = <TTabName extends string>({
  initialTabName,
  onTabSelect
}: Props<TTabName>) => {
  const [selectedTabName, setSelectedTabNameState] = useState(initialTabName);

  const setSelectedTabName: SetSelectedTabName<TTabName> = tabName => {
    setSelectedTabNameState(tabName);

    onTabSelect?.(tabName);
  };

  return {
    selectedTabName: selectedTabName as TTabName,
    setSelectedTabName: setSelectedTabName as SetSelectedTabName<string>
  };
};

export default useTabSelection;

export type {SetSelectedTabName};
