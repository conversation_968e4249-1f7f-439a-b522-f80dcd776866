# Tabs

## About

These components provide all the building blocks needed to render a set of tabs.

There are 3 main parts:
- a `TabSelectionProvider` component to wrap everything
- a `TabList` with `Tab` components inside to render the tab buttons with their labels
- a `TabPanel` to contain each tab content

In addition to these components there is a `useTabSelection` hook that can be used inside a component rendered within a `TabSelectionProvider` to get the `selectedTabName` and change it using the `setSelectedTabName` function.

## Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {Tab, TabList, TabPanel, TabsProvider, useTabSelection} from "@unlockre/components-library/dist/tabs";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {useRef} from "react";

const tabNames = {
  one: "one",
  two: "two"
} as const;

const SecondTabPanelContent = () => {
  const randomNumberRef = useRef(Math.random());

  return (
    <span>Second Panel with random number: {randomNumberRef.current}</span>
  );
};

const StyledTabPanel = styled(TabPanel)`
  ${getTypography("body", "m")};

  align-items: center;
  background-color: ${getColorByAlias("backgroundPrimary")};
  color: ${getColorByAlias("textPrimary")};
  display: flex;
  justify-content: center;
  height: 300px;
`;

const Example = () => {
  // optional tab select handler
  const onTabSelect = (selectedTabName: TabName) => console.log(selectedTabName);

  const tabSelection = useTabSelection({initialTabName, onTabSelect});

  return (
    <ThemeProvider>
      <TabsProvider {...{tabSelection}} variant="blue">
        <TabList>
          <Tab label="Tab one" name={tabNames.one} />
          <Tab label="Tab two" name={tabNames.two} />
        </TabList>
        <StyledTabPanel tabName={tabNames.one}>
          First panel
          <StyledButton
            onClick={() => tabSelection.setSelectedTabName(tabNames.two)}
            size="medium"
            variant="primary"
          >
            Go to Second Panel
          </StyledButton>
        </StyledTabPanel>
        {/* keepMounted is used to keep the TabPanel mounted when it is not selected */}
        <StyledTabPanel keepMounted tabName={tabNames.two}>
          <SecondTabPanelContent />
        </StyledTabPanel>
      </TabsProvider>
    </ThemeProvider>
  )
}

export default Example;
```
