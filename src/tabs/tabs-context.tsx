import {createContext, useContext} from "react";
import type {Context, ReactNode} from "react";

import type {TabVariant} from "./tab-variant";
import type {TabSelection} from "./types";

type TabsProviderProps<TTabName extends string> = {
  children: ReactNode;
  tabSelection: TabSelection<TTabName>;
  variant: TabVariant;
};

type TabsContextType<TTabName extends string> = Context<
  | {
      tabSelection: TabSelection<TTabName>;
      variant: TabVariant;
    }
  | undefined
>;

// eslint-disable-next-line @typescript-eslint/naming-convention
const tabsContext = createContext<TabsContextType<string> | undefined>(
  undefined
);

const getTabsContext = <TTabName extends string>() =>
  tabsContext as TabsContextType<TTabName>;

const TabsProvider = <TTabName extends string>({
  children,
  tabSelection,
  variant
}: TabsProviderProps<TTabName>) => {
  const tabsContext = getTabsContext<TTabName>();

  return (
    <tabsContext.Provider
      value={{
        tabSelection,
        variant
      }}
    >
      {children}
    </tabsContext.Provider>
  );
};

const useTabsContext = <TTabName extends string>() => {
  const tabsContext = useContext(getTabsContext<TTabName>());

  if (!tabsContext) {
    throw new Error("TabsContext is not initialized");
  }

  return tabsContext;
};

export {TabsProvider, useTabsContext};
