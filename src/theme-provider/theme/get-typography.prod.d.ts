// This file is copied as it is to the package
// see: https://github.com/microsoft/TypeScript/issues/39683

import type {
  PropsWithTheme,
  ThemeFontWeight,
  ThemeTypographyNameOf,
  ThemeTypographySetName
} from "./types";

declare const getTypography: <
  TThemeTypographySetName extends ThemeTypographySetName,
  TThemeTypographyName extends ThemeTypographyNameOf<TThemeTypographySetName>,
  TThemeFontWeight extends ThemeFontWeight
>(
  typographySetName: TThemeTypographySetName,
  typographyName: TThemeTypographyName,
  fontWeight?: TThemeFontWeight
) => (
  props: PropsWithTheme
) => (typeof props)["theme"]["typographies"][TThemeTypographySetName][TThemeTypographyName];

export {getTypography};
