import {latestColorPalettes} from "./latest-color-palettes";
import {legacyColors} from "./legacy-colors";
import {themeBreakpoints} from "./theme-breakpoints";
import {themeTypographies} from "./theme-typographies";
import type {AnyLatestThemeColors, ThemeWith} from "./types";

const create = <TThemeColors extends AnyLatestThemeColors>(
  themeColors: TThemeColors
): ThemeWith<TThemeColors> => ({
  breakpoints: themeBreakpoints,
  colors: {
    aliases: {
      ...legacyColors.aliases,
      ...themeColors.aliases
    },
    palette: {
      ...legacyColors.palette,
      ...latestColorPalettes,
      ...themeColors.palette
    }
  },
  typographies: themeTypographies
});

export {create};
