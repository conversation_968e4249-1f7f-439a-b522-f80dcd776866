import {create} from "./create";
import type {AnyTheme, AnyThemeColors, ExtendTheme} from "./types";

const extend = <
  TTheme extends AnyTheme,
  TOtherThemeColors extends AnyThemeColors
>(
  theme: TTheme,
  otherThemeColors: TOtherThemeColors
): ExtendTheme<TTheme, TOtherThemeColors> =>
  create({
    aliases: {...theme.colors.aliases, ...otherThemeColors.aliases},
    palette: {...theme.colors.palette, ...otherThemeColors.palette}
  });

export {extend};
