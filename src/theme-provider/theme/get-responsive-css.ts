import {css as cssFn} from "styled-components";
import type {FlattenInterpolation, ThemedStyledProps} from "styled-components";

import {getMediaQueryString} from "./get-media-query-string";
import type {ThemeBreakpointName} from "./theme-breakpoint-name";
import type {PropsWithTheme, Theme} from "./types";

const getResponsiveCss = <TProps extends PropsWithTheme>(
  breakpoint: ThemeBreakpointName,
  css: FlattenInterpolation<ThemedStyledProps<TProps, Theme>>
) => cssFn`
  @media screen and ${props => getMediaQueryString(props.theme, breakpoint)} {
    ${css};
  }
`;

export {getResponsiveCss};
