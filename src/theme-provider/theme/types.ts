import type {DefaultTheme, ThemeProps} from "styled-components";

import type latestColorDefinitions from "./latest-color-definitions.json";
import type {latestColorPalettes} from "./latest-color-palettes";
import type {legacyColors} from "./legacy-colors";
import type {ThemeBreakpointName} from "./theme-breakpoint-name";
import type {themeBreakpoints} from "./theme-breakpoints";
import type {themeTypographies} from "./theme-typographies";

type ThemeFontWeight = 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900;

type ThemeTypographyStyle = {
  fontFamily: string;
  fontSize: string;
  fontVariantNumeric?: string;
  fontWeight?: number;
  lineHeight: string;
};

type AnyThemeTypographySet = Record<string, ThemeTypographyStyle>;

type AnyThemeTypographies = Record<string, AnyThemeTypographySet>;

type AnyThemeColorPalette = Record<string, string>;

type AnyThemeColorPalettes = Record<string, AnyThemeColorPalette>;

type AnyThemeColorAliases = Record<string, string>;

type ThemeColorsWith<
  TThemeAliases extends AnyThemeColorAliases,
  TThemePalettes extends AnyThemeColorPalettes
> = {
  aliases: TThemeAliases;
  palette: TThemePalettes;
};

type AnyThemeColors = ThemeColorsWith<
  AnyThemeColorAliases,
  AnyThemeColorPalettes
>;

type ExtendThemeColors<
  TThemeColors1 extends AnyThemeColors,
  TThemeColors2 extends AnyThemeColors
> = ThemeColorsWith<
  TThemeColors1["aliases"] & TThemeColors2["aliases"],
  TThemeColors1["palette"] & TThemeColors2["palette"]
>;

type ThemeBreakpoints = Record<ThemeBreakpointName, number>;

type LatestColorAliasName = keyof typeof latestColorDefinitions.colorNames;

type LatestColorAliases = Record<LatestColorAliasName, string>;

type AnyLatestThemeColors = ThemeColorsWith<
  LatestColorAliases,
  AnyThemeColorPalettes
>;

// prettier-ignore
type ThemeDefaultColorPalettes =
  & typeof latestColorPalettes
  & typeof legacyColors.palette;

type ThemeColorPalettesWith<
  TOtherThemeColorPalettes extends AnyThemeColorPalettes
> = [TOtherThemeColorPalettes] extends [never]
  ? ThemeDefaultColorPalettes
  : ThemeDefaultColorPalettes & TOtherThemeColorPalettes;

type ThemeColorAliasesWith<
  TOtherThemeColorAliases extends AnyThemeColorAliases
> = TOtherThemeColorAliases & typeof legacyColors.aliases;

type ThemeWith<TColors extends AnyLatestThemeColors> = {
  breakpoints: typeof themeBreakpoints;
  colors: ThemeColorsWith<
    ThemeColorAliasesWith<TColors["aliases"]>,
    ThemeColorPalettesWith<TColors["palette"]>
  >;
  typographies: typeof themeTypographies;
};

type AnyTheme = ThemeWith<AnyLatestThemeColors>;

type ExtendTheme<
  TTheme extends AnyTheme,
  TOtherThemeColors extends AnyThemeColors
> = ThemeWith<ExtendThemeColors<TTheme["colors"], TOtherThemeColors>>;

type Theme = DefaultTheme;

type PropsWithTheme = ThemeProps<Theme>;

type GetColor = (props: PropsWithTheme) => string;

type ThemeTypographySetName = keyof Theme["typographies"];

type ThemeTypographyNameOf<
  TThemeTypographySetName extends ThemeTypographySetName
> = keyof Theme["typographies"][TThemeTypographySetName];

type ThemePaletteName = keyof Theme["colors"]["palette"];

type ThemeColorNameOf<TThemePaletteName extends ThemePaletteName> =
  keyof Theme["colors"]["palette"][TThemePaletteName];

type ThemeColorAliasName = keyof Theme["colors"]["aliases"];

type ProdTheme = any;

type DevTheme = ThemeWith<ThemeColorsWith<LatestColorAliases, never>>;

export type {
  AnyLatestThemeColors,
  AnyTheme,
  AnyThemeColorAliases,
  AnyThemeColorPalettes,
  AnyThemeColors,
  AnyThemeTypographies,
  DevTheme,
  ExtendTheme,
  LatestColorAliases,
  GetColor,
  ProdTheme,
  PropsWithTheme,
  Theme,
  ThemeBreakpoints,
  ThemeColorAliasName,
  ThemeColorNameOf,
  ThemeColorsWith,
  ThemeFontWeight,
  ThemePaletteName,
  ThemeTypographySetName,
  ThemeTypographyNameOf,
  ThemeTypographyStyle,
  ThemeWith
};
