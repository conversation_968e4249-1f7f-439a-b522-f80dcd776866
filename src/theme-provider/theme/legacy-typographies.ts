import {getTypographyStyle} from "./get-typography-style";
import type {AnyThemeTypographies} from "./types";

const legacyTypographies = {
  body: {
    xxxs: getTypographyStyle(10, 12),
    xxs: getTypographyStyle(11, 13),
    xs: getTypographyStyle(12, 15),
    s: getTypographyStyle(14, 18),
    m: getTypographyStyle(16, 20),
    l: getTypographyStyle(18, 22)
  },
  display: {
    s: getTypographyStyle(42, 40),
    m: getTypographyStyle(48, 62),
    l: getTypographyStyle(56, 68)
  },
  title: {
    xs: getTypographyStyle(18, 22),
    s: getTypographyStyle(20, 25),
    m: getTypographyStyle(24, 30),
    l: getTypographyStyle(28, 35),
    xl: getTypographyStyle(32, 40)
  }
} as const satisfies AnyThemeTypographies;

export {legacyTypographies};
