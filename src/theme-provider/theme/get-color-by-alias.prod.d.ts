// This file is copied as it is to the package
// see: https://github.com/microsoft/TypeScript/issues/39683

import type {PropsWithTheme, ThemeColorAliasName} from "./types";

declare const getColorByAlias: <
  TThemeColorAliasName extends ThemeColorAliasName
>(
  colorAliasName: TThemeColorAliasName
) => <TProps extends PropsWithTheme>(
  props: TProps
) => TProps["theme"]["colors"]["aliases"][TThemeColorAliasName];

export {getColorByAlias};
