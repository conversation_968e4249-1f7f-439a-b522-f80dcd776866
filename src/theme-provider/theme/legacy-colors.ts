import type {AnyThemeColorAliases, AnyThemeColorPalettes} from "./types";

/* eslint-disable @typescript-eslint/naming-convention */
const palette = {
  blue: {
    "040": "rgb(246, 249, 255)",
    "100": "rgb(231, 241, 255)",
    "150": "rgb(220, 234, 255)",
    "200": "rgb(208, 227, 255)",
    "300": "rgb(184, 212, 255)",
    "400": "rgb(160, 198, 255)",
    "450": "rgb(108, 167, 255)",
    "500": "rgb(18, 113, 255)",
    "700": "rgb(13, 67, 165)",
    "750": "rgb(11, 58, 143)",
    "800": "rgb(0, 44, 124)",
    "900": "rgb(7, 24, 84)",
    "950": "rgb(5, 7, 53)"
  },
  gray: {
    "000": "rgb(255, 255, 255)",
    "020": "rgb(250, 250, 250)",
    "040": "rgb(245, 245, 245)",
    "070": "rgb(237, 237, 237)",
    "100": "rgb(229, 229, 229)",
    "160": "rgb(214, 214, 214)",
    "250": "rgb(191, 191, 191)",
    "290": "rgb(182, 182, 182)",
    "400": "rgb(153, 153, 153)",
    "500": "rgb(127, 127, 127)",
    "550": "rgb(115, 115, 115)",
    "600": "rgb(102, 102, 102)",
    "650": "rgb(90, 90, 90)",
    "730": "rgb(68, 68, 68)",
    "800": "rgb(51, 51, 51)",
    "900": "rgb(26, 26, 26)",
    "1000": "rgb(0, 0, 0)"
  },
  green: {
    "50": "rgb(241, 252, 253)",
    "100": "rgb(229, 246, 237)",
    "150": "rgb(217, 242, 229)",
    "200": "rgb(204, 237, 220)",
    "300": "rgb(169, 221, 190)",
    "400": "rgba(0, 166, 80, 0.4)",
    "500": "rgb(0, 166, 80)",
    "600": "rgb(0, 135, 68)",
    "700": "rgb(0, 102, 51)",
    "800": "rgb(0, 94, 47)"
  },
  green1: {
    // TODO: Rename to 500
    "550": "rgb(89, 185, 157)"
  },
  lightBlue: {
    // TODO: Rename to 500
    "550": "rgb(91, 188, 242)"
  },
  lila: {
    "50": "rgb(245, 240, 255)",
    "500": "rgb(197, 170, 255)",
    "600": "rgb(167, 138, 229)"
  },
  orange: {
    "100": "rgb(255, 241, 235)",
    "150": "rgba(255, 119, 51, 0.15)",
    "200": "rgb(255, 228, 214)",
    "300": "rgb(255, 200, 172)",
    "400": "rgba(255, 119, 51, 0.4)",
    "500": "rgb(255, 119, 51)",
    "600": "rgb(230, 84, 11)",
    "700": "rgb(204, 62, 10)",
    "800": "rgb(166, 42, 8)"
  },
  orange1: {
    "50": "rgb(255, 246, 244)",
    // TODO: Rename to 500
    "550": "rgb(255, 117, 87)"
  },
  pink: {
    // TODO: Rename to 500
    "550": "rgb(255, 139, 216)"
  },
  purple: {
    "550": "rgb(197, 170, 255)"
  },
  red: {
    "50": "rgb(253, 241, 245)",
    "100": "rgb(254, 236, 237)",
    "150": "rgba(242, 61, 79, 0.15)",
    "200": "rgb(252, 216, 220)",
    "300": "rgb(255, 193, 200)",
    "400": "rgba(242, 61, 79, 0.4)",
    "500": "rgb(242, 61, 79)",
    "600": "rgb(209, 36, 64)",
    "700": "rgb(166, 29, 51)",
    "800": "rgb(128, 22, 39)"
  },
  temp: {
    blue01: `rgb(0, 0, 255)`,
    blue02: `rgb(11, 200, 241)`,
    blue03: `rgb(17, 107, 158)`,
    blue04: `rgb(24, 144, 255)`,
    blue05: `rgb(25, 37, 104)`,
    blue06: `rgb(31, 51, 131)`,
    blue07: `rgb(32, 47, 101)`,
    blue08: `rgb(32, 151, 246)`,
    blue09: `rgb(49, 57, 134)`,
    blue10: `rgb(62, 152, 199)`,
    blue11: `rgb(66, 141, 255)`,
    blue12: `rgb(91, 147, 255)`,
    blue13: `rgb(108, 167, 255)`,
    blue14: `rgb(134, 224, 215)`,
    blue15: `rgb(173, 240, 233)`,
    blue16: `rgb(183, 207, 254)`,
    blue17: `rgb(195, 226, 252)`,
    blue18: `rgb(198, 210, 255)`,
    blue19: `rgb(214, 228, 251)`,
    blue20: `rgb(223, 233, 255)`,
    blue21: `rgb(239, 252, 255)`,
    green01: `rgb(0, 221, 62)`,
    green02: `rgb(2, 211, 71)`,
    green03: `rgb(4, 189, 92)`,
    green04: `rgb(6, 169, 110)`,
    green05: `rgb(7, 90, 80)`,
    green06: `rgb(7, 147, 130)`,
    green07: `rgb(8, 111, 99)`,
    green08: `rgb(8, 129, 115)`,
    green09: `rgb(30, 224, 57)`,
    green10: `rgb(74, 161, 129)`,
    green11: `rgb(94, 230, 46)`,
    green12: `rgb(99, 221, 208)`,
    green13: `rgb(164, 237, 34)`,
    green14: `rgb(203, 241, 225)`,
    green15: `rgb(213, 242, 25)`,
    green16: `rgb(215, 244, 241)`,
    green17: `rgb(230, 242, 239)`,
    orange01: `rgb(238, 164, 53)`,
    orange02: `rgb(244, 230, 216)`,
    orange03: `rgb(245, 74, 37)`,
    orange04: `rgb(247, 89, 36)`,
    orange05: `rgb(247, 240, 221)`,
    orange06: `rgb(249, 128, 31)`,
    orange07: `rgb(251, 164, 27)`,
    orange08: `rgb(251, 206, 49)`,
    orange09: `rgb(252, 195, 24)`,
    orange10: `rgb(254, 207, 40)`,
    pink01: `rgb(253, 230, 233)`,
    red01: `rgb(142, 26, 0)`,
    red02: `rgb(153, 31, 4)`,
    red03: `rgb(185, 46, 16)`,
    red04: `rgb(204, 55, 23)`,
    red05: `rgb(229, 67, 31)`,
    red06: `rgb(255, 0, 0)`,
    violet01: `rgb(32, 40, 79)`,
    violet02: `rgb(101, 74, 141)`,
    violet03: `rgb(122, 139, 228)`,
    violet04: `rgb(144, 93, 139)`,
    violet05: `rgb(186, 96, 177)`,
    violet06: `rgb(202, 192, 215)`,
    violet07: `rgb(216, 133, 255)`,
    violet08: `rgb(238, 238, 255)`,
    yellow01: `rgb(255, 235, 19)`,
    yellow02: `rgb(255, 245, 17)`,
    yellow03: `rgb(243, 238, 190)`,
    yellow04: `rgb(161, 156, 13)`
  },
  yellow: {
    // TODO: Rename to 500
    "550": "rgb(248, 177, 72)"
  }
} as const satisfies AnyThemeColorPalettes;
/* eslint-enable */

const aliases = {
  accentPrimary: palette.blue["500"],
  accentSecondary: palette.blue["700"],
  accentTertiary: palette.blue["800"],
  backgroundBlue05: palette.temp.blue05,
  backgroundBlue06: palette.temp.blue06,
  backgroundBlue09: palette.temp.blue09,
  backgroundBlue13: palette.temp.blue13,
  backgroundBlue14: palette.temp.blue14,
  backgroundBlue15: palette.temp.blue15,
  backgroundBlue17: palette.temp.blue17,
  backgroundBlue18: palette.temp.blue18,
  backgroundBlue19: palette.temp.blue19,
  backgroundBlue20: palette.temp.blue20,
  backgroundBlue21: palette.temp.blue21,
  backgroundGreen01: palette.temp.green01,
  backgroundGreen02: palette.temp.green02,
  backgroundGreen03: palette.temp.green03,
  backgroundGreen04: palette.temp.green04,
  backgroundGreen05: palette.temp.green05,
  backgroundGreen06: palette.temp.green06,
  backgroundGreen07: palette.temp.green07,
  backgroundGreen08: palette.temp.green08,
  backgroundGreen09: palette.temp.green09,
  backgroundGreen10: palette.temp.green10,
  backgroundGreen11: palette.temp.green11,
  backgroundGreen12: palette.temp.green12,
  backgroundGreen13: palette.temp.green13,
  backgroundGreen14: palette.temp.green14,
  backgroundGreen15: palette.temp.green15,
  backgroundGreen16: palette.temp.green16,
  backgroundGreen17: palette.temp.green17,
  backgroundOrange01: palette.temp.orange01,
  backgroundOrange02: palette.temp.orange02,
  backgroundOrange03: palette.temp.orange03,
  backgroundOrange04: palette.temp.orange04,
  backgroundOrange05: palette.temp.orange05,
  backgroundOrange06: palette.temp.orange06,
  backgroundOrange07: palette.temp.orange07,
  backgroundOrange09: palette.temp.orange09,
  backgroundPink01: palette.temp.pink01,
  backgroundPrimary: palette.gray["070"],
  backgroundRed01: palette.temp.red01,
  backgroundRed02: palette.temp.red02,
  backgroundRed03: palette.temp.red03,
  backgroundRed04: palette.temp.red04,
  backgroundRed05: palette.temp.red05,
  backgroundSecondary: palette.gray["040"],
  backgroundTertiary: palette.gray["020"],
  backgroundViolet02: palette.temp.violet02,
  backgroundViolet04: palette.temp.violet04,
  backgroundViolet05: palette.temp.violet05,
  backgroundViolet06: palette.temp.violet06,
  backgroundViolet08: palette.temp.violet08,
  backgroundWhite: palette.gray["000"],
  backgroundYellow01: palette.temp.yellow01,
  backgroundYellow02: palette.temp.yellow02,
  feedbackError: palette.red["500"],
  feedbackSuccess: palette.green["500"],
  feedbackWarning: palette.orange["500"],
  foregroundBlue01: palette.temp.blue01,
  foregroundBlue02: palette.temp.blue02,
  foregroundBlue03: palette.temp.blue03,
  foregroundBlue08: palette.temp.blue08,
  foregroundBlue12: palette.temp.blue12,
  foregroundBlue17: palette.temp.blue17,
  foregroundRed06: palette.temp.red06,
  foregroundViolet03: palette.temp.violet03,
  foregroundViolet07: palette.temp.violet07,
  shadowBlue11: palette.temp.blue11,
  shadowBlue19: palette.temp.blue19,
  strokeBlue04: palette.temp.blue04,
  strokeBlue07: palette.temp.blue07,
  strokeBlue08: palette.temp.blue08,
  strokeBlue10: palette.temp.blue10,
  strokeBlue12: palette.temp.blue12,
  strokeBlue16: palette.temp.blue16,
  strokeOrange08: palette.temp.orange08,
  strokeOrange10: palette.temp.orange10,
  strokeViolet01: palette.temp.violet01,
  textDisabled: palette.gray["250"],
  textInverted: palette.gray["000"],
  textPrimary: palette.gray["900"],
  textSecondary: palette.gray["550"]
} as const satisfies AnyThemeColorAliases;

const legacyColors = {
  aliases,
  palette
} as const;

export {legacyColors};
