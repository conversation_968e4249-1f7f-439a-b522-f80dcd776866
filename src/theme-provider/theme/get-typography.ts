import type {
  PropsWithTheme,
  ThemeFontWeight,
  ThemeTypographyNameOf,
  ThemeTypographySetName
} from "./types";

const getWithoutFontWeight =
  <
    TThemeTypographySetName extends ThemeTypographySetName,
    TThemeTypographyName extends ThemeTypographyNameOf<TThemeTypographySetName>
  >(
    typographySetName: TThemeTypographySetName,
    typographyName: TThemeTypographyName
  ) =>
  (props: PropsWithTheme) =>
    props.theme.typographies[typographySetName][typographyName];

const getWithFontWeight =
  <
    TThemeTypographySetName extends ThemeTypographySetName,
    TThemeTypographyName extends ThemeTypographyNameOf<TThemeTypographySetName>,
    TThemeFontWeight extends ThemeFontWeight
  >(
    typographySetName: TThemeTypographySetName,
    typographyName: TThemeTypographyName,
    fontWeight: TThemeFontWeight
  ) =>
  (props: PropsWithTheme) => ({
    ...props.theme.typographies[typographySetName][typographyName],
    fontWeight
  });

const getTypography = <
  TThemeTypographySetName extends ThemeTypographySetName,
  TThemeTypographyName extends ThemeTypographyNameOf<TThemeTypographySetName>,
  TThemeFontWeight extends ThemeFontWeight
>(
  typographySetName: TThemeTypographySetName,
  typographyName: TThemeTypographyName,
  fontWeight?: TThemeFontWeight
) =>
  fontWeight === undefined
    ? getWithoutFontWeight(typographySetName, typographyName)
    : getWithFontWeight(typographySetName, typographyName, fontWeight);

export {getTypography};
