import type {ThemeTypographyStyle} from "./types";

const getTypographyStyle = <
  TFontSize extends number,
  TLineHeight extends number
>(
  fontSize: TFontSize,
  lineHeight: TLineHeight
) =>
  ({
    fontFamily: "Inter Variable",
    fontSize: (fontSize + "px") as `${TFontSize}px`,
    lineHeight: (lineHeight + "px") as `${TLineHeight}px`
  }) as const satisfies ThemeTypographyStyle;

export {getTypographyStyle};
