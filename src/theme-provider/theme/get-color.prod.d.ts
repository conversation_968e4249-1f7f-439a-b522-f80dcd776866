// This file is copied as it is to the package
// see: https://github.com/microsoft/TypeScript/issues/39683

import type {
  PropsWithTheme,
  Theme,
  ThemeColorNameOf,
  ThemePaletteName
} from "./types";

declare const getColor: <
  TThemePaletteName extends ThemePaletteName,
  TThemeColorName extends ThemeColorNameOf<TThemePaletteName>
>(
  paletteName: TThemePaletteName,
  colorName: TThemeColorName
) => (
  props: PropsWithTheme
) => Theme["colors"]["palette"][TThemePaletteName][TThemeColorName];

export {getColor};
