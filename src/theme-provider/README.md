# ThemeProvider

## About

This component provides the given theme to be used inside styled components.

Wrapping a component with the `ThemeProvider` enables all its descendants to use any of the `theme` utility functions inside a styled components declaration to declare a color using the [getColor](theme/get-color.ts) or the [getColorByAlias](theme/get-color-by-alias.ts) functions, or to specify a typography using the [getTypography](theme/get-typography.ts) utility.

## History

The theme has, breakpoints, colors and typographies.

### Colors

Theme colors are organized in color palettes and aliases.

Color palettes used to be different variations of the same color, but nowadays, color palettes are flattened, or in other words, they contain different colors with all their variants.

Color aliases are not divided into groups like the color palettes, they are just "links" or "aliases" to colors in the palettes.

Currently, the theme contains the following color palettes:

**Legacy color palettes** (the ones that came first, these shouldn't be used)

- `blue`
- `gray`
- `green`
- `green1`
- `lightBlue`
- `lila`
- `orange`
- `orange1`
- `pink`
- `purple`
- `red`
- `temp`
- `yellow`

> The temp color palette was created to add to the theme the colors we used directly using their values in the apps

**Latest color palette** (the latest and current color palette, you should used these to create new aliases, don't used them directly)

- `latest`

### Typographies

In the past, typographies were organized by font names and then by sizes.

Recently, we decided to flatten the typography organization, which allows us to have a single typography font set with all its typography styles. This means if we ever need to add a new font set, it would be as easy as adding a single entry to the typographies object.

The flattened organization is fully compatible with the previous structure, as it's just a matter of how we name things. In the past, the first level inside typographies was the font name, and the second level was the size. Now, what used to be the font name (first level) we call the font set name, and what used to be the font size (second level) we call the font name. We maintain two levels, with the font style remaining in the last one as usual.

Flattening simplified the organization as now all related typographies are grouped under a single font set. This gives us the possibility of adding new complete font sets if needed while maintaining full compatibility. In other words, we can add new font sets without having to remove or modify existing ones.

The main reason which pushed us to do this was to support the latest typographies available in [figma](https://www.figma.com/design/6icmCUZBmSYpVS84E9fnEm/Foundations?node-id=1346-3750&p=f) without breaking the existing code that uses the old organization. To do this, we added a new font set called `latest` which exposes the typographies present inside the [json file](theme/latest-typography-definitions.json) we get from the design team (more on this in the [update theme](#update-theme) section).

Currently, the theme contains the following typographies (font sets):

[Legacy typographies](theme/legacy-typographies.ts)

> the ones that came first, these shouldn't be used

- `body`: **xxxs**, **xxs**, **xs**, **s**, **m**, **l**
- `display`: **s**, **m**, **l**
- `title`: **xs**, **s**, **m**, **l**, **xl**

[Latest typography](theme/latest-typography.ts)

> the latest typographies (font set), you should use the fonts under this font set

- `latest`

## Usage

1. Be sure to use (in case of apps), or require it as peer dependency (in case of package) version `^32.0.0`, that's in which was added the support of new themes
2. Create an **application-theme.ts** module under **src/components/application** to define a theme by creating a new one (using the [create](theme/create.ts) utility), using (just importing it) or extending an existing one (using [extend](theme/extend.ts) utility) and set **styled-components** `DefaultTheme` to this one using a declaration merging (see the examples below)
3. Use the `ThemeProvider` inside your `Application` component (**src/components/application/application.tsx**) and pass the `applicationTheme` created in the previous step
4. You can use all the theme utilities provided here

## Available Themes

- [Light Theme](theme/light-theme.ts)
- [Magic Violet Theme](theme/magic-violet-theme.ts)

## Examples

### Using an existing theme (Light Theme)

**src/components/application/application-theme.ts**

```tsx
import "styled-components";

import {lightTheme} from "@unlockre/components-library/dist/theme-provider/theme/light-theme";
import type {LightTheme} from "@unlockre/components-library/dist/theme-provider/theme/light-theme";

type ApplicationTheme = LightTheme;

const applicationTheme = lightTheme;

declare module "styled-components" {
  // eslint-disable-next-line
  interface DefaultTheme extends ApplicationTheme {}
}

export {applicationTheme};
export type {ApplicationTheme};
```

**src/components/application/application.tsx**

```tsx
import styled from "styled-components";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import * as withTheme from "@unlockre/components-library/dist/theme-provider/theme";

import {applicationTheme} from "./application-theme";

const SomeTextContainer = styled.div`
  ${withTheme.getTypography("body", "m")}

  color: ${withTheme.getColorByAlias("textPrimary")};
  background: ${withTheme.getColor("gray", "550")};
`;

const Application = () => (
  <ThemeProvider theme={applicationTheme}>
    <SomeTextContainer>Some Text</SomeTextContainer>
  </ThemeProvider>
);

export {Application};
```

### Extending an existing theme (Light Theme)

**src/components/application/application-theme.ts**

> The usage of `as const` in the following example is to improve type inference, as if we don't use this, strings are inferred as `string` instead of their values

```tsx
import styled from "styled-components";

import {lightTheme} from "@unlockre/components-library/dist/theme-provider/theme/light-theme";
import type {LightTheme} from "@unlockre/components-library/dist/theme-provider/theme/light-theme";
import * as withTheme from "@unlockre/components-library/dist/theme-provider/theme";

const someApplicationColorPalette = {
  specialRed: "#fe0000"
} as const

const applicationTheme = withTheme.extend(lightTheme, {
  // If we don't need to define additional aliases we could pass {}
  aliases: {
    someAlias1: lightTheme.colors.palette.latest.violet950,
    someAlias2: someApplicationColorPalette.specialRed
  },
  // If we don't need to define additional palettes we could pass {}
  palette: {
    someApplication: someApplicationColorPalette
  }
} as const);

type ApplicationTheme = LightTheme;

declare module "styled-components" {
  // eslint-disable-next-line
  interface DefaultTheme extends DevTheme {}
}

export type {ApplicationTheme} from "@unlockre/components-library/dist/theme-provider/light-theme";
```

**src/components/application/application.tsx**

```tsx
import styled from "styled-components";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import * as withTheme from "@unlockre/components-library/dist/theme-provider/theme";

import {applicationTheme} from "./application-theme";

const SomeTextContainer = styled.div`
  ${withTheme.getTypography("body", "m")}

  color: ${withTheme.getColorByAlias("textPrimary")};
  background: ${withTheme.getColor("gray", "550")};
`;

const Application = () => (
  <ThemeProvider theme={applicationTheme}>
    <SomeTextContainer>Some Text</SomeTextContainer>
  </ThemeProvider>
);

export {Application};
```

## Update the theme

The theme has legacy colors and typographies and new stuff that comes from the design team.

Whenever the theme is updated, we only update the new stuff, the legacy colors and typographies will remain as they are until we deprecate all their usage, so this way we don't break apps that still use the legacy colors and typographies.

Updating the latest colors and typographies with the ones in the [foundation figma](https://www.figma.com/design/6icmCUZBmSYpVS84E9fnEm/Foundations) is simply done by running `yarn update-theme`.

## Configure a package to use the theme

The problem with packages is that they can't define themes, because these are defined in the apps, but they need to use them.

The following steps are needed to solve this issue.

1. Create a module with the development theme (not included in the build)

**src/dev-theme.ts**

```ts
import "styled-components";

import type {DevTheme} from "@unlockre/components-library/dist/theme-provider/theme";

declare module "styled-components" {
  // eslint-disable-next-line
  interface DefaultTheme extends DevTheme {}
}
```

2. Create a module with the production theme (included in the build)

> This module just left the `DefaultTheme` as `any` so it can be extended with the one defined in the app.

**src/prod-theme.ts**

```ts
import "styled-components";

import type {ProdTheme} from "@unlockre/components-library/dist/theme-provider/theme";

declare module "styled-components" {
  // eslint-disable-next-line
  interface DefaultTheme extends ProdTheme {}
}
```

3. Split the package **tsconfig** into the following 3:

> These are examples, they could differ from the ones required for your package

**tsconfig.all.json**

> This one includes the configuration used in all the environments (it extends from the config provided by [@tsconfig/strictest](https://www.npmjs.com/package/@tsconfig/strictest) package)

```json
{
  "$schema": "https://json.schemastore.org/tsconfig.json",
  "extends": "./node_modules/@tsconfig/strictest/tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "baseUrl": ".",
    "declaration": true,
    "exactOptionalPropertyTypes": false,
    "jsx": "react-jsx",
    "lib": ["dom", "es2020"],
    "moduleResolution": "node",
    "noErrorTruncation": true,
    "noPropertyAccessFromIndexSignature": false,
    "paths": {
      "@/*": ["./src/*"]
    },
    "resolveJsonModule": true,
    "rootDir": "./src",
    "typeRoots": ["./@types", "./node_modules/@types"]
  }
}
```

**tsconfig.prod.json**

> This one includes the configuration used for building the package

```json
{
  "$schema": "https://json.schemastore.org/tsconfig.json",
  "extends": "./tsconfig.all.json",
  "compilerOptions": {
    "declaration": true,
    "importHelpers": true,
    "inlineSourceMap": true,
    "inlineSources": true,
    "noEmitOnError": true,
    "outDir": "./dist",
    "target": "es5"
  },
  "exclude": ["dist", "node_modules", "src/dev-theme.ts"],
  "include": ["src"],
}
```

**tsconfig.json**

> This is the one used for development (vscode, eslint, etc)

```json
{
  "extends": "./tsconfig.all.json",
  "exclude": ["dist", "node_modules", "src/prod-theme.ts"],
  "include": ["src"],
}
```

4. Ignore production theme in eslint

> We need to do this because eslint throws an error that this file is not included in TS (excluded in **tsconfig.json**)

**.eslintignore**

```
src/prod-theme.ts
```

5. Configure TS to use **tsconfig.prod.json** for building the package

To do this add the following npm scripts to the **package.json**:

> Your package could already have these or similar ones, if that's the case, modify the one that calls tsc and tsconfig-replace-paths to use the **tsconfig.prod.json** config.

```json
  {
    "build": "yarn build:clean && yarn build:code",
    "build:clean": "rm -rf dist",
    "build:code": "tsc -p ./tsconfig.prod.json && tsconfig-replace-paths -p ./tsconfig.prod.json"
  }
```