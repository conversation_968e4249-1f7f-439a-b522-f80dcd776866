import {useEffect, useRef} from "react";
import type {DependencyList, EffectCallback} from "react";

// TODO: Move this one to @unlockre/utils-react package
const useNotInitialEffect = (
  notInitialEffect: EffectCallback,
  deps: DependencyList
) => {
  const isInitialEffectRef = useRef(true);

  useEffect(() => {
    if (isInitialEffectRef.current) {
      isInitialEffectRef.current = false;

      return;
    }

    return notInitialEffect();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);
};

export default useNotInitialEffect;
