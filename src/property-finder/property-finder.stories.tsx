import {action} from "@storybook/addon-actions";
import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import {useEffect, useState} from "react";
import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledAnchor} from "@/unstyled";

import PropertyFinder from "./property-finder";
import type {PropertyFinderProps} from "./property-finder";
import PropertyItem from "./property-item";
import type {PropertyType} from "./property-type";

type Property = {
  address: string;
  id: string;
  name: string;
  type: PropertyType;
};

type ConcretePropertyFinderProps = PropertyFinderProps<Property>;

type Args = Pick<
  ConcretePropertyFinderProps,
  | "placeholder"
  | "properties"
  | "propertySearch"
  | "variant"
  | "withoutSearchIcon"
>;

const getGoogleMapsUrl = (propertySearch: string) =>
  "https://www.google.com/maps/search/" + encodeURI(propertySearch);

const ViewOnGoogleMapsLink = styled(UnstyledAnchor)`
  ${getTypography("body", "m")}

  color: ${getColorByAlias("accentTertiary")};
  transition: color 200ms ease-in-out;

  &:hover {
    color: ${getColorByAlias("accentSecondary")};
    text-decoration: underline;
  }
`;

const renderViewOnGoogleMapsLink = (propertySearch: string) => (
  <ViewOnGoogleMapsLink
    href={getGoogleMapsUrl(propertySearch)}
    rel="noreferrer"
    target="_blank"
  >
    View on Google Maps
  </ViewOnGoogleMapsLink>
);

const dispatchFocusChangeAction = action("FocusChange");

const renderPropertyItem: ConcretePropertyFinderProps["renderPropertyItem"] = ({
  property,
  ...rest
}) => (
  <PropertyItem
    {...rest}
    bottomRight="something"
    propertyAddress={property.address}
    propertyName={property.name}
    propertyType={property.type}
  />
);

const isSearchedProperty = (propertySearch: string) => (property: Property) =>
  property.address.toLowerCase().includes(propertySearch.toLowerCase()) ||
  property.id.includes(propertySearch) ||
  property.name.toLowerCase().includes(propertySearch.toLowerCase());

const useSearchedProperties = (
  properties: Property[],
  propertySearch: string,
  selectedProperty: Property | null
) => {
  const [isLoading, setIsLoading] = useState(false);

  const [searchedProperties, setSearchedProperties] = useState<Property[]>([]);

  useEffect(() => {
    if (selectedProperty || !propertySearch || properties.length === 0) {
      setIsLoading(false);

      setSearchedProperties([]);

      return;
    }

    console.log("isLoading = true");

    setIsLoading(true);

    const timeoutId = setTimeout(() => {
      setSearchedProperties(
        properties.filter(isSearchedProperty(propertySearch))
      );

      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [properties, propertySearch, selectedProperty]);

  return {isLoading, searchedProperties};
};

const Default: StoryFn<Args> = ({properties, propertySearch, ...rest}) => {
  const [, updateArgs] = useArgs();

  const [selectedProperty, setSelectedProperty] = useState<Property | null>(
    null
  );

  const {isLoading, searchedProperties} = useSearchedProperties(
    properties,
    propertySearch,
    selectedProperty
  );

  const updatePropertySearch = (propertySearch: string) => {
    updateArgs({propertySearch});
  };

  const handlePropertySelect = (property: Property) => {
    setSelectedProperty(property);

    updatePropertySearch(property.name);
  };

  const handlePropertySearchChange = (propertySearch: string) => {
    updatePropertySearch(propertySearch);

    setSelectedProperty(null);
  };

  return (
    <PropertyFinder
      {...rest}
      {...{isLoading, propertySearch, renderPropertyItem, selectedProperty}}
      noPropertiesRight={renderViewOnGoogleMapsLink(propertySearch)}
      onFocusChange={dispatchFocusChangeAction}
      onPropertySearchChange={handlePropertySearchChange}
      onPropertySelect={handlePropertySelect}
      properties={searchedProperties}
    />
  );
};

const meta: Meta<Args> = {
  title: "property-finder",
  argTypes: {
    placeholder: {
      control: "text"
    },
    properties: {
      control: "object"
    },
    propertySearch: {
      control: "text"
    },
    variant: {
      control: "radio",
      options: ["white", "blue"]
    },
    withoutSearchIcon: {
      control: "boolean"
    }
  },
  args: {
    placeholder: "Search by name, address, or ID",
    properties: [
      {
        address: "3500 Maple Ave, Dallas, TX",
        name: "3500 Maple Ave",
        id: "1",
        type: "NNN"
      },
      {
        address: "9250 Pinecroft Dr, Shenandoah, TX",
        name: "9250 Pinecroft Dr",
        id: "2",
        type: "NNN"
      },
      {
        address: "26273 S Royal Crest Dr, Crete, IL",
        name: "26273 S Royal Crest Dr",
        id: "3",
        type: "NNN"
      },
      {
        address: "3530 Travis St, Dallas, TX",
        name: "Riviera at West Village",
        id: "4",
        type: "MULTIFAMILY"
      },
      {
        address: "1500 N Garrett Ave, Dallas, TX",
        name: "Paradise Gardens",
        id: "5",
        type: "MULTIFAMILY"
      },
      {
        address: "350 N Ervay St, Dallas, TX",
        name: "Gables Republic Tower",
        id: "6",
        type: "MULTIFAMILY"
      }
    ],
    propertySearch: "",
    variant: "white",
    withoutSearchIcon: false
  }
};

export {meta as default, Default};
