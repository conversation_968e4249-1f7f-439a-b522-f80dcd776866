const propertyTypes = {
  multifamily: "MULTIFAMILY",
  nnn: "NNN"
} as const;

type PropertyType = (typeof propertyTypes)[keyof typeof propertyTypes];

const descriptions = {
  [propertyTypes.multifamily]: "Multifamily",
  [propertyTypes.nnn]: "NNN"
};

const getDescription = (propertyType: PropertyType) =>
  descriptions[propertyType];

export {getDescription, propertyTypes};

export type {PropertyType};
