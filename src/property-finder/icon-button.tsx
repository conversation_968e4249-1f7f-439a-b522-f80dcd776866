import colorAlpha from "color-alpha";
import type {ReactElement} from "react";
import styled, {keyframes} from "styled-components";

import {getColor} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type Variant = "default" | "primary" | "secondary";

type Props = {
  isDisabled?: boolean;
  onClick?: () => void;
  renderIcon: (getColor: GetColor) => ReactElement;
  variant?: Variant;
};

type VariantDefinition = {
  getBackgroundColor?: GetColor;
  getClickColor: GetColor;
  getDisabledColor: GetColor;
  getHoverColor: GetColor;
  getIconColor: GetColor;
};

type StyledButtonStyledProps = {
  isDisabled?: boolean;
  variant: Variant;
};

const rippleKeyframes = keyframes`
    0% {
      opacity: 0.5;
      scale: 0;
    }

    30% {
      opacity: 1;
      scale: 1;
    }

    100% {
      opacity: 0;
      scale: 1;
    }
`;

const variantDefinitions: Record<Variant, VariantDefinition> = {
  default: {
    getClickColor: props => colorAlpha(getColor("gray", "500")(props), 0.2),
    getDisabledColor: getColor("gray", "100"),
    getHoverColor: props => colorAlpha(getColor("gray", "500")(props), 0.1),
    getIconColor: getColor("gray", "400")
  },

  primary: {
    getClickColor: props => colorAlpha(getColor("blue", "500")(props), 0.2),
    getDisabledColor: getColor("gray", "100"),
    getHoverColor: props => colorAlpha(getColor("blue", "500")(props), 0.1),
    getIconColor: getColor("blue", "800")
  },

  secondary: {
    getBackgroundColor: getColor("gray", "000"),
    getClickColor: props => colorAlpha(getColor("gray", "1000")(props), 0.5),
    getDisabledColor: getColor("gray", "100"),
    getHoverColor: props => colorAlpha(getColor("gray", "1000")(props), 0.2),
    getIconColor: getColor("gray", "000")
  }
};

const StyledButton = styled(UnstyledButton)<StyledButtonStyledProps>`
  align-items: center;
  background-color: ${({variant}) =>
    variantDefinitions[variant].getBackgroundColor || "none"};
  border-radius: 100%;
  display: flex;
  height: 32px;
  justify-content: center;
  pointer-events: ${({isDisabled}) => (isDisabled ? "none" : "auto")};
  position: relative;
  width: 32px;
  z-index: 0;

  /* These pseudo-elements are used to create the ripple effect */
  /* The first one is the circle that appears when you hover */
  /* The second one is the circle that appears when you click */
  &::before,
  &::after {
    border-radius: 100%;
    content: "";
    height: 100%;
    left: 50%;
    position: absolute;
    scale: 0;
    top: 50%;
    translate: -50% -50%;
    width: 100%;
    z-index: -1;
  }

  /* Hover ripple animation */
  &::before {
    background-color: ${({variant}) =>
      variantDefinitions[variant].getHoverColor};
    transition:
      scale 200ms,
      opacity 200ms;
  }

  &:hover::before {
    scale: 1;
  }

  /* Click ripple animation */
  &::after {
    background-color: ${({variant}) =>
      variantDefinitions[variant].getClickColor};
  }

  &:active::after {
    animation: ${rippleKeyframes} 250ms ease-out;
  }
`;

// TODO: Check if it makes sense to move this component to the library
const IconButton = ({
  isDisabled,
  onClick,
  renderIcon,
  variant = "default"
}: Props) => (
  <StyledButton isDisabled={isDisabled} onClick={onClick} variant={variant}>
    {renderIcon(
      isDisabled
        ? variantDefinitions[variant].getDisabledColor
        : variantDefinitions[variant].getIconColor
    )}
  </StyledButton>
);

export default IconButton;
