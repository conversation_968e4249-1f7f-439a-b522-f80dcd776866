import type {ReactElement} from "react";
import styled, {css} from "styled-components";

import {CloseIcon} from "@/icons";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import FloatingContainer from "./floating-container";
import {getResultsBorderCss} from "./get-results-border";
import IconButton from "./icon-button";
import type {PropertyFinderVariant} from "./property-finder-variant";

type Props = {
  onClose: () => unknown;
  right?: ReactElement;
  shouldHide?: boolean;
  variant: PropertyFinderVariant;
};

type StyledProps = {
  $variant: PropertyFinderVariant;
};

const Apologies = styled.span<StyledProps>`
  ${getTypography("body", "m")}

  color: ${props =>
    props.$variant === "white"
      ? getColorByAlias("textSecondary")
      : getColorByAlias("textInverted")};
`;

const Content = styled.div<StyledProps>`
  padding: 12px 24px;
  align-items: center;
  display: grid;
  grid-gap: 8px;
  grid-template-columns: auto 1fr auto;
  ${props =>
    props.$variant === "blue" &&
    css`
      background-color: ${getColor("blue", "950")(props)};
      color: ${getColorByAlias("backgroundWhite")(props)};
    `}

  ${getResultsBorderCss};
`;

const NoProperties = ({
  onClose,
  right,
  shouldHide,
  variant = "white"
}: Props) => (
  <FloatingContainer $shouldHide={shouldHide} $variant={variant}>
    <Content $variant={variant}>
      <IconButton
        onClick={onClose}
        renderIcon={getColor => <CloseIcon {...{getColor}} size={16} />}
        variant={variant === "white" ? "primary" : "default"}
      />
      <Apologies $variant={variant}>
        Sorry, we don&apos;t have any results for your search
      </Apologies>
      {right}
    </Content>
  </FloatingContainer>
);

export default NoProperties;
