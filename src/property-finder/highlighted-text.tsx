type Props = {
  children: string;
  highlight?: string;
};

// TODO: Check if it makes sense to move this component to the library
const HighlightedText = ({children, highlight}: Props) => {
  if (!highlight) {
    return <>{children}</>;
  }

  const highlightIndex = children
    .toLowerCase()
    .indexOf(highlight.toLowerCase());

  if (highlightIndex === -1) {
    return <>{children}</>;
  }

  const before = children.slice(0, highlightIndex);

  const highlighted = children.slice(
    highlightIndex,
    highlightIndex + highlight.length
  );

  const after = children.slice(highlightIndex + highlight.length);

  return (
    <>
      {before}
      <strong>{highlighted}</strong>
      {after}
    </>
  );
};

export default HighlightedText;
