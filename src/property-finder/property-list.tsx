import {Fragment} from "react";
import type {ReactElement} from "react";
import styled, {css} from "styled-components";

import {getColor} from "@/theme-provider/theme";
import {UnstyledUl} from "@/unstyled";

import FloatingContainer from "./floating-container";
import {getResultsBorderCss} from "./get-results-border";
import type {PropertyFinderVariant} from "./property-finder-variant";

type RenderPropertyItemParams<TProperty extends object> = {
  index: number;
  isFirst?: boolean;
  isLast?: boolean;
  onClick: () => unknown;
  property: TProperty;
  propertySearch: string;
  variant?: PropertyFinderVariant;
};

type RenderPropertyItem<TProperty extends object> = (
  params: RenderPropertyItemParams<TProperty>
) => ReactElement;

type Props<TProperty extends object> = {
  onPropertySelect: (property: TProperty) => unknown;
  properties: TProperty[];
  propertySearch: string;
  renderPropertyItem: RenderPropertyItem<TProperty>;
  shouldHide?: boolean;
  variant?: PropertyFinderVariant;
};

const StyledList = styled(UnstyledUl)<{$variant?: PropertyFinderVariant}>`
  ${props =>
    props.$variant === "blue" &&
    css`
      background-color: ${getColor("blue", "950")};
    `}
  ${getResultsBorderCss};
`;

const PropertyList = <TProperty extends object>({
  onPropertySelect,
  properties,
  propertySearch,
  renderPropertyItem,
  shouldHide,
  variant
}: Props<TProperty>) => (
  <FloatingContainer $shouldHide={shouldHide}>
    <StyledList $variant={variant}>
      {properties.map((property, index) => (
        <Fragment key={index}>
          {renderPropertyItem({
            property,
            index,
            propertySearch,
            onClick: () => onPropertySelect(property),
            variant,
            isFirst: index === 0,
            isLast: index === properties.length - 1
          })}
        </Fragment>
      ))}
    </StyledList>
  </FloatingContainer>
);

export default PropertyList;

export type {Props as PropertyListProps};
