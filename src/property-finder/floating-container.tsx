import colorAlpha from "color-alpha";
import styled, {css} from "styled-components";
import type {ThemeProps} from "styled-components";

import {getColor} from "@/theme-provider/theme";
import type {Theme} from "@/theme-provider/theme";

import type {PropertyFinderVariant} from "./property-finder-variant";

type StyledProps = {
  $shouldHide?: boolean;
  $variant?: PropertyFinderVariant;
};

const borderAndShadowCss = (props: ThemeProps<Theme>) => css`
  border: 1px solid white;
  box-shadow: 8px 8px 40px 6px
    ${colorAlpha(getColor("blue", "900")(props), 0.2)};
`;

const showCss = css`
  opacity: 1;
  pointer-events: all;
  translate: 0 0;
`;

const hideCss = css`
  opacity: 0;
  pointer-events: none;
  translate: 0 -16px;
`;

// We are only changing the opacity to animate the list hide/show
// transition. However, child elements are still focusable and will
// receive focus, preventing users from correctly using the keyboard.
// The "inert" property works in modern browsers and prevents the focus
// on child elements.
// Note: inert={true} doesn't work as React filters any unknown
// non-string prop.
const FloatingContainer = styled.div.attrs(({$shouldHide}: StyledProps) =>
  $shouldHide ? {inert: ""} : {}
)<StyledProps>`
  ${props => (props.$shouldHide ? hideCss : showCss)}

  ${props => props.$variant === "white" && borderAndShadowCss}

  background: white;
  border-radius: 16px;
  box-sizing: border-box;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: calc(100% + 12px);
  transition: all 200ms ease-in-out;
  width: 100%;
`;

export default FloatingContainer;
