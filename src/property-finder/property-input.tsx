import {MagnifyingGlass, X} from "@phosphor-icons/react";
import colorAlpha from "color-alpha";
import mergeRefs from "merge-refs";
import {useRef} from "react";
import type {KeyboardEventHandler, Ref} from "react";
import styled, {css} from "styled-components";
import type {ThemeProps} from "styled-components";

import {IconWithColor} from "@/icons";
import {getGradientRoundedBorder} from "@/property-finder/get-gradient-rounded-border";
import Spinner from "@/spinner";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";
import type {Theme} from "@/theme-provider/theme";
import {UnstyledButton, UnstyledInput} from "@/unstyled";

import type {PropertyFinderVariant} from "./property-finder-variant";

type Props = {
  inputRef?: Ref<HTMLInputElement>;
  isActive?: boolean;
  isLoading?: boolean;
  onFocus?: () => unknown;
  onPropertySearchChange: (propertySearch: string) => unknown;
  placeholder: string;
  propertySearch: string;
  variant?: PropertyFinderVariant;
  withoutSearchIcon?: boolean;
};

type VariantStyledProps = {
  $variant: PropertyFinderVariant;
};

type ContainerStyledProps = VariantStyledProps & {
  $isActive?: boolean;
};

type RightSideElementContainerStyledProps = {
  $shouldHide?: boolean;
  $variant?: PropertyFinderVariant;
};

const colorGettersByVariant = {
  white: {
    getPlaceholderColor: getColorByAlias("textSecondary"),
    getInputColor: getColorByAlias("accentTertiary"),
    getInactiveBackgroundColor: (props: ThemeProps<Theme>) =>
      colorAlpha(getColorByAlias("backgroundWhite")(props), 0.6),
    getActiveBackgroundColor: getColorByAlias("backgroundWhite"),
    getInactiveBorderColor: "transparent",
    getClearIconColor: getColorByAlias("accentTertiary"),
    getSearchIconActiveColor: getColor("gray", "550"),
    getSearchIconInactiveColor: getColor("blue", "800")
  },
  blue: {
    getPlaceholderColor: getColor("gray", "250"),
    getInputColor: getColorByAlias("backgroundWhite"),
    getInactiveBackgroundColor: getColor("blue", "950"),
    getActiveBackgroundColor: getColor("blue", "950"),
    getInactiveBorderColor: "",
    getClearIconColor: getColorByAlias("textInverted"),
    getSearchIconActiveColor: getColor("gray", "250"),
    getSearchIconInactiveColor: getColor("gray", "250")
  }
};

// We are only changing the opacity to animate the list hide/show
// transition. However, child elements are still focusable and will
// receive focus, preventing users from correctly using the keyboard.
// The "inert" property works in modern browsers and prevents the focus
// on child elements.
// Note: inert={true} doesn't work as React filters any unknown
// non-string prop.
const RightSideElementContainer = styled.div.attrs(
  ({$shouldHide}: RightSideElementContainerStyledProps) =>
    $shouldHide ? {inert: ""} : {}
)<RightSideElementContainerStyledProps>`
  opacity: ${props => (props.$shouldHide ? 0 : 1)};
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translate(0, -50%);
  transition-delay: 100ms;
  transition-duration: 200ms;
  transition-property: opacity;
  transition-timing-function: ease-in-out;
`;

const Placeholder = styled.div<VariantStyledProps>`
  ${getTypography("body", "l")}

  align-items: center;
  color: ${props => colorGettersByVariant[props.$variant].getPlaceholderColor};
  display: flex;
  position: absolute;
  scale: 1;
  top: 50%;
  transform-origin: top left;
  transition:
    translate 200ms ease-in-out,
    scale 200ms ease-in-out;
  translate: 0 -50%;
`;

const StyledInput = styled(UnstyledInput)<VariantStyledProps>`
  ${getTypography("body", "l")}

  background-color: transparent;
  color: ${props => colorGettersByVariant[props.$variant].getInputColor};
  height: 100%;
  position: relative;
  top: 8px;
  width: 100%;

  &:focus ~ ${Placeholder}, &:not(:focus):valid ~ ${Placeholder} {
    scale: 0.65;
    translate: 0 -100%;
  }
`;

const containerInactiveCss = css<ContainerStyledProps>`
  background-color: ${props =>
    colorGettersByVariant[props.$variant].getInactiveBackgroundColor};
  border-color: ${props =>
    colorGettersByVariant[props.$variant].getInactiveBorderColor};

  ${props =>
    props.$variant === "blue" &&
    getGradientRoundedBorder(
      1,
      16,
      css`
        background: radial-gradient(
          104.29% 104.29% at 95.24% 9.29%,
          #915d8b 1.43%,
          #654a8d 18.38%,
          #1f3383 96.24%
        );
      `
    )}
`;

const containerActiveCss = css<ContainerStyledProps>`
  background-color: ${props =>
    colorGettersByVariant[props.$variant].getActiveBackgroundColor};
  border-color: ${getColorByAlias("accentPrimary")};
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => (props.$isActive ? containerActiveCss : containerInactiveCss)}

  border-width: 1px;
  border-style: solid;
  border-radius: 16px;
  box-sizing: border-box;
  height: 72px;
  padding: 16px 16px;
  position: relative;
  transition:
    background-color 600ms ease-in-out,
    border-color 200ms ease-in-out;
  width: 100%;
`;

// eslint-disable-next-line complexity
const PropertyInput = ({
  inputRef: inputRefProp,
  isActive,
  isLoading,
  onFocus,
  onPropertySearchChange,
  placeholder,
  propertySearch,
  variant = "white",
  withoutSearchIcon
}: Props) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleContainerClick = () => {
    if (!inputRef.current) {
      return;
    }

    inputRef.current.focus();
  };

  const clearOnEscape: KeyboardEventHandler = ({key}) => {
    if (key === "Escape") {
      onPropertySearchChange("");
    }
  };

  return (
    <Container
      $isActive={isActive}
      $variant={variant}
      onClick={handleContainerClick}
    >
      <StyledInput
        $variant={variant}
        onChange={e => onPropertySearchChange(e.target.value)}
        onFocus={onFocus}
        onKeyDown={clearOnEscape}
        ref={mergeRefs(inputRef, inputRefProp)}
        required
        value={propertySearch}
      />
      <Placeholder $variant={variant}>
        {!withoutSearchIcon && (
          <IconWithColor
            getColor={
              isActive
                ? colorGettersByVariant[variant].getSearchIconActiveColor
                : colorGettersByVariant[variant].getSearchIconInactiveColor
            }
            icon={MagnifyingGlass}
            size={21}
            weight="regular"
          />
        )}
        &nbsp;{placeholder}
      </Placeholder>
      <RightSideElementContainer $shouldHide={!isLoading}>
        <Spinner size={20} />
      </RightSideElementContainer>
      <RightSideElementContainer
        $shouldHide={isLoading || propertySearch.length === 0}
      >
        <UnstyledButton
          onClick={() => onPropertySearchChange("")}
          tabIndex={-1}
        >
          <IconWithColor
            getColor={colorGettersByVariant[variant].getClearIconColor}
            icon={X}
            size={16}
            weight="regular"
          />
        </UnstyledButton>
      </RightSideElementContainer>
    </Container>
  );
};

export default PropertyInput;
