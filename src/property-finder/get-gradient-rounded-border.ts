import {css} from "styled-components";

const generateBorderMask = (borderWidth: number, borderRadius: number) => {
  const maskImage =
    encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
        <rect
          x="0" y="0" width="100%" height="100%"
          rx="${borderRadius}" ry="${borderRadius}"
          fill="transparent" stroke="white" stroke-width="${borderWidth * 2}"/>
      </svg>`);

  return css`
    --mask: url("data:image/svg+xml;utf8,${maskImage}");
    mask: var(--mask);
  `;
};

const getGradientRoundedBorder = (
  borderWidth: number,
  borderRadius: number,
  background: ReturnType<typeof css>
) => css`
  position: relative;
  z-index: 0;
  border: ${borderWidth}px solid transparent;
  border-radius: ${borderRadius}px;

  ::before {
    content: "";
    position: absolute;
    z-index: -1;
    top: ${-borderWidth}px;
    left: ${-borderWidth}px;
    right: ${-borderWidth}px;
    bottom: ${-borderWidth}px;
    border-radius: inherit;
    ${background}
    ${generateBorderMask(borderWidth, borderRadius)}
  }
`;

export {getGradientRoundedBorder};
