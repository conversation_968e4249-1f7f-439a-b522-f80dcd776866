import {useRef, useState} from "react";
import type {Compo<PERSON><PERSON><PERSON>, FocusEventHandler, ReactElement, Ref} from "react";
import styled from "styled-components";

import NoProperties from "./no-properties";
import PropertyInput from "./property-input";
import PropertyList from "./property-list";
import type {PropertyListProps} from "./property-list";
import useNotInitialEffect from "./use-not-initial-effect";

type ExposedPropertyListProps<TProperty extends object> = Omit<
  PropertyListProps<TProperty>,
  "shouldShow"
>;

type PropertyInputProps = ComponentProps<typeof PropertyInput>;

type ExposedPropertyInputProps = Omit<
  PropertyInputProps,
  "isActive" | "onFocus" | "onPropertySearchChange"
>;

// prettier-ignore
type Props<TProperty extends object> =
  & ExposedPropertyInputProps
  & ExposedPropertyListProps<TProperty>
  & {
      className?: string;
      inputRef?: Ref<HTMLInputElement>;
      noPropertiesRight?: ReactElement;
      onFocusChange?: (isFocused: boolean) => unknown,
      onPropertySearchChange: (propertySearch: string) => unknown;
      selectedProperty: TProperty | null
    };

const Container = styled.div`
  position: relative;
  width: 560px;
`;

// eslint-disable-next-line complexity
const PropertyFinder = <TProperty extends object>({
  className,
  inputRef,
  isLoading,
  noPropertiesRight,
  onFocusChange,
  onPropertySearchChange,
  placeholder,
  properties,
  propertySearch,
  selectedProperty,
  variant = "white",
  withoutSearchIcon,
  ...rest
}: Props<TProperty>) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const [isFocused, setIsFocused] = useState(false);

  useNotInitialEffect(() => {
    onFocusChange?.(isFocused);
  }, [isFocused, onFocusChange]);

  const onBlur: FocusEventHandler = ({relatedTarget}) => {
    const {current: container} = containerRef;
    if (!container?.contains(relatedTarget)) {
      setIsFocused(false);
    }
  };

  return (
    <Container
      {...{className}}
      onBlur={onBlur}
      onClick={() => setIsFocused(true)}
      ref={containerRef}
    >
      <PropertyInput
        {...{isLoading, onPropertySearchChange, placeholder, propertySearch}}
        inputRef={inputRef}
        isActive={isFocused}
        onFocus={() => setIsFocused(true)}
        variant={variant}
        withoutSearchIcon={withoutSearchIcon}
      />
      <NoProperties
        onClose={() => onPropertySearchChange("")}
        right={noPropertiesRight}
        shouldHide={
          !isFocused ||
          isLoading ||
          properties.length !== 0 ||
          !propertySearch ||
          selectedProperty !== null
        }
        variant={variant}
      />
      <PropertyList
        {...{properties, propertySearch}}
        {...rest}
        shouldHide={
          !isFocused ||
          isLoading ||
          properties.length === 0 ||
          selectedProperty !== null
        }
        variant={variant}
      />
    </Container>
  );
};

export default PropertyFinder;

export type {Props as PropertyFinderProps};
