import {css} from "styled-components";

import {getGradientRoundedBorder} from "./get-gradient-rounded-border";
import type {PropertyFinderVariant} from "./property-finder-variant";

const getResultsBorderCss = <T extends {$variant?: PropertyFinderVariant}>(
  props: T
) =>
  props.$variant === "blue" &&
  getGradientRoundedBorder(
    1,
    16,
    css`
      background: radial-gradient(
        104.29% 104.29% at 95.24% 9.29%,
        #915d8b 1.43%,
        #654a8d 18.38%,
        #1f3383 96.24%
      );
    `
  );

export {getResultsBorderCss};
