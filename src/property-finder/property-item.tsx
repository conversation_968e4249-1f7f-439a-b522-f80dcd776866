import styled, {css, keyframes} from "styled-components";

import {MultifamilyIcon, NNNIcon} from "@/icons";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import HighlightedText from "./highlighted-text";
import type {PropertyFinderVariant} from "./property-finder-variant";
import * as withPropertyType from "./property-type";
import type {PropertyType} from "./property-type";

type Props = {
  bottomRight?: string;
  index: number;
  isFirst?: boolean;
  isLast?: boolean;
  onClick: () => unknown;
  propertyAddress: string;
  propertyName: string;
  propertySearch: string;
  propertyType: PropertyType;
  variant?: PropertyFinderVariant;
};

type VariantStyledProps = {
  $variant: PropertyFinderVariant;
};
type ContainerStyledProps = VariantStyledProps & {
  $index: number;
};

type TextStyledProps = VariantStyledProps & {
  $isPrimary?: boolean;
};

type ButtonStyledProps = VariantStyledProps & {
  $isFirst?: boolean;
  $isLast?: boolean;
};

const colorGettersByVariant = {
  white: {
    getBackgroundColor: getColorByAlias("backgroundWhite"),
    getHoverBackgroundColor: getColor("blue", "040"),
    getTextPrimary: getColorByAlias("accentTertiary"),
    getTextSecondary: getColorByAlias("textSecondary"),
    getIconColor: getColor("blue", "800")
  },
  blue: {
    getBackgroundColor: () => "",
    getHoverBackgroundColor: getColor("blue", "900"),
    getTextPrimary: getColorByAlias("textInverted"),
    getTextSecondary: getColor("gray", "250"),
    getIconColor: getColorByAlias("textInverted")
  }
};

const Text = styled.div<TextStyledProps>`
  color: ${props =>
    props.$isPrimary
      ? colorGettersByVariant[props.$variant].getTextPrimary
      : colorGettersByVariant[props.$variant].getTextSecondary};
`;

const SmallText = styled(Text)`
  ${getTypography("body", "xs")}
`;

const LargeText = styled(Text)`
  ${getTypography("body", "m")}
`;

const Right = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-around;
  text-align: right;
`;

const Center = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  height: 100%;
  text-align: left;
`;

const Left = styled.div`
  position: relative;
  top: 4px;
`;

const getPropertyTypeIconProps = (variant: PropertyFinderVariant) => ({
  getColor: colorGettersByVariant[variant].getIconColor,
  size: 16
});

const {propertyTypes} = withPropertyType;

const renderPropertyTypeIcon = (
  propertyType: PropertyType,
  variant: PropertyFinderVariant = "white"
) => {
  switch (propertyType) {
    case propertyTypes.multifamily:
      return <MultifamilyIcon {...getPropertyTypeIconProps(variant)} />;

    case propertyTypes.nnn:
      return <NNNIcon {...getPropertyTypeIconProps(variant)} />;
  }
};

const StyledButton = styled(UnstyledButton)<ButtonStyledProps>`
  ${getTypography("body", "m")}

  display: grid;
  grid-gap: 8px;
  grid-template-columns: auto 1fr auto;
  padding: 12px 24px;
  transition: all 200ms ease-in-out;
  width: 100%;
  background-color: ${props =>
    colorGettersByVariant[props.$variant].getBackgroundColor};
  border-top-left-radius: ${props => (props.$isFirst ? "16px" : "0")};
  border-top-right-radius: ${props => (props.$isFirst ? "16px" : "0")};
  border-bottom-left-radius: ${props => (props.$isLast ? "16px" : "0")};
  border-bottom-right-radius: ${props => (props.$isLast ? "16px" : "0")};
  &:hover {
    background-color: ${props =>
      colorGettersByVariant[props.$variant].getHoverBackgroundColor};
  }
`;

const slidesInKeyframes = keyframes`
  from {
    opacity: 0;
    translate: -16px 0;
  }

  to {
    opacity: 1;
    translate: 0px 0;
  }
`;
const blueVariantBorderBottomCSS = css`
  border-image-source: radial-gradient(
    104.29% 104.29% at 95.24% 9.29%,
    #915d8b 1.43%,
    #654a8d 18.38%,
    #1f3383 96.24%
  );
  border-image-slice: 1;
  border-bottom: 1px solid;
`;

const whiteVariantBorderBottomCSS = css`
  border-bottom: 1px solid ${getColorByAlias("backgroundPrimary")};
`;

const Container = styled.li<ContainerStyledProps>`
  animation-delay: ${props => props.$index * 150}ms;
  animation-duration: 200ms;
  animation-fill-mode: forwards;
  animation-timing-function: ease-in-out;
  animation-name: ${slidesInKeyframes};
  opacity: 0;
  /* We need the following rule as otherwise animation breaks in chrome on linux */
  will-change: opacity, translate;

  ${props =>
    props.$variant === "blue"
      ? blueVariantBorderBottomCSS
      : whiteVariantBorderBottomCSS};

  &:last-child {
    border-bottom: none;
  }
`;

const PropertyItem = ({
  bottomRight,
  index,
  isFirst,
  isLast,
  onClick,
  propertyAddress,
  propertyName,
  propertySearch,
  propertyType,
  variant = "white"
}: Props) => (
  <Container $index={index} $variant={variant}>
    <StyledButton
      {...{onClick}}
      $isFirst={isFirst}
      $isLast={isLast}
      $variant={variant}
    >
      <Left>{renderPropertyTypeIcon(propertyType, variant)}</Left>
      <Center>
        <LargeText $isPrimary $variant={variant}>
          <HighlightedText highlight={propertySearch}>
            {propertyName}
          </HighlightedText>
        </LargeText>
        <SmallText $variant={variant}>
          <HighlightedText highlight={propertySearch}>
            {propertyAddress}
          </HighlightedText>
        </SmallText>
      </Center>
      <Right>
        <SmallText $isPrimary $variant={variant}>
          {withPropertyType.getDescription(propertyType)}
        </SmallText>
        {bottomRight !== undefined && (
          <SmallText $variant={variant}>{bottomRight}</SmallText>
        )}
      </Right>
    </StyledButton>
  </Container>
);

export default PropertyItem;
