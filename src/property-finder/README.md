# PropertyFinder

## About

This component provides an input to enter some text that can be used to search properties.

The way it was constructed allows to use any property type, given that a `renderPropertyItem` function is used to render the property results.

## Example

**view-on-google-maps-link.tsx**

```tsx
import {getColorByAlias, getTypography} from "@unlockre/components-library/dist/theme-provider/theme";
import {UnstyledAnchor} from "@unlockre/components-library/dist/unstyled";
import styled from "styled-components";

const getGoogleMapsUrl = (propertySearch: string) =>
  "https://www.google.com/maps/search/" + encodeURI(propertySearch);

const Container = styled(UnstyledAnchor)`
  ${getTypography("body", "m")}

  color: ${getColorByAlias("accentTertiary")};
  transition: color 200ms ease-in-out;

  &:hover {
    color: ${getColorByAlias("accentSecondary")};
    text-decoration: underline;
  }
`;

const ViewOnGoogleMapsLink = ({propertySearch}: Props) => (
  <Container
    href={getGoogleMapsUrl(propertySearch)}
    target="_blank"
  >
    View on Google Maps
  </Container>
);

export default ViewOnGoogleMapsLink;
```

**some-property-finder.tsx**

```tsx
import PropertyFinder from "@unlockre/components-library/dist/property-finder";
import type {PropertyFinderProps} from "@unlockre/components-library/dist/property-finder";
import type {PropertyType} from "@unlockre/components-library/dist/property-finder/property-type";
import {useState} from "react";

import {useSomeApiGet} from "@/utils/some-api";

import ViewOnGoogleMapsLink from "./view-on-google-maps-link";

type SomeProperty = {
  address: string;
  id: string;
  name: string;
  something: string;
  type: PropertyType;
};

type Props = Omit<
  PropertyFinderProps<SomeProperty>,
  "isLoading" | "properties" | "propertySearch" | "renderPropertyItem"
>;

const renderPropertyItem: Props["renderPropertyItem"] = ({
  property,
  ...rest
}) => (
  <PropertyItem
    {...rest}
    // this prop is optional
    bottomRight={property.something}
    propertyAddress={property.address}
    propertyName={property.name}
    propertyType={property.type}
  />
);

const SomePropertyFinder = (props: Props) => {
  const [propertySearch, setPropertySearch] = useState("");

  const propertiesResponse = useSomeApiGet(
    "/properties",
    {query: propertySearch}
    {skip: !propertySearch}
  );

  const {isLoading} = propertiesResponse;

  const properties = propertiesResponse.data?.data;

  return (
      <PropertyFinder
        {...props}
        {...{isLoading, renderPropertyItem}}
        noPropertiesRight={<ViewOnGoogleMapsLink {...{propertySearch}} />}
        onPropertySearchChange={setPropertySearch}
        placeholder="Search by name, address, or ID"
      />
  );
}

export default SomePropertyFinder;
```