import {Rocket} from "@phosphor-icons/react";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {badgeColorCategories} from "./badge-color-category";
import {allBadgeColorShades} from "./badge-color-shade";
import {CategoryBadge} from "./category-badge";

type CategoryBadgeProps = ComponentProps<typeof CategoryBadge>;

type ExposedCategoryBadgeProps = Pick<
  CategoryBadgeProps,
  "colorCategory" | "colorShade" | "value"
>;

type Args = ExposedCategoryBadgeProps & {
  hasLeftIcon: boolean;
  hasRightIcon: boolean;
};

const Default: StoryFn<Args> = ({
  hasLeftIcon,
  hasRightIcon,
  value,
  ...rest
}) => (
  <CategoryBadge
    {...rest}
    leftIcon={hasLeftIcon ? Rocket : undefined}
    rightIcon={hasRightIcon ? Rocket : undefined}
    value={value === "" ? null : value}
  />
);

const meta: Meta<Args> = {
  title: "badges/category-badge",
  argTypes: {
    colorCategory: {
      control: "select",
      options: Object.values(badgeColorCategories)
    },
    colorShade: {
      control: "select",
      options: allBadgeColorShades
    },
    hasLeftIcon: {
      control: "boolean"
    },
    hasRightIcon: {
      control: "boolean"
    },

    value: {
      control: "text"
    }
  },
  args: {
    colorCategory: badgeColorCategories.lavender,
    colorShade: 3,
    hasLeftIcon: false,
    hasRightIcon: false,
    value: "Some Value"
  }
};

export {meta as default, Default};
