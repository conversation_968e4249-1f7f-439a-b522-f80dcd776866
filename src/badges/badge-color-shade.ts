const allBadgeColorShades = [1, 2, 3, 4, 5, 6, 7] as const;

type BadgeColorShade = (typeof allBadgeColorShades)[number];

type BadgeColorShadeToString<TBadgeColorShade extends BadgeColorShade> =
  `${TBadgeColorShade}`;

const toString = <TBadgeColorShade extends BadgeColorShade>(
  badgeColorShade: TBadgeColorShade
) => String(badgeColorShade) as BadgeColorShadeToString<TBadgeColorShade>;

export {allBadgeColorShades, toString};
export type {BadgeColorShade};
