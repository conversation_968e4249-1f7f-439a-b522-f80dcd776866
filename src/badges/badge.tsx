import type {Icon as IconComponent} from "@phosphor-icons/react";
import {formatOptionalValue} from "@unlockre/utils-formatting/dist";
import {forwardRef} from "react";
import styled from "styled-components";

import {IconWithColor} from "@/icons";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";

import {BadgeContainer} from "./badge-container";
import type {
  BadgeContainerElement,
  BadgeContainerProps
} from "./badge-container";

type BadgeValue = number | string;

type ExposedBadgeContainerProps = Pick<
  BadgeContainerProps,
  "className" | "onMouseEnter" | "onMouseLeave"
>;

type Props = ExposedBadgeContainerProps & {
  className?: string;
  getBackgroundColor: GetColor;
  getColor: GetColor;
  isLoading?: boolean;
  leftIcon?: IconComponent;
  rightIcon?: IconComponent;
  value?: BadgeValue | null;
};

type ValueTextStyledProps = {
  $getColor: GetColor;
};

const getNoValueColor = getColorByAlias("textOnCategoryGray1");

const getNoValueBackgroundColor = getColorByAlias("bgCategoryGray1");

const ValueText = styled.span<ValueTextStyledProps>`
  ${getTypography("latest", "bodyS")}

  color: ${props => props.$getColor};
  white-space: nowrap;
`;

const renderIcon = (icon: IconComponent, getColor: GetColor) => (
  <IconWithColor {...{getColor, icon}} size={16} weight="regular" />
);

const renderRegularContent = (
  {getColor, leftIcon, rightIcon}: Props,
  value?: BadgeValue
) => (
  <>
    {leftIcon && renderIcon(leftIcon, getColor)}
    {value !== undefined && <ValueText $getColor={getColor}>{value}</ValueText>}
    {rightIcon && renderIcon(rightIcon, getColor)}
  </>
);

const renderNoValueContent = () => (
  <ValueText $getColor={getNoValueColor}>
    {formatOptionalValue(undefined)}
  </ValueText>
);

const renderContent = (props: Props) =>
  props.value === null
    ? renderNoValueContent()
    : renderRegularContent(props, props.value);

const getBadgeContainerProps = ({
  className,
  getBackgroundColor,
  isLoading,
  onMouseEnter,
  onMouseLeave,
  value
}: Props): BadgeContainerProps => ({
  className,
  $getBackgroundColor:
    value === null ? getNoValueBackgroundColor : getBackgroundColor,
  $isLoading: isLoading,
  onMouseEnter,
  onMouseLeave
});

const Badge = forwardRef<BadgeContainerElement, Props>((props, ref) => (
  <BadgeContainer {...{ref}} {...getBadgeContainerProps(props)}>
    {!props.isLoading && renderContent(props)}
  </BadgeContainer>
));

export {Badge};
