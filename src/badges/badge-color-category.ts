import * as withString from "@unlockre/utils-string/dist";

const badgeColorCategories = {
  blue: "blue",
  gray: "gray",
  green: "green",
  lavender: "lavender",
  orange: "orange",
  red: "red",
  yellow: "yellow"
} as const;

type BadgeColorCategory =
  (typeof badgeColorCategories)[keyof typeof badgeColorCategories];

const getColorPrefix = (badgeColorCategory: BadgeColorCategory) =>
  withString.concat([
    "textOnCategory",
    withString.capitalize(badgeColorCategory)
  ]);

const getBackgroundColorPrefix = (badgeColorCategory: BadgeColorCategory) =>
  withString.concat(["bgCategory", withString.capitalize(badgeColorCategory)]);

export {badgeColorCategories, getColorPrefix, getBackgroundColorPrefix};
export type {BadgeColorCategory};
