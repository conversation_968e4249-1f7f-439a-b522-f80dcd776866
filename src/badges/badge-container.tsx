import type {ComponentProps} from "react";
import styled, {css, keyframes} from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

// We need to type and export these, because neither ComponentRef nor
// ComponentProps work when using them on a styled component with styled props

type BadgeContainerElement = HTMLDivElement;

type StyledProps = {
  $getBackgroundColor: GetColor;
  $isLoading?: boolean;
};

type BadgeContainerProps = Omit<ComponentProps<"div">, "key" | "ref"> &
  StyledProps;

const containerLoadingKeyframes = keyframes`
0% {
  background-position: 200% 0;
}
100% {
  background-position: -200% 0;
}
`;

const containerLoadingCss = css`
  animation: ${containerLoadingKeyframes} 1.5s linear infinite;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.06),
    rgba(0, 0, 0, 0.15),
    rgba(0, 0, 0, 0.06)
  );
  background-size: 200% 100%;
  overflow: hidden;
  position: relative;
`;

const height = 22;

const BadgeContainer = styled.div<StyledProps>`
  align-items: center;
  background: ${props => props.$getBackgroundColor};
  border-radius: ${height / 2}px;
  display: inline-flex;
  justify-content: center;
  gap: 2px;
  height: ${height}px;
  padding: 0 6px;

  ${props => props.$isLoading && containerLoadingCss}
`;

export {BadgeContainer};
export type {BadgeContainerElement, BadgeContainerProps};
