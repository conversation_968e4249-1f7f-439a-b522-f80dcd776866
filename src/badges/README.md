# badges

The following components can be used to render badges:

- [CategoryBadge](#CategoryBadge)
- [Badge](#StatusBadge) (not yet implemented)

## CategoryBadge

### About

This component renders a badge that can contain a number, a string, or an em dash when no value is provided and can render icons on the left and right side.

The badge colors are determined by color shades ranging from `1` (most subtle) to `7` (highest contrast) and the following color categories:

- `blue`
- `gray`
- `green`
- `lavender`
- `orange`
- `red`
- `yellow`

### Example

```tsx
import {CategoryBadge} from "@unlockre/components-library/dist/badges";

const SomeComponent = () => (
  <CategoryBadge
    colorCategory="blue"
    colorShade={3}
    value="Some Value"
  />
);
```