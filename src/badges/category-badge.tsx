import * as withString from "@unlockre/utils-string/dist";
import {forwardRef} from "react";
import type {ComponentProps, ComponentRef} from "react";

import {getColorByAlias} from "@/theme-provider/theme";

import {Badge} from "./badge";
import * as withBadgeColorCategory from "./badge-color-category";
import type {BadgeColorCategory} from "./badge-color-category";
import * as withBadgeColorShade from "./badge-color-shade";
import type {BadgeColorShade} from "./badge-color-shade";

type BadgeElement = ComponentRef<typeof Badge>;

type BadgeProps = ComponentProps<typeof Badge>;

type ExposedBadgeProps = Omit<BadgeProps, "getBackgroundColor" | "getColor">;

type Props = ExposedBadgeProps & {
  colorCategory: BadgeColorCategory;
  colorShade: BadgeColorShade;
};

const getColorName = (
  badgeColorCategory: BadgeColorCategory,
  badgeColorShade: BadgeColorShade
) =>
  withString.concat([
    withBadgeColorCategory.getColorPrefix(badgeColorCategory),
    withBadgeColorShade.toString(badgeColorShade)
  ]);

const getColorGetter = (
  badgeColorCategory: BadgeColorCategory,
  badgeColorShade: BadgeColorShade
) => getColorByAlias(getColorName(badgeColorCategory, badgeColorShade));

const getBackgroundColorName = (
  badgeColorCategory: BadgeColorCategory,
  badgeColorShade: BadgeColorShade
) =>
  withString.concat([
    withBadgeColorCategory.getBackgroundColorPrefix(badgeColorCategory),
    withBadgeColorShade.toString(badgeColorShade)
  ]);

const getBackgroundColorGetter = (
  badgeColorCategory: BadgeColorCategory,
  badgeColorShade: BadgeColorShade
) =>
  getColorByAlias(getBackgroundColorName(badgeColorCategory, badgeColorShade));

const CategoryBadge = forwardRef<BadgeElement, Props>(
  ({colorCategory, colorShade, ...rest}, ref) => (
    <Badge
      {...{ref}}
      {...rest}
      getBackgroundColor={getBackgroundColorGetter(colorCategory, colorShade)}
      getColor={getColorGetter(colorCategory, colorShade)}
    />
  )
);

export {CategoryBadge};
