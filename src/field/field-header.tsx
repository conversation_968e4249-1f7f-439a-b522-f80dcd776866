import type {ComponentProps, ReactElement} from "react";
import styled from "styled-components";

import {fieldClassNames} from "./class-names";
import FieldLabel from "./field-label";

type FieldLabelProps = ComponentProps<typeof FieldLabel>;

type ExposedFieldLabelProps = Omit<FieldLabelProps, "children">;

type Props = ExposedFieldLabelProps & {
  headerRight?: ReactElement;
  label?: FieldLabelProps["children"];
};

const Container = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
`;

const render = ({className, headerRight, label, ...fieldLabelProps}: Props) => (
  <Container {...{className}}>
    <FieldLabel {...fieldLabelProps} className={fieldClassNames.header}>
      {label}
    </FieldLabel>
    {headerRight}
  </Container>
);

const FieldHeader = (props: Props) =>
  props.headerRight || props.label ? render(props) : null;

export default FieldHeader;
