import {forwardRef} from "react";
import type {ReactElement} from "react";
import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type ButtonRefElement = HTMLButtonElement;

type Props = {
  icon?: ReactElement;
  isDisabled?: boolean;
  label: string;
  onClick: () => unknown;
};

type LabelTextStyledProps = {
  $hasLeftIcon?: boolean;
  $isDisabled?: boolean;
};

const LabelText = styled.span<LabelTextStyledProps>`
  color: ${props =>
    props.$isDisabled
      ? getColorByAlias("textDisabled")
      : getColorByAlias("accentSecondary")};
  cursor: ${props => (props.$isDisabled ? "default" : "pointer")};
  font-family: "Inter Variable";
  font-size: 12px;
  font-weight: 600;
  line-height: 15px;
`;

const Container = styled(UnstyledButton)`
  align-items: center;
  display: flex;
  padding: 2px 0;
`;

const FieldActionButton = forwardRef<ButtonRefElement, Props>(
  ({icon, isDisabled, label, onClick}, ref) => (
    <Container {...{ref}} {...{onClick}} disabled={isDisabled} type="button">
      {icon}
      <LabelText $hasLeftIcon={icon !== undefined} $isDisabled={isDisabled}>
        {label}
      </LabelText>
    </Container>
  )
);

export default FieldActionButton;
