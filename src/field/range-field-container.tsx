import type {ReactElement} from "react";
import styled, {css} from "styled-components";

import {rangeFieldLayouts} from "@/field/range-field-layout";
import type {RangeFieldLayout} from "@/field/range-field-layout";
import {getColor} from "@/theme-provider/theme";

type Props = {
  className?: string;
  fieldHeight: number;
  fromField: ReactElement;
  layout?: RangeFieldLayout;
  toField: ReactElement;
};

type RangeFieldGroupProps = {
  $layout?: RangeFieldLayout;
};

const Container = styled.div``;

const verticalRangeFieldGroupCss = css`
  flex-direction: column;
  gap: 20px;
`;

const horizontalRangeFieldGroupCss = css`
  align-items: end;
`;

const RangeFieldGroup = styled.div<RangeFieldGroupProps>`
  display: flex;

  ${props =>
    props.$layout === rangeFieldLayouts.vertical
      ? verticalRangeFieldGroupCss
      : horizontalRangeFieldGroupCss}
`;

const SeparatorLineContainer = styled.div`
  display: flex;
  align-items: center;
  align-self: end;
`;

const SeparatorLine = styled.div`
  margin: 0px 8px 4px;
  height: 1px;
  width: 5px;
  background: ${getColor("gray", "900")};
`;

const RangeFieldContainer = ({
  fieldHeight,
  fromField,
  layout,
  toField,
  ...rest
}: Props) => (
  <Container {...rest}>
    <RangeFieldGroup $layout={layout}>
      {fromField}
      {layout !== rangeFieldLayouts.vertical && (
        <SeparatorLineContainer style={{height: fieldHeight}}>
          <SeparatorLine />
        </SeparatorLineContainer>
      )}
      {toField}
    </RangeFieldGroup>
  </Container>
);

export default RangeFieldContainer;
