import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

type Props = {
  className?: string;
};

const StyledPath = styled.path`
  fill: ${getColorByAlias("feedbackError")};
`;

const FieldErrorIcon = (props: Props) => (
  <svg
    fill="none"
    height="16"
    viewBox="0 0 16 16"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <StyledPath d="M7.33331 10.0002H8.66665V11.3335H7.33331V10.0002ZM7.33331 4.66683H8.66665V8.66683H7.33331V4.66683ZM7.99331 1.3335C4.31331 1.3335 1.33331 4.32016 1.33331 8.00016C1.33331 11.6802 4.31331 14.6668 7.99331 14.6668C11.68 14.6668 14.6666 11.6802 14.6666 8.00016C14.6666 4.32016 11.68 1.3335 7.99331 1.3335ZM7.99998 13.3335C5.05331 13.3335 2.66665 10.9468 2.66665 8.00016C2.66665 5.0535 5.05331 2.66683 7.99998 2.66683C10.9466 2.66683 13.3333 5.0535 13.3333 8.00016C13.3333 10.9468 10.9466 13.3335 7.99998 13.3335Z" />
  </svg>
);

export default FieldErrorIcon;
