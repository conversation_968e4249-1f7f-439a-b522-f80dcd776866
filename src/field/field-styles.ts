import {css} from "styled-components";
import type {StyledProps as PropsWithTheme} from "styled-components";

import * as withTheme from "@/theme-provider/theme";

type FieldBoxTextStyledProps = {
  disabled?: boolean;
};

const getErrorColor = withTheme.getColorByAlias("feedbackError");

const getFocusedColor = withTheme.getColorByAlias("accentPrimary");

const getNormalTextColor = withTheme.getColorByAlias("textPrimary");

const getDisabledColor = withTheme.getColorByAlias("textDisabled");

const fieldPlaceholderCss = css`
  ${withTheme.getTypography("body", "s")}

  color: ${getDisabledColor};
`;

const getFieldBoxTextColor = <
  TProps extends PropsWithTheme<FieldBoxTextStyledProps>
>(
  props: TProps
) => (props.disabled ? getDisabledColor(props) : getNormalTextColor(props));

const fieldBoxTextCss = css<FieldBoxTextStyledProps>`
  ${withTheme.getTypography("body", "s")}

  color: ${getFieldBoxTextColor};
`;

export {
  getDisabledColor,
  getErrorColor,
  getFocusedColor,
  getNormalTextColor,
  fieldBoxTextCss,
  fieldPlaceholderCss
};
