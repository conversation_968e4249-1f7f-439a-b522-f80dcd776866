import styled, {css} from "styled-components";
import type {
  StyledProps as PropsWithTheme,
  StyledComponentProps
} from "styled-components";

import {getErrorColor, getFocusedColor} from "@/field/field-styles";
import * as withTheme from "@/theme-provider/theme";
import type {Theme} from "@/theme-provider/theme";

// This should be extracted using ComponentRef utility type, but unfortunately
// this technique doesn't work with styled components
type RefElement = HTMLDivElement;

type StyledProps = {
  $hasError?: boolean;
  $withoutPaddingRight?: boolean;
};

type Props = StyledComponentProps<"div", Theme, StyledProps, never>;

const paddingHorizontal = 16;

const paddingRightCss = css`
  padding-right: ${paddingHorizontal}px;
`;

const getNormalBorderColor = withTheme.getColor("gray", "250");

const getBorderColor = (props: PropsWithTheme<Props>) =>
  props.$hasError ? getErrorColor : getNormalBorderColor(props);

const FieldBoxContainer = styled.div<StyledProps>`
  ${props => !props.$withoutPaddingRight && paddingRightCss}

  align-items: center;
  border: 1px solid ${getBorderColor};
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  padding-left: ${paddingHorizontal}px;

  &:focus-within {
    border-color: ${getFocusedColor};
  }
`;

export default Object.assign(FieldBoxContainer, {paddingHorizontal});
export type {RefElement as FieldBoxContainerRefElement};
