import type {ReactElement} from "react";
import styled from "styled-components";

import FieldErrorIcon from "./field-error-icon";
import FieldMessageText from "./field-message-text";

type ContainerStyledProps = {
  $hasMessages?: boolean;
};

type Props = {
  className?: string;
  errorMessage?: string;
  footerRight?: ReactElement;
  hasError?: boolean;
  infoMessage?: string;
  isDisabled?: boolean;
};

const StyledFieldErrorIcon = styled(FieldErrorIcon)`
  margin-right: 8px;
`;

const getFieldMessage = ({errorMessage, hasError, infoMessage}: Props) =>
  hasError ? errorMessage : infoMessage;

const renderFieldMessageText = (props: Props) => (
  <FieldMessageText
    $hasError={props.hasError}
    $isDisabled={props.isDisabled}
    title={getFieldMessage(props)}
  >
    {getFieldMessage(props)}
  </FieldMessageText>
);

const FieldMessageContainer = styled.div`
  align-items: center;
  display: flex;
  flex: 1;

  /* children with text-overflow: ellipsis hack */
  min-width: 0;
`;

const renderFieldMessage = (props: Props) => (
  <FieldMessageContainer>
    {props.hasError && <StyledFieldErrorIcon />}
    {renderFieldMessageText(props)}
  </FieldMessageContainer>
);

const Container = styled.div<ContainerStyledProps>`
  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: ${props =>
    props.$hasMessages ? "space-between" : "flex-end"};
  margin-top: 4px;
`;

const hasMessages = ({errorMessage, infoMessage}: Props) =>
  errorMessage !== undefined || infoMessage !== undefined;

const render = ({className, ...rest}: Props) => (
  <Container {...{className}} $hasMessages={hasMessages(rest)}>
    {hasMessages(rest) && renderFieldMessage(rest)}
    {rest.footerRight}
  </Container>
);

const FieldFooter = (props: Props) =>
  props.footerRight || hasMessages(props) ? render(props) : null;

export default FieldFooter;
