import styled from "styled-components";

import {getTypography} from "@/theme-provider/theme";

import {fieldClassNames} from "./class-names";
import {getDisabledColor, getNormalTextColor} from "./field-styles";

type Props = {
  children?: string;
  className?: string;
  isDisabled?: boolean;
  isRequired?: boolean;
};

type LabelContainerStyledProps = {
  $isDisabled?: boolean;
};

const OptionalContainer = styled.span`
  ${getTypography("body", "xs")}

  color: ${getDisabledColor};
  margin-left: 5px;
`;

const Container = styled.div``;

const renderOptional = () => <OptionalContainer>(Optional)</OptionalContainer>;

const renderOptionalIfNeeded = ({isRequired}: Props) =>
  !isRequired && renderOptional();

const LabelContainer = styled.span<LabelContainerStyledProps>`
  ${getTypography("body", "s")}

  color: ${props =>
    props.$isDisabled ? getDisabledColor : getNormalTextColor};
`;

const renderLabel = ({children, isDisabled}: Props) => (
  <LabelContainer $isDisabled={isDisabled} className={fieldClassNames.label}>
    {children}
  </LabelContainer>
);

const FieldLabel = (props: Props) => {
  const {className} = props;
  return (
    <Container {...{className}}>
      {renderLabel(props)}
      {renderOptionalIfNeeded(props)}
    </Container>
  );
};

export default FieldLabel;
