import styled from "styled-components";
import type {
  StyledProps as PropsWithTheme,
  StyledComponentProps
} from "styled-components";

import {getTypography} from "@/theme-provider/theme";

import {
  getDisabledColor,
  getErrorColor,
  getNormalTextColor
} from "./field-styles";

type StyledProps = {
  $hasError?: boolean;
  $isDisabled?: boolean;
};

type Props = StyledComponentProps<"div", object, StyledProps, never>;

const getNonErrorColor = (props: PropsWithTheme<Props>) =>
  props.$isDisabled ? getDisabledColor(props) : getNormalTextColor(props);

const getColor = (props: PropsWithTheme<Props>) =>
  props.$hasError ? getErrorColor(props) : getNonErrorColor(props);

const FieldMessageText = styled.div<StyledProps>`
  ${getTypography("body", "xs")}

  color: ${getColor};
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

export default FieldMessageText;
