import type {ComponentProps, ReactNode} from "react";

import {fieldClassNames} from "./class-names";
import FieldFooter from "./field-footer";
import FieldHeader from "./field-header";

type FieldHeaderProps = ComponentProps<typeof FieldHeader>;

type FieldFooterProps = ComponentProps<typeof FieldFooter>;

// prettier-ignore
type Props = 
  & FieldFooterProps
  & FieldHeaderProps
  & {
    children: ReactNode;
    className?: string;
  };

const FieldContainer = ({
  children,
  className,
  hasError,
  headerRight,
  isDisabled,
  isRequired,
  label,
  ...fieldFooterProps
}: Props) => (
  <div {...{className}}>
    <FieldHeader
      {...{headerRight, isDisabled, isRequired, label}}
      className={fieldClassNames.headerContainer}
    />
    {children}
    <FieldFooter
      {...{hasError, isDisabled}}
      {...fieldFooterProps}
      className={fieldClassNames.footer}
    />
  </div>
);

export default FieldContainer;
