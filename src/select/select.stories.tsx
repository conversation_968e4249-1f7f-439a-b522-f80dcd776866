import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import Select from "./select";

type SelectProps = ComponentProps<typeof Select>;

type Args = Pick<
  SelectProps,
  "error" | "items" | "label" | "placeholder" | "value"
>;

const Default: StoryFn<Args> = args => <Select {...args} />;

const meta: Meta<Args> = {
  title: "select",
  argTypes: {
    items: {
      control: "object"
    },
    placeholder: {
      control: "text"
    },
    error: {
      control: "text"
    },
    label: {
      control: "text"
    },
    value: {
      control: "text"
    }
  },
  args: {
    items: [
      {label: "first", value: 1},
      {label: "second", value: 2},
      {label: "third", value: 3}
    ],
    placeholder: "Select an option"
  }
};

export {meta as default, Default};
