import {MenuItem, Select as MuiSelect} from "@mui/material";
import type {SelectProps} from "@mui/material";
import styled from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider";

type Item = {label: string; value: number | string};

type Props = SelectProps<Item> & {
  items: Item[];
};

const Container = styled.div`
  & .MuiInputBase-root {
    border-radius: 10px;
    border: 1px solid #bfbfbf;
    color: rgba(0, 0, 0, 0.9);
    font-size: 12px;
    height: 40px;
    line-height: 15px;
    width: 100%;
  }
`;

const Select = ({items, placeholder, ...rest}: Props) => (
  <Container>
    <MuiThemeProvider>
      <MuiSelect {...(placeholder && {displayEmpty: true})} {...rest}>
        {placeholder && (
          <MenuItem disabled value={undefined}>
            <em>{placeholder}</em>
          </MenuItem>
        )}
        {items.map(item => (
          <MenuItem key={item.value} value={item.value}>
            {item.label}
          </MenuItem>
        ))}
      </MuiSelect>
    </MuiThemeProvider>
  </Container>
);

export default Select;
