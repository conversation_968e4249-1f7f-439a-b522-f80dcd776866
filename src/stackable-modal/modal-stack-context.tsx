import {createContext, useCallback, useContext, useState} from "react";
import type {PropsWithChildren} from "react";

type ModalStackContextValue = {
  addModal: (modalId: string) => void;
  lastModalId: string;
  removeModal: (modalId: string) => void;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
const ModalStackContext = createContext<ModalStackContextValue | null>(null);

const ModalStackProvider = ({children}: PropsWithChildren<object>) => {
  const [stack, setStack] = useState<string[]>([]);

  const addModal = useCallback((modalId: string) => {
    setStack(prev =>
      prev.find(id => id === modalId) ? prev : [...prev, modalId]
    );
  }, []);

  const removeModal = useCallback((modalId: string) => {
    setStack(prev => prev.filter(id => id !== modalId));
  }, []);

  const contextValue: ModalStackContextValue = {
    lastModalId: stack[stack.length - 1],
    addModal,
    removeModal
  };

  return (
    <ModalStackContext.Provider value={contextValue}>
      {children}
    </ModalStackContext.Provider>
  );
};

const useModalStack = (): ModalStackContextValue => {
  const context = useContext(ModalStackContext);

  if (!context) {
    throw new Error("useModalStack must be used within an ModalStackProvider");
  }
  return context;
};

export {ModalStackProvider, useModalStack};
