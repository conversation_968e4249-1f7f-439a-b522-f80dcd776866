import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import {type ComponentProps, useState} from "react";
import styled from "styled-components";

import Button from "@/button";
import {getTypography} from "@/theme-provider/theme";

import {ModalStackProvider, useModalStack} from "./modal-stack-context";
import {StackableModal} from "./stackable-modal";

type ButtonProps = ComponentProps<typeof Button>;

type Args = {
  isOpen?: boolean;
};

type Props = ComponentProps<typeof StackableModal>;

const MainModal = styled(StackableModal)`
  ${getTypography("body", "s")}

  width: 400px;
  height: 125px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 16px;
`;

const ButtonGroup = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
`;

const NestedModal = styled(MainModal)`
  width: 300px;
  height: fit-content;
  align-items: flex-end;
`;

const CloseButton = ({onClick}: Pick<ButtonProps, "onClick">) => (
  <Button
    {...{onClick}}
    size="medium"
    style={{width: "fit-content"}}
    variant="primary"
  >
    Close modal
  </Button>
);

const Modal = ({isOpen, onClose}: Props) => {
  const {addModal: addModalToStack, removeModal: removeModalFromStack} =
    useModalStack();

  const [isOpenNestedModal, setIsOpenNestedModal] = useState(false);

  const openNestedModal = () => setIsOpenNestedModal(true);
  const closeNestedModal = () => setIsOpenNestedModal(false);

  const renderMainModal = () => (
    <MainModal
      {...{
        addModalToStack,
        isOpen,
        onClose,
        removeModalFromStack
      }}
    >
      This is the main modal. Close it by clicking the button below or by
      pressing &quot;Escape&quot;.
      <ButtonGroup>
        <Button onClick={openNestedModal} size="medium" variant="secondary">
          Open a nested modal
        </Button>
        <CloseButton onClick={onClose} />
      </ButtonGroup>
    </MainModal>
  );

  const renderNestedModal = () => (
    <NestedModal
      {...{addModalToStack, removeModalFromStack}}
      isOpen={isOpenNestedModal}
      onClose={closeNestedModal}
    >
      This is a nested modal. Close it by clicking the button below or by
      pressing &quot;Escape&quot;.
      <CloseButton onClick={closeNestedModal} />
    </NestedModal>
  );

  return (
    <>
      {renderMainModal()}
      {renderNestedModal()}
    </>
  );
};

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  const onClick = () => updateArgs({isOpen: true});
  const onClose = () => updateArgs({isOpen: false});

  return (
    <ModalStackProvider>
      <Button {...{onClick}} size="medium" variant="primary">
        Open modal
      </Button>
      <Modal {...{isOpen: args.isOpen, onClose}} />
    </ModalStackProvider>
  );
};

const meta: Meta<Args> = {
  title: "stackable-modal",
  argTypes: {
    isOpen: {
      control: "boolean"
    }
  },
  args: {
    isOpen: false
  }
};

export {meta as default, Default};
