import {useId} from "@unlockre/utils-react/dist";
import colorAlpha from "color-alpha";
import {useEffect, useRef} from "react";
import type {PropsWithChildren} from "react";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

import type {useModalStack} from "./modal-stack-context";

type UseModalStackReturn = Pick<
  ReturnType<typeof useModalStack>,
  "addModal" | "removeModal"
>;

type Props = {
  addModalToStack?: UseModalStackReturn["addModal"];
  className?: string;
  isOpen?: boolean;
  onClose?: () => unknown;
  onEscape?: (event: KeyboardEvent) => unknown;
  removeModalFromStack?: UseModalStackReturn["removeModal"];
};

const Dialog = styled.dialog`
  border-radius: 10px;
  border: none;
  box-shadow: 0px 4px 25px 0px
    ${props => colorAlpha(getColor("blue", "900")(props), 0.1)};

  ::backdrop {
    background: ${props => colorAlpha(getColor("gray", "1000")(props), 0.55)};
  }
`;

/**
 * @deprecated Use AlertDialog, ModalDialog, or a custom Dialog instead
 */
const StackableModal = ({
  addModalToStack,
  children,
  className,
  isOpen,
  onClose,
  onEscape,
  removeModalFromStack
}: PropsWithChildren<Props>) => {
  const modalId = useId();
  const dialogRef = useRef<HTMLDialogElement>(null);

  useEffect(() => {
    if (isOpen) {
      addModalToStack?.(modalId);
      dialogRef.current?.showModal();
      return;
    }

    removeModalFromStack?.(modalId);
  }, [isOpen, addModalToStack, removeModalFromStack, modalId]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== "Escape") {
        return;
      }

      onEscape?.(event);
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [onEscape]);

  return isOpen ? (
    <Dialog {...{className, onClose}} id={modalId} ref={dialogRef}>
      {children}
    </Dialog>
  ) : null;
};

export {StackableModal};
