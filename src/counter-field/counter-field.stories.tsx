import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import CounterField from "./counter-field";

type CounterFieldProps = ComponentProps<typeof CounterField>;

type Args = Omit<
  CounterFieldProps,
  "className" | "footerRight" | "headerRight" | "onChange"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return <CounterField {...args} onChange={value => updateArgs({value})} />;
};

Default.parameters = {
  design: {
    control: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=3451%3A34230"
  }
};

const meta: Meta<Args> = {
  title: "counter-field",
  argTypes: {
    errorMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    infoMessage: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    max: {
      control: "number"
    },
    min: {
      control: "number"
    },
    value: {
      control: "number"
    },
    withoutThousandsSeparator: {
      control: "boolean"
    },
    decimalScale: {
      control: "number"
    }
  },
  args: {
    errorMessage: "error!",
    infoMessage: "Some info",
    label: "Some label",
    value: 5
  }
};

export {meta as default, Default};
