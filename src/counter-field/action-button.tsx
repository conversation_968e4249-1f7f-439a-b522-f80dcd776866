import type {ComponentProps} from "react";

import {IconWithColor} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type IconWithColorProps = ComponentProps<typeof IconWithColor>;

type ExposedIconWithColorProps = Pick<IconWithColorProps, "icon">;

type Props = ExposedIconWithColorProps & {
  isDisabled?: boolean;
  onClick: () => unknown;
};

const ActionButton = ({isDisabled, onClick, ...rest}: Props) => (
  <UnstyledButton {...{onClick}} disabled={isDisabled}>
    <IconWithColor
      {...rest}
      getColor={
        isDisabled
          ? getColorByAlias("textDisabled")
          : getColorByAlias("textPrimary")
      }
      size={16}
      weight="regular"
    />
  </UnstyledButton>
);

export {ActionButton};
