import {Minus, Plus} from "@phosphor-icons/react";
import React from "react";
import type {ComponentProps} from "react";
import NumberFormat from "react-number-format";
import styled, {css} from "styled-components";

import {FieldBoxContainer, FieldContainer} from "@/field";
import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledInput} from "@/unstyled";

import {ActionButton} from "./action-button";

type FieldContainerProps = Omit<
  ComponentProps<typeof FieldContainer>,
  "children"
>;

type Props = FieldContainerProps & {
  decimalScale?: number;
  max?: number;
  min?: number;
  onChange: (value: number | null) => unknown;
  value: number | null;
  withoutThousandsSeparator?: boolean;
};

const hideDefaultButtonsCss = css`
  -moz-appearance: textfield;

  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &:focus {
    outline: none;
  }
`;

const StyledInput = styled(UnstyledInput)`
  border: 0;
  flex: 1;
  min-width: 0;
  text-align: center;

  &:disabled {
    color: ${getColorByAlias("textDisabled")};
  }

  ${hideDefaultButtonsCss}
`;

const StyledFieldBoxContainer = styled(FieldBoxContainer)`
  display: flex;
  height: 48px;
  justify-content: space-between;
  padding: 10px 8px;
`;

const CounterField = ({
  decimalScale,
  hasError,
  isDisabled,
  max,
  min,
  onChange,
  value,
  withoutThousandsSeparator,
  ...containerProps
}: Props) => {
  const canChangeValue = (newValue: number) =>
    (min === undefined || newValue >= min) &&
    (max === undefined || newValue <= max);

  const handleChange = (newValue: number) => {
    if (canChangeValue(newValue)) {
      onChange(newValue);
    }
  };

  const increaseValue = () => handleChange((value ?? 0) + 1);

  const decreaseValue = () => handleChange((value ?? 0) - 1);

  const onKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement> | undefined
  ) => {
    switch (event?.key) {
      case "ArrowDown":
        event.stopPropagation();
        decreaseValue();
        break;

      case "ArrowUp":
        event.stopPropagation();
        increaseValue();
        break;

      default:
        break;
    }
  };

  return (
    <FieldContainer {...containerProps} {...{hasError, isDisabled}}>
      <StyledFieldBoxContainer $hasError={hasError} $isDisabled={isDisabled}>
        <ActionButton {...{isDisabled}} icon={Minus} onClick={decreaseValue} />
        <NumberFormat
          customInput={StyledInput}
          decimalScale={decimalScale}
          isAllowed={({floatValue}) =>
            floatValue === undefined || canChangeValue(floatValue)
          }
          max={max}
          min={min}
          onKeyDown={onKeyDown}
          onValueChange={({floatValue}) => onChange(floatValue ?? null)}
          thousandSeparator={!withoutThousandsSeparator}
          type="text"
          value={value ?? ""}
        />

        <ActionButton {...{isDisabled}} icon={Plus} onClick={increaseValue} />
      </StyledFieldBoxContainer>
    </FieldContainer>
  );
};

export default CounterField;
