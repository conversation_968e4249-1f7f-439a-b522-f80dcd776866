import type {ComponentProps} from "react";
import styled from "styled-components";

import Spinner from "./spinner";

type SpinnerProps = ComponentProps<typeof Spinner>;

type ExposedSpinnerProps = Pick<SpinnerProps, "size">;

type Props = ExposedSpinnerProps & {
  className?: string;
};

const Container = styled.div`
  align-items: center;
  display: flex;
  justify-content: center;
  height: 100%;
`;

const CenteredSpinner = ({className, ...rest}: Props) => (
  <Container {...{className}}>
    <Spinner {...rest} />
  </Container>
);

export {CenteredSpinner};
