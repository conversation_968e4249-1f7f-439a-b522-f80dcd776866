import styled from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

import {spinnerVariants} from "./spinner-variant";
import type {SpinnerVariant} from "./spinner-variant";

type Props = {
  className?: string;
  size: number;
  variant?: SpinnerVariant;
};

type StopStyledProps = {
  $variant: SpinnerVariant;
};

const getStop0VariantColor = ({$variant}: StopStyledProps) => {
  const variantColors = {
    [spinnerVariants.accent]: getColorByAlias("bgAccent"),
    [spinnerVariants.blue]: getColor("blue", "100")
  };

  return variantColors[$variant];
};

const getStop100VariantColor = ({$variant}: StopStyledProps) => {
  const variantColors = {
    [spinnerVariants.accent]: getColorByAlias("bgAccentTertiary"),
    [spinnerVariants.blue]: getColorByAlias("accentPrimary")
  };

  return variantColors[$variant];
};

const StyledSvg = styled.svg`
  animation-name: rotate;
  animation-duration: 1000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const Stop100 = styled.stop<StopStyledProps>`
  stop-color: ${getStop100VariantColor};
`;

const Stop0 = styled.stop<StopStyledProps>`
  stop-color: ${getStop0VariantColor};
`;

const Spinner = ({size, variant = spinnerVariants.blue, ...rest}: Props) => (
  <StyledSvg
    fill="none"
    height={size}
    viewBox="0 0 100 100"
    width={size}
    xmlns="http://www.w3.org/2000/svg"
    {...rest}
  >
    <defs>
      <linearGradient id="gradient">
        <Stop0 $variant={variant} offset="0%" opacity="0.0001" />
        <Stop100 $variant={variant} offset="100%" />
      </linearGradient>
    </defs>

    <circle
      cx="50"
      cy="50"
      fill="none"
      opacity="1"
      r="40"
      stroke="url(#gradient)"
      strokeWidth="15"
    ></circle>
  </StyledSvg>
);

export default Spinner;
