import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import Spinner from "./spinner";
import {spinnerVariants} from "./spinner-variant";

type SpinnerProps = ComponentProps<typeof Spinner>;

type Args = Pick<SpinnerProps, "size" | "variant">;

const Default: StoryFn<Args> = args => <Spinner {...args} />;

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/T0jvDtEMM7maBpAYKJ5GUG/%E2%9B%94%EF%B8%8F-(DEPRECATED)-Keyway---Main-library?node-id=1700%3A7287"
  }
};

const meta: Meta<Args> = {
  title: "spinner",
  argTypes: {
    size: {
      control: "number"
    },
    variant: {
      control: "radio",
      options: Object.values(spinnerVariants)
    }
  },
  args: {
    size: 54
  }
};

export {meta as default, Default};
