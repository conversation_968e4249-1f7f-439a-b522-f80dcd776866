import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  className?: string;
  getColor: GetColor;
  size: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  stroke: ${props => props.$getColor};
`;

/**
 * @deprecated use IconWithColor with PencilSimple
 */
const EditIcon = ({getColor, size, ...rest}: Props) => (
  <svg {...rest} fill="none" height={size} viewBox="0 0 20 20">
    <StyledPath
      $getColor={getColor}
      d="M10 16.666h7.5M13.75 2.916a1.768 1.768 0 012.5 2.5L5.833 15.833l-3.333.833.833-3.333L13.75 2.916z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

export {EditIcon};
