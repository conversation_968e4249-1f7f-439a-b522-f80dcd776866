import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  className?: string;
  getColor: GetColor;
  size: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

const InfoIcon = ({getColor, size, ...rest}: Props) => (
  <svg {...rest} fill="none" height={size} viewBox="0 0 46 46">
    <StyledPath
      $getColor={getColor}
      clipRule="evenodd"
      d="M45.4 23.002a22.4 22.4 0 11-44.801 0 22.4 22.4 0 0144.8 0zm-19.6-11.2a2.8 2.8 0 11-5.6 0 2.8 2.8 0 015.6 0zm-5.6 8.4a2.8 2.8 0 000 5.6v8.4a2.8 2.8 0 002.8 2.8h2.8a2.8 2.8 0 000-5.6v-8.4a2.8 2.8 0 00-2.8-2.8h-2.8z"
      fillRule="evenodd"
    />
  </svg>
);

export {InfoIcon};
