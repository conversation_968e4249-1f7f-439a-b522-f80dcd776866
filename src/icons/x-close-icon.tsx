import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  size: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

/**
 * @deprecated use IconWithColor with X
 */
const XCloseIcon = ({getColor, size}: Props) => (
  <svg
    fill="none"
    height={size}
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <StyledPath
      $getColor={getColor}
      d="M15.28 14.22a.75.75 0 01-1.061 1.062l-6.22-6.22-6.219 6.22A.75.75 0 01.72 14.22l6.22-6.219-6.22-6.22A.75.75 0 111.78.722L8 6.94 14.218.72a.75.75 0 111.061 1.06l-6.22 6.22 6.22 6.22z"
    ></StyledPath>
  </svg>
);

export {XCloseIcon};
