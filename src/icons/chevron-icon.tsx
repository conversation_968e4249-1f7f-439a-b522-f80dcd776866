import styled, {css} from "styled-components";

import type {GetColorStyledProps} from "@/icons/types";
import type {GetColor} from "@/theme-provider/theme";

const chevronDirections = {
  down: "down",
  left: "left",
  right: "right",
  up: "up"
} as const;

type ChevronDirection =
  (typeof chevronDirections)[keyof typeof chevronDirections];

type StyledSvgStyledProps = {
  $direction: ChevronDirection;
};

type Props = {
  className?: string;
  direction: ChevronDirection;
  getColor: GetColor;
  height?: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

const rotationAngles = {
  [chevronDirections.down]: 0,
  [chevronDirections.left]: 90,
  [chevronDirections.right]: 270,
  [chevronDirections.up]: 180
};

// TODO: move this to utils-styling package
const getRotationCss = (chevronDirection: ChevronDirection) => {
  const rotationAngle = rotationAngles[chevronDirection];

  return rotationAngle === 0
    ? undefined
    : css`
        transform: rotate(${rotationAngle}deg);
      `;
};

const StyledSvg = styled.svg<StyledSvgStyledProps>`
  ${props => getRotationCss(props.$direction)}
`;

/**
 * @deprecated use IconWithColor with CaretDown
 */
const ChevronIcon = ({direction, getColor, ...rest}: Props) => (
  <StyledSvg {...rest} $direction={direction} viewBox="0 0 20 20">
    <StyledPath
      $getColor={getColor}
      clip-rule="evenodd"
      d="M4.29289 6.29289C4.68342 5.90237 5.31658 5.90237 5.70711 6.29289L10 10.5858L14.2929 6.29289C14.6834 5.90237 15.3166 5.90237 15.7071 6.29289C16.0976 6.68342 16.0976 7.31658 15.7071 7.70711L10.7071 12.7071C10.3166 13.0976 9.68342 13.0976 9.29289 12.7071L4.29289 7.70711C3.90237 7.31658 3.90237 6.68342 4.29289 6.29289Z"
      fill-rule="evenodd"
    />
  </StyledSvg>
);

export {ChevronIcon, chevronDirections};

export type {ChevronDirection};
