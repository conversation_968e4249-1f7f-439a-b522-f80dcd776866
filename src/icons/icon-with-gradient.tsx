import type {Icon as IconComponent} from "@phosphor-icons/react";
import {useId} from "@unlockre/utils-react/dist";
import type {ReactElement} from "react";
import {useTheme} from "styled-components";

import type {Theme} from "@/theme-provider/theme";

type RenderGradientParams = {
  gradientId: string;
  scale: (numbers: number[]) => number[];
  theme: Theme;
};

type RenderGradient = (params: RenderGradientParams) => ReactElement;

type Props = {
  children: RenderGradient;
  className?: string;
  icon: IconComponent;
  size: number;
};

const figmaPhosphorSize = 256;

const getRatio = (iconSize: number) => figmaPhosphorSize / iconSize;

const scaleUsing = (iconSize: number) => (numbers: number[]) =>
  numbers.map(number => number * getRatio(iconSize));

const IconWithGradient = ({
  children: renderGradient,
  icon: Icon,
  ...rest
}: Props) => {
  const theme = useTheme();

  const gradientId = useId();

  const scale = scaleUsing(rest.size);

  return (
    <Icon {...rest} fill={`url(#${gradientId})`} weight="fill">
      {renderGradient({gradientId, scale, theme})}
    </Icon>
  );
};

export {IconWithGradient};
