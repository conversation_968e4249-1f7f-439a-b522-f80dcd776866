import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  className?: string;
  getColor: GetColor;
  size: number;
};

const StyledSvg = styled.svg<GetColorStyledProps>`
  stroke: ${props => props.$getColor};
`;

const LoadingIcon = ({getColor, size, ...rest}: Props) => (
  <StyledSvg
    {...rest}
    $getColor={getColor}
    height={size}
    viewBox="0 0 40 40"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g fill="none" fillRule="evenodd">
      <g strokeWidth="2" transform="translate(1 1)">
        <circle cx="18" cy="18" r="18" strokeOpacity=".5" />
        <path d="M36 18c0-9.94-8.06-18-18-18">
          <animateTransform
            attributeName="transform"
            dur="1s"
            from="0 18 18"
            repeatCount="indefinite"
            to="360 18 18"
            type="rotate"
          />
        </path>
      </g>
    </g>
  </StyledSvg>
);

export {LoadingIcon};
