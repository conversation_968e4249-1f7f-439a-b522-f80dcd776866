import styled from "styled-components";

import type {GetColorStyledProps, RegularIconProps} from "./types";

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

const KeywayIcon = ({getColor, size}: RegularIconProps) => (
  <svg fill="none" height={size} viewBox="0 0 20 20">
    <StyledPath
      $getColor={getColor}
      clipRule="evenodd"
      d="M16.72 3.28L3.28 3.28L3.28 16.72L16.72 16.72L16.72 3.28ZM18 3.06667C18 2.47756 17.5224 2 16.9333 2L3.06667 2C2.47756 2 2 2.47756 2 3.06667L2 16.9333C2 17.5224 2.47756 18 3.06667 18L16.9333 18C17.5224 18 18 17.5224 18 16.9333L18 3.06667Z"
      fillRule="evenodd"
    />
    <StyledPath
      $getColor={getColor}
      clipRule="evenodd"
      d="M2.02119 16.6455L16.2935 2.37318L17.1986 3.27827L2.92629 17.5506L2.02119 16.6455Z"
      fillRule="evenodd"
    />
    <StyledPath
      $getColor={getColor}
      clipRule="evenodd"
      d="M17.702 10.6018L9.51793 10.6018V9.32176H17.7021L17.702 10.6018Z"
      fillRule="evenodd"
    />
    <StyledPath
      $getColor={getColor}
      clipRule="evenodd"
      d="M10.0005 9.44695L17.1992 16.6457L16.2941 17.5508L9.09539 10.352L10.0005 9.44695Z"
      fillRule="evenodd"
    />
  </svg>
);

export {KeywayIcon};
