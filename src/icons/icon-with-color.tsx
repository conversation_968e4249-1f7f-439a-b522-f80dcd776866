import type {
  Icon as IconComponent,
  IconProps,
  IconWeight
} from "@phosphor-icons/react";
import {forwardRef} from "react";
import {useTheme} from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

type ExposedIconProps = Pick<
  IconProps,
  "className" | "onMouseEnter" | "onMouseLeave"
>;

type Props = ExposedIconProps & {
  getColor: GetColor;
  icon: IconComponent;
  size: number;
  weight: IconWeight;
};

const IconWithColor = forwardRef<SVGSVGElement, Props>(
  ({getColor, icon: Icon, size, ...rest}, ref) => {
    const theme = useTheme();

    return (
      <Icon
        {...{ref}}
        {...rest}
        color={getColor({theme})}
        size={size}
        style={{minWidth: size, maxWidth: size}}
      />
    );
  }
);

export {IconWithColor};
