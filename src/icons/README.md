# Icons

We use [Phosphor Icons](https://phosphoricons.com) and some other custom icons as well.

## Custom Icons

- [KeywayIcon](src/icons/keyway-icon.tsx)

## Helper Components

- [IconButton](#IconButton)
- [IconWithColor](#IconWithColor)
- [IconWithGradient](#IconWithGradient)

## IconButton

### About

This component makes an icon clickable wrapping it with a button.

### Props

```ts
type IconButtonVariant = "default" | "primary" | "secondary";

type RenderIconParams = {
  getColor: GetColor;
  size: number;
};

type RenderIcon = (params: RenderIconParams) => ReactElement;

type Props = {
  className?: string;
  isDisabled?: boolean;
  onClick?: () => unknown;
  renderIcon: RenderIcon;
  size: number;
  variant: IconButtonVariant;
};
```

### Example

```tsx
import {ChartBar} from "@phosphor-icons/react";
import {IconButton, IconWithColor} from "@unlockre/components-library/dist/icons";
import {getColor} from "@unlockre/components-library/dist/theme-provider/theme";
import {ComponentProps} from "react";

type IconButtonProps = ComponentProps<typeof IconButton>;

type Props = Omit<IconButton, "icon" | "renderIcon">;

const ChatBarIconButton = (props: Props) => (
  <IconButton
    {...props}
    renderIcon={params => (
      <IconWithColor
        {...params}
        icon={ChartBar}
        weight="regular"
      />
    )}
  />
);
```

## IconWithColor

### About

This component renders a given `Phosphor Icon` with a color from the `Theme`.

> If a ref is given, this is passed to the Icon

### Props

```ts
import type {Icon as IconComponent, IconProps, IconWeight} from "@phosphor-icons/react";

import type {GetColor} from "@/theme-provider/theme";

type ExposedIconProps = Pick<
  IconProps,
  "className" | "onMouseEnter" | "onMouseLeave"
>;

type Props = ExposedIconProps & {
  getColor: GetColor;
  icon: IconComponent;
  size: number;
  weight: IconWeight;
};
```

### Example

```tsx
import {ChartBar} from "@phosphor-icons/react";
import {IconWithColor} from "@unlockre/components-library/dist/icons";
import {getColor} from "@unlockre/components-library/dist/theme-provider/theme";
import {ComponentProps} from "react";

type IconWithGradientProps = ComponentProps<typeof IconWithGradient>;

type Props = Omit<IconWithGradientProps, "getColor" | "icon">;

const ChartBarIcon = (props: Props) => (
  <IconWithColor
    {...props}
    getColor={getColor("blue", "500")}
    icon={ChartBar}
  />
);

export {ChartBarIcon};
```

## IconWithGradient

### About

This component renders a given `Phosphor Icon` with a custom gradient.

### Props

```ts
type RenderGradientParams = {
  gradientId: string;
  scale: (numbers: number[]) => number[];
  theme: Theme;
};

type RenderGradient = (params: RenderGradientParams) => ReactElement;

type Props = {
  children: RenderGradient;
  className?: string;
  icon: IconComponent;
  size: number;
};
```

### Example

```tsx
import {ChartBar} from "@phosphor-icons/react";
import {IconWithGradient} from "@unlockre/components-library/dist/icons";
import {ComponentProps} from "react";

type IconWithGradientProps = ComponentProps<typeof IconWithGradient>;

type Props = Omit<IconWithGradientProps, "children" | "icon">;

const ChartBarIcon = (props: Props) => (
  <IconWithGradient {...props} icon={ChartBar}>
    {({gradientId, scale, theme}) => (
      <defs>
        <radialGradient
          cx="0"
          cy="0"
          gradientTransform={[
            `translate(${scale([33.5773, 22.3173])})`,
            "rotate(-179.976)",
            `scale(${scale([36.1979, 670.598])})`
          ].join(" ")}
          gradientUnits="userSpaceOnUse"
          id={gradientId}
          r="1"
        >
          {/* TODO: move these colors to the theme and extract them from there */}
          <stop offset="0.0313003" stopColor="#497FF6" />
          <stop offset="0.561426" stopColor="#98ADFF" />
          <stop offset="1" stopColor="#FCFCFF" />
        </radialGradient>
      </defs>
    )}
  </IconWithGradient>;
);

export {ChartBarIcon};
```