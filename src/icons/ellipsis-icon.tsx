import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  size: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

/**
 * @deprecated use IconWithColor with DotsThree
 */
const EllipsisIcon = ({getColor, size}: Props) => (
  <svg height={size} viewBox="0 0 16 16">
    <StyledPath
      $getColor={getColor}
      d="M4 6.667c-.733 0-1.333.6-1.333 1.333S3.267 9.333 4 9.333 5.333 8.733 5.333 8 4.733 6.667 4 6.667zm8 0c-.733 0-1.333.6-1.333 1.333s.6 1.333 1.333 1.333 1.333-.6 1.333-1.333-.6-1.333-1.333-1.333zm-4 0c-.733 0-1.333.6-1.333 1.333S7.267 9.333 8 9.333 9.333 8.733 9.333 8 8.733 6.667 8 6.667z"
    />
  </svg>
);

export default EllipsisIcon;
