import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  size: number;
};

const StrokedRect = styled.rect<GetColorStyledProps>`
  stroke: ${props => props.$getColor};
`;

const StrokedPath = styled.path<GetColorStyledProps>`
  stroke: ${props => props.$getColor};
`;

const FilledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

const NNNIcon = ({getColor, size}: Props) => (
  <svg fill="none" height={size} viewBox="0 0 16 16">
    <mask fill="white" id="path-1-inside-1_2027_27688">
      <rect height="5" rx="0.6" width="5" x="5.5" y="1" />
    </mask>
    <StrokedRect
      height="5"
      mask="url(#path-1-inside-1_2027_27688)"
      rx="0.6"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      width="5"
      x="5.5"
      y="1"
    />
    <StrokedPath
      $getColor={getColor}
      d="M9.5 14V10.4C9.5 10.1791 9.32091 10 9.1 10H6.9C6.67909 10 6.5 10.1791 6.5 10.4V14"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.2"
    />
    <StrokedPath
      $getColor={getColor}
      d="M4 13.8008L4 4.76156C4 4.42874 4.11706 4.10956 4.32544 3.87422C4.53381 3.63888 4.81643 3.50666 5.11111 3.50666L5.5 3.50079M12 13.8008L12 4.76157C12 4.42874 11.8829 4.10956 11.6746 3.87422C11.4662 3.63888 11.1836 3.50666 10.8889 3.50666L10.5 3.50079"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.2"
    />
    <StrokedPath
      $getColor={getColor}
      d="M2.5 14H13.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.2"
    />
    <FilledPath
      $getColor={getColor}
      d="M8.33333 3.06706H8.66667C8.75507 3.06706 8.83986 3.10218 8.90237 3.16469C8.96488 3.2272 9 3.31199 9 3.40039C9 3.4888 8.96488 3.57358 8.90237 3.63609C8.83986 3.6986 8.75507 3.73372 8.66667 3.73372H8.33333V4.06706C8.33333 4.15546 8.29821 4.24025 8.2357 4.30276C8.17319 4.36527 8.08841 4.40039 8 4.40039C7.91159 4.40039 7.82681 4.36527 7.7643 4.30276C7.70179 4.24025 7.66667 4.15546 7.66667 4.06706V3.73372H7.33333C7.24493 3.73372 7.16014 3.6986 7.09763 3.63609C7.03512 3.57358 7 3.4888 7 3.40039C7 3.31199 7.03512 3.2272 7.09763 3.16469C7.16014 3.10218 7.24493 3.06706 7.33333 3.06706H7.66667V2.73372C7.66667 2.64532 7.70179 2.56053 7.7643 2.49802C7.82681 2.43551 7.91159 2.40039 8 2.40039C8.08841 2.40039 8.17319 2.43551 8.2357 2.49802C8.29821 2.56053 8.33333 2.64532 8.33333 2.73372V3.06706Z"
    />
  </svg>
);

export default NNNIcon;
