import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  size: number;
};

const StyledRect = styled.rect<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

/**
 * @deprecated use IconWithColor with Minus
 */
const MinusIcon = ({getColor, size}: Props) => (
  <svg
    fill="none"
    height={size}
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <StyledRect $getColor={getColor} height="2" rx="1" width="12" x="2" y="7" />
  </svg>
);

export default MinusIcon;
