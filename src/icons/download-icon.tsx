import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  className?: string;
  getColor: GetColor;
  size: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  stroke: ${props => props.$getColor};
`;

/**
 * @deprecated use IconWithColor with DownloadSimple
 */
const DownloadIcon = ({getColor, size, ...rest}: Props) => (
  <svg
    {...rest}
    fill="none"
    height={size}
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <StyledPath
      $getColor={getColor}
      d="M14 9V11.6667C14 12.0203 13.8595 12.3594 13.6095 12.6095C13.3594 12.8595 13.0203 13 12.6667 13H3.33333C2.97971 13 2.64057 12.8595 2.39052 12.6095C2.14048 12.3594 2 12.0203 2 11.6667V9"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.4"
    />
    <StyledPath
      $getColor={getColor}
      d="M4.66732 6.66667L8.00065 10L11.334 6.66667"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.4"
    />
    <StyledPath
      $getColor={getColor}
      d="M8 10L8 2"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.4"
    />
  </svg>
);

export {DownloadIcon};
