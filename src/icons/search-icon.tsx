import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  height: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

/**
 * @deprecated use IconWithColor with MagnifyingGlass
 */
const SearchIcon = ({getColor, ...rest}: Props) => (
  <svg {...rest} fill="none" viewBox="0 0 20 21">
    <StyledPath
      $getColor={getColor}
      d="M13.4351 12.5629H12.7124L12.4563 12.3159C13.3528 11.273 13.8925 9.9191 13.8925 8.44625C13.8925 5.16209 11.2304 2.5 7.94625 2.5C4.66209 2.5 2 5.16209 2 8.44625C2 11.7304 4.66209 14.3925 7.94625 14.3925C9.4191 14.3925 10.773 13.8528 11.8159 12.9563L12.0629 13.2124V13.9351L16.6369 18.5L18 17.1369L13.4351 12.5629ZM7.94625 12.5629C5.66838 12.5629 3.82962 10.7241 3.82962 8.44625C3.82962 6.16838 5.66838 4.32962 7.94625 4.32962C10.2241 4.32962 12.0629 6.16838 12.0629 8.44625C12.0629 10.7241 10.2241 12.5629 7.94625 12.5629Z"
    />
  </svg>
);

export default SearchIcon;
