import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  size: number;
};

const FilledStrokedPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
  stroke: ${props => props.$getColor};
`;

const StrokedPath = styled.path<GetColorStyledProps>`
  stroke: ${props => props.$getColor};
`;

const CloseIcon = ({getColor, size}: Props) => (
  <svg fill="none" height={size} viewBox="0 0 16 16">
    <g clipPath="url(#clip0_2227_2973)">
      <StrokedPath
        $getColor={getColor}
        d="M7.99967 14.6673C11.6816 14.6673 14.6663 11.6825 14.6663 8.00065C14.6663 4.31875 11.6816 1.33398 7.99967 1.33398C4.31778 1.33398 1.33301 4.31875 1.33301 8.00065C1.33301 11.6825 4.31778 14.6673 7.99967 14.6673Z"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.4"
      />
      <FilledStrokedPath
        $getColor={getColor}
        d="M11.1061 5.71009L11.2121 5.60403L11.1061 5.49796L10.502 4.89393L10.396 4.78787L10.2899 4.89393L8 7.18384L5.71009 4.89393L5.60403 4.78787L5.49796 4.89393L4.89393 5.49796L4.78787 5.60403L4.89393 5.71009L7.18384 8L4.89393 10.2899L4.78787 10.396L4.89393 10.502L5.49796 11.1061L5.60403 11.2121L5.71009 11.1061L8 8.81616L10.2899 11.1061L10.396 11.2121L10.502 11.1061L11.1061 10.502L11.2121 10.396L11.1061 10.2899L8.81616 8L11.1061 5.71009Z"
        strokeWidth="0.3"
      />
    </g>
    <defs>
      <clipPath id="clip0_2227_2973">
        <rect fill="transparent" height="16" width="16" />
      </clipPath>
    </defs>
  </svg>
);

export default CloseIcon;
