import styled from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import type {GetColorStyledProps} from "./types";

type Props = {
  getColor: GetColor;
  size: number;
};

const StyledPath = styled.path<GetColorStyledProps>`
  fill: ${props => props.$getColor};
`;

const MultifamilyIcon = ({getColor, size}: Props) => (
  <svg fill="none" height={size} viewBox="0 0 16 16">
    <StyledPath
      $getColor={getColor}
      d="M7.99992 4.66667V2H1.33325V14H14.6666V4.66667H7.99992ZM3.99992 12.6667H2.66659V11.3333H3.99992V12.6667ZM3.99992 10H2.66659V8.66667H3.99992V10ZM3.99992 7.33333H2.66659V6H3.99992V7.33333ZM3.99992 4.66667H2.66659V3.33333H3.99992V4.66667ZM6.66659 12.6667H5.33325V11.3333H6.66659V12.6667ZM6.66659 10H5.33325V8.66667H6.66659V10ZM6.66659 7.33333H5.33325V6H6.66659V7.33333ZM6.66659 4.66667H5.33325V3.33333H6.66659V4.66667ZM13.3333 12.6667H7.99992V11.3333H9.33325V10H7.99992V8.66667H9.33325V7.33333H7.99992V6H13.3333V12.6667ZM11.9999 7.33333H10.6666V8.66667H11.9999V7.33333ZM11.9999 10H10.6666V11.3333H11.9999V10Z"
    />
  </svg>
);

export default MultifamilyIcon;
