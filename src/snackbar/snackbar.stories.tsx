import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import type {ComponentProps} from "react";

import Button from "@/button";

import Snackbar from "./snackbar";

type SnackbarProps = ComponentProps<typeof Snackbar>;

type Args = Pick<
  SnackbarProps,
  "actionLabel" | "keepOpen" | "message" | "variant"
>;

const Default: StoryFn<Args> = args => {
  const [isOpened, setIsOpened] = useState(false);

  return (
    <>
      <Button onClick={() => setIsOpened(true)} size="small" variant="primary">
        Show!
      </Button>
      {isOpened && <Snackbar {...args} onClose={() => setIsOpened(false)} />}
    </>
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/T0jvDtEMM7maBpAYKJ5GUG/Keyway---Main-library?node-id=835%3A3485"
  }
};

const meta: Meta<Args> = {
  title: "snackbar",
  argTypes: {
    actionLabel: {
      control: "text"
    },
    keepOpen: {
      control: "boolean"
    },
    message: {
      control: "text"
    },
    variant: {
      control: "select",
      options: ["success", "error"]
    }
  },
  args: {
    actionLabel: "close",
    message: "This is a message!",
    variant: "success"
  }
};

export {meta as default, Default};
