import {useSetTimeout} from "@unlockre/utils-react/dist";
import styled from "styled-components";

import Portal from "@/portal";
import {getColor, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type Variant = "error" | "success";

type Props = {
  actionLabel?: string;
  keepOpen?: boolean;
  message: string;
  onClose: () => unknown;
  variant: Variant;
};

type ContainerStyledProps = {
  $hasActions: boolean;
  $variant: Variant;
};

const variantColors = {
  error: getColor("red", "500"),
  success: getColor("green", "500")
};

const Container = styled.div<ContainerStyledProps>`
  ${getTypography("body", "m")};

  background: ${props => variantColors[props.$variant]};
  border-radius: 6px;
  bottom: 40px;
  color: white;
  display: flex;
  justify-content: ${props => (props.$hasActions ? "space-between" : "center")};
  left: 50%;
  min-width: 395px;
  padding: 16px 24px;
  position: fixed;
  transform: translate(-50%);
  z-index: 1;
`;

const ActionButton = styled(UnstyledButton)`
  ${getTypography("body", "m", 600)};

  color: ${getColor("gray", "000")};
  text-transform: uppercase;
`;

const Snackbar = ({
  actionLabel,
  keepOpen = false,
  message,
  onClose,
  variant
}: Props) => {
  useSetTimeout(!keepOpen, 5000, onClose);

  return (
    <Portal>
      <Container
        $hasActions={Boolean(actionLabel)}
        $variant={variant}
        role="status"
      >
        <span>{message}</span>
        {actionLabel && (
          <ActionButton onClick={onClose}>{actionLabel}</ActionButton>
        )}
      </Container>
    </Portal>
  );
};

export default Snackbar;

export type {Variant as SnackbarVariant};
