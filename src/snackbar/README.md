# Snackbar

## About

This component renders a message appearing from the bottom of the screen with
different color variants that could dissapear automatically or not
(see `keepOpen` prop) after some time.

### Types

```tsx

type SnackbarVariant = "error" | "success";

type SnackbarProps = {
  actionLabel?: string;
  keepOpen?: boolean;
  message: string;
  onClose: () => unknown;
  variant: Variant;
};

```

### Example using setSnackbarState

```tsx
import Button from "@unlockre/components-library/dist/button";
import Snackbar, {useSnackbar} from "@unlockre/components-library/dist/snackbar";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

const MyApp = () => {
  const {isSnackbarVisible, snackbarProps, setSnackbarState} = useSnackbar();

  const showSuccessSnackbar = () => setSnackbarState({message: "A Message", variant: "success"})

  return (
    <ThemeProvider>
      <Button onClick={showSuccessSnackbar} size="small" variant="primary">
        Show success snackbar!
      </Button>
      {isSnackbarVisible && <Snackbar {...snackbarProps} />}
    </ThemeProvider>
  );
};

export default MyApp;
```

### Example using showSnackbarOn

```tsx
import Button from "@unlockre/components-library/dist/button";
import Snackbar, {useSnackbar} from "@unlockre/components-library/dist/snackbar";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

const MyApp = () => {
  const {isSnackbarVisible, showSnackbarOn, snackbarProps} = useSnackbar();

  const onClick = () => {
    const aPromise = axios.fetch("/some/endpoint");

    showSnackbarOn(aPromise, "A success message", "An error message");
  }

  return (
    <ThemeProvider>
      <Button {...{onClick}} size="small" variant="primary">
        Show success snackbar!
      </Button>
      {isSnackbarVisible && <Snackbar {...snackbarProps} />}
    </ThemeProvider>
  );
};

export default MyApp;
```