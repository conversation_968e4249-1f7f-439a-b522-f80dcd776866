import {useState} from "react";
import type {ComponentProps, Dispatch, SetStateAction} from "react";

import type Snackbar from "./snackbar";

type SnackbarProps = ComponentProps<typeof Snackbar>;

type SnackbarState = Pick<SnackbarProps, "message" | "variant">;

type ResultSnackbarProps = Pick<
  SnackbarProps,
  "message" | "onClose" | "variant"
>;

type ShowSnackbarOn = (
  promise: Promise<unknown>,
  successMessage: string,
  errorMessage: string
) => Promise<void>;

type SetSnackbarState = Dispatch<SetStateAction<SnackbarState | undefined>>;

type ResultWith<TIsSnackbarVisible extends boolean> = {
  isSnackbarVisible: TIsSnackbarVisible;
  setSnackbarState: SetSnackbarState;
  showSnackbarOn: ShowSnackbarOn;
  snackbarProps: TIsSnackbarVisible extends true
    ? ResultSnackbarProps
    : undefined;
};

type VisibleResult = ResultWith<true>;

type HiddenResult = ResultWith<false>;

type Result = HiddenResult | VisibleResult;

const useSnackbar = (): Result => {
  const [snackbarState, setSnackbarState] = useState<SnackbarState | undefined>(
    undefined
  );

  const onClose = () => setSnackbarState(undefined);

  const isSnackbarVisible = snackbarState !== undefined;

  const snackbarProps = {
    ...snackbarState,
    onClose
  };

  const showSnackbarOn = (
    promise: Promise<unknown>,
    successMessage: string,
    errorMessage: string
  ) =>
    promise
      .then(() =>
        setSnackbarState({message: successMessage, variant: "success"})
      )
      .catch(() => setSnackbarState({message: errorMessage, variant: "error"}));

  return {
    isSnackbarVisible,
    setSnackbarState,
    showSnackbarOn,
    snackbarProps
  } as Result;
};

export default useSnackbar;

export type {ShowSnackbarOn};
