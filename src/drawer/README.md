# Drawer

## About

This component provides a panel that appears from the left or right of the screen on top of other components, containing information and/or actions to perform.

## Example

```tsx
import {Button} from "@unlockre/components-library/dist/buttons";
import {Drawer, DrawerFooter} from "@unlockre/components-library/dist/drawer";
import styled from "styled-components";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {useState} from "react";

const StyledButton = styled(Button)`
  width: 100%;
`;

const MyApp = () => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <ThemeProvider>
      <Button
        onClick={() => setIsVisible(true)}
        size="medium"
        type="button"
        variant="primary"
      >
        Open Drawer
      </Button>
      {isVisible && (
        <Drawer
          {...args}
          onClose={() => setIsVisible(false)}
        >
          <p>Content...</p>
          <p>Content...</p>
          <DrawerFooter>
            <StyledButton
              onClick={() => console.log("Apply Filters")}
              size="large"
              type="button"
              variant="primary"
            >
              Apply
            </StyledButton>
          </DrawerFooter>
        </Drawer>
      )}
    </ThemeProvider>
  );
};

export {MyApp};
```
