import {X} from "@phosphor-icons/react";
import styled from "styled-components";

import {IconWithColor} from "@/icons";
import * as withTheme from "@/theme-provider/theme";
import {UnstyledButton, UnstyledHeading} from "@/unstyled";

type Props = {
  onClose: () => unknown;
  subtitle?: string;
  title?: string;
};

const CloseDrawerButton = styled(UnstyledButton)`
  margin-left: auto;
  padding: 6px;
`;

const Container = styled.div`
  align-items: start;
  display: flex;
  justify-content: space-between;
  padding: 32px 32px 16px;
`;

const Header = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const Title = styled(UnstyledHeading)`
  color: ${withTheme.getColorByAlias("text")};
  ${withTheme.getTypography("latest", "titleS")}
`;

const Subtitle = styled.div`
  color: ${withTheme.getColorByAlias("text")};
  ${withTheme.getTypography("latest", "bodyM")}
`;

const DrawerHeader = ({onClose, subtitle, title}: Props) => (
  <Container>
    {subtitle ? (
      <Header>
        {title !== undefined && <Title level={2}>{title}</Title>}
        <Subtitle>{subtitle}</Subtitle>
      </Header>
    ) : (
      title !== undefined && <Title level={2}>{title}</Title>
    )}
    <CloseDrawerButton onClick={onClose}>
      <IconWithColor
        getColor={withTheme.getColorByAlias("iconAccent")}
        icon={X}
        size={20}
        weight="bold"
      />
    </CloseDrawerButton>
  </Container>
);

export {DrawerHeader};
