import {action} from "@storybook/addon-actions";
import type {<PERSON><PERSON>, StoryFn} from "@storybook/react";
import {useState} from "react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {Button} from "@/buttons";

import {Drawer} from "./drawer";
import {DrawerBody} from "./drawer-body";
import {DrawerFooter} from "./drawer-footer";
import {drawerPositions} from "./drawer-position";

type DrawerProps = ComponentProps<typeof Drawer>;

type StoryArgs = Pick<
  DrawerProps,
  "noBackdrop" | "position" | "subtitle" | "title"
>;

const applyAction = action("apply");

const StyledDrawer = styled(Drawer)`
  width: 316px;
`;

const StyledButton = styled(Button)`
  width: 100%;
`;

const StyledDrawerBody = styled(DrawerBody)`
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 0 32px;
`;

const DrawerActions = () => (
  <DrawerFooter>
    <StyledButton
      onClick={applyAction}
      size="large"
      type="button"
      variant="primary"
    >
      Apply
    </StyledButton>
  </DrawerFooter>
);

const Default: StoryFn<StoryArgs> = args => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsVisible(true)}
        size="medium"
        type="button"
        variant="primary"
      >
        Open Drawer
      </Button>
      {isVisible && (
        <StyledDrawer {...args} onClose={() => setIsVisible(false)}>
          <StyledDrawerBody>
            <p>Content...</p>
            <p>Content...</p>
          </StyledDrawerBody>
          <DrawerActions />
        </StyledDrawer>
      )}
    </>
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/C3waJMAyXf5Lt1pqOkHZq0/Admin?node-id=4841%3A212310"
  }
};

const meta: Meta<StoryArgs> = {
  title: "drawer",
  argTypes: {
    position: {
      options: Object.values(drawerPositions),
      control: "select"
    },
    noBackdrop: {
      control: "boolean"
    },
    title: {
      control: "text"
    }
  },
  args: {
    position: "left",
    noBackdrop: false,
    title: "Deals Tasks Filters",
    subtitle: "Detail deals tasks filters"
  }
};

export {meta as default, Default};
