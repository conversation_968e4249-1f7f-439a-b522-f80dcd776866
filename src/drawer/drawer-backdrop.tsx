import colorAlpha from "color-alpha";
import type {MouseEvent} from "react";
import styled, {keyframes} from "styled-components";

import {getColor} from "@/theme-provider/theme";

type Props = {
  isClosing?: boolean;
  onClose: () => unknown;
};

type BackdropStyledProps = {
  $isClosing?: boolean;
};

const fadeInAnimation = keyframes`
  from {
    opacity: 0
  }
  to {
    opacity: 1
  }
`;

const fadeOutAnimation = keyframes`
  from {
    opacity: 1
  }
  to {
    opacity: 0
  }
`;

const Backdrop = styled.div<BackdropStyledProps>`
  align-items: center;
  background-color: ${props =>
    colorAlpha(getColor("gray", "1000")(props), 0.55)};
  bottom: 0;
  display: flex;
  justify-content: center;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1;
  transition: opacity 2s ease-in;

  animation: ${props =>
      !props.$isClosing ? fadeInAnimation : fadeOutAnimation}
    0.2s linear;
`;

const DrawerBackdrop = ({isClosing, onClose}: Props) => {
  const onClickOutsideModal = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  return <Backdrop $isClosing={isClosing} onClick={onClickOutsideModal} />;
};

export {DrawerBackdrop};
