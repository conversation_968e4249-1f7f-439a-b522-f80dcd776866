import colorAlpha from "color-alpha";
import {useState} from "react";
import type {ReactNode} from "react";
import styled, {css, keyframes} from "styled-components";

import Portal from "@/portal";
import * as withTheme from "@/theme-provider/theme";
import {getColor} from "@/theme-provider/theme";

import {DrawerBackdrop} from "./drawer-backdrop";
import {DrawerHeader} from "./drawer-header";
import type {DrawerPosition} from "./drawer-position";

type Props = {
  children: ReactNode;
  className?: string;
  noBackdrop?: boolean;
  onClose: () => unknown;
  position: DrawerPosition;
  subtitle?: string;
  title?: string;
};

type ContainerStyledProps = {
  $isClosing?: boolean;
  $position: DrawerPosition;
};

const leftToRightShowAnimation = keyframes`
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const rightToLeftShowAnimation = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const rightToLeftHideAnimation = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
`;

const leftToRightHideAnimation = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
`;

const showAnimations = {
  left: leftToRightShowAnimation,
  right: rightToLeftShowAnimation
};

const hideAnimations = {
  left: rightToLeftHideAnimation,
  right: leftToRightHideAnimation
};

const rightPositionCss = css`
  right: 0;
`;

const leftPositionCss = css`
  left: 0;
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => (props.$position === "left" ? leftPositionCss : rightPositionCss)}

  animation: ${props =>
    !props.$isClosing
      ? showAnimations[props.$position]
      : hideAnimations[props.$position]}
    0.2s ease-out;
  animation-fill-mode: both;
  background: ${withTheme.getColorByAlias("bg")};
  bottom: 0;
  box-shadow: 0px 4px 28px 0px
    ${props => colorAlpha(getColor("gray", "900")(props), 0.06)};
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
`;

const Drawer = ({
  children,
  className,
  noBackdrop,
  onClose,
  position,
  subtitle,
  title
}: Props) => {
  const [isClosing, setIsClosing] = useState(false);

  return (
    <Portal>
      {!noBackdrop && (
        <DrawerBackdrop {...{isClosing}} onClose={() => setIsClosing(true)} />
      )}
      <Container
        {...{className}}
        $isClosing={isClosing}
        $position={position}
        onAnimationEnd={() => {
          if (isClosing) {
            onClose();
          }
        }}
      >
        <DrawerHeader
          {...{subtitle, title}}
          onClose={() => {
            setIsClosing(true);
          }}
        />
        {children}
      </Container>
    </Portal>
  );
};

export {Drawer};
export type {DrawerPosition};
