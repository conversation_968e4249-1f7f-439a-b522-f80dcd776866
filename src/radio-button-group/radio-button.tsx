import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import RadioButtonCircle from "./radio-button-circle";
import type {Size} from "./types";

type LabelStyledProps = {
  $size: Size;
};

type Props = {
  hasError?: boolean;
  isDisabled?: boolean;
  isSelected?: boolean;
  label: string;
  onChange?: () => unknown;
  size: Size;
};

const getMediumLabelTypography = getTypography("body", "m");

const getSmallLabelTypography = getTypography("body", "s");

const Label = styled.span<LabelStyledProps>`
  color: ${getColorByAlias("textPrimary")};
  ${props =>
    props.$size === "small"
      ? getSmallLabelTypography
      : getMediumLabelTypography}
`;

const Container = styled(UnstyledButton)`
  display: flex;
  gap: 12px;
  align-items: center;
`;

const RadioButton = ({label, onChange, size, ...rest}: Props) => (
  <Container onClick={onChange} type="button">
    <RadioButtonCircle {...rest} {...{size}} />
    <Label $size={size}>{label}</Label>
  </Container>
);

export default RadioButton;

export type {Props as RadioButtonProps};
