# RadioButtonGroup

## About

This component renders a group of buttons from a list of items that only one of them can be selected at the same time.

## Examples

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import RadioButtonGroup from "@unlockre/components-library/dist/radio-button-group";

const items = ["Option 1", "Option 2"];

const MyComponent = () => {
  const [selectedItem, setSelectedItems] = useState("Option 1");

  const getItemLabel = (item: string) => item;

  return(

    <ThemeProvider>
      <RadioButtonGroup
        {...{getItemLabel, items, selectedItem}}
        direction="vertical"
        onChange={setSelectedItem}
        size="medium"
      />
    </ThemeProvider>
  )
};

export default MyComponent;
```
