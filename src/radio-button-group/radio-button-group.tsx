import styled from "styled-components";

import {UnstyledUl} from "@/unstyled";

import RadioButton from "./radio-button";
import type {Size} from "./types";

type Direction = "horizontal" | "vertical";

type ContainerStyledProps = {
  $direction: Direction;
};

type Props<TItem> = {
  className?: string;
  direction: Direction;
  getItemLabel: (item: TItem) => string;
  hasError?: boolean;
  isDisabled?: boolean;
  items: TItem[];
  onChange: (item: TItem) => unknown;
  selectedItem: TItem;
  size: Size;
};

const Container = styled(UnstyledUl)<ContainerStyledProps>`
  display: flex;
  gap: 12px;
  flex-direction: ${props =>
    props.$direction === "horizontal" ? "row" : "column"};
`;

const RadioButtonGroup = <TItem,>({
  direction,
  getItemLabel,
  hasError,
  isDisabled,
  items,
  onChange,
  selectedItem,
  size,
  ...rest
}: Props<TItem>) => (
  <Container $direction={direction} {...rest}>
    {items.map(item => (
      <li key={getItemLabel(item)}>
        <RadioButton
          {...{hasError, isDisabled, size}}
          isSelected={selectedItem === item}
          label={getItemLabel(item)}
          onChange={!isDisabled ? () => onChange(item) : undefined}
        />
      </li>
    ))}
  </Container>
);

export default RadioButtonGroup;

export type {Props as RadioButtonGroupProps};
