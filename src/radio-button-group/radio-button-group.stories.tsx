import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";

import RadioButtonGroup from "./radio-button-group";
import type {RadioButtonGroupProps} from "./radio-button-group";

type Args = Pick<
  RadioButtonGroupProps<string>,
  "direction" | "hasError" | "isDisabled" | "items" | "selectedItem" | "size"
>;

const getItemLabel = (item: string) => item;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return (
    <RadioButtonGroup
      {...args}
      {...{getItemLabel}}
      onChange={item => updateArgs({selectedItem: item})}
    />
  );
};

const meta: Meta<Args> = {
  title: "radio-button-group",
  argTypes: {
    direction: {
      control: "radio",
      options: ["horizontal", "vertical"]
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {control: "boolean"},
    items: {
      control: "object"
    },
    selectedItem: {
      control: "text"
    },
    size: {
      control: "radio",
      options: ["small", "medium"]
    }
  },
  args: {
    direction: "vertical",
    items: ["Option 1", "Option 2", "Option 3"],
    size: "medium"
  }
};

export {meta as default, Default};
