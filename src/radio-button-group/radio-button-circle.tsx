import styled, {css} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

import type {Size} from "./types";

type StyledProps = {
  $hasError?: boolean;
  $isDisabled?: boolean;
  $isSelected?: boolean;
  $size: Size;
};

type Props = {
  hasError?: boolean;
  isDisabled?: boolean;
  isSelected?: boolean;
  size: Size;
};

const getErrorColor = getColorByAlias("feedbackError");
const getDefaultColor = getColor("gray", "250");
const getDisabledColor = getColor("gray", "070");
const getSelectedColor = getColorByAlias("accentSecondary");

const getColorCss = ({$hasError, $isDisabled, $isSelected}: StyledProps) => {
  if ($isDisabled) {
    return getDisabledColor;
  }

  if ($hasError) {
    return getErrorColor;
  }

  return $isSelected ? getSelectedColor : getDefaultColor;
};

const mediumInnerCircleCss = css`
  height: 10px;
  width: 10px;
`;

const smallInnerCircleCss = css`
  height: 8px;
  width: 8px;
`;

const innerCircleCssBySize = {
  ["small"]: smallInnerCircleCss,
  ["medium"]: mediumInnerCircleCss
};

const InnerCircle = styled.div<StyledProps>`
  ${props => innerCircleCssBySize[props.$size]}
  background: ${getColorCss};
  border-radius: 50%;
`;

const mediumContainerCss = css`
  height: 20px;
  width: 20px;
`;

const smallContainerCss = css`
  height: 16px;
  width: 16px;
`;

const containerCssBySize = {
  ["small"]: smallContainerCss,
  ["medium"]: mediumContainerCss
};

const Container = styled.div<StyledProps>`
  ${props => containerCssBySize[props.$size]};
  align-items: center;
  border-radius: 50%;
  border: 1px solid ${getColorCss};
  display: flex;
  justify-content: center;
`;

const RadioButtonCircle = ({hasError, isDisabled, isSelected, size}: Props) => (
  <Container
    $hasError={hasError}
    $isDisabled={isDisabled}
    $isSelected={isSelected}
    $size={size}
  >
    {isSelected && (
      <InnerCircle
        $hasError={hasError}
        $isDisabled={isDisabled}
        $isSelected
        $size={size}
      />
    )}
  </Container>
);

export default RadioButtonCircle;
