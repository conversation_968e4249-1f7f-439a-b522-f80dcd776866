import {LoadScript} from "@react-google-maps/api";
import type {Meta, StoryFn} from "@storybook/react";

import MapViewDeprecated from "./map-view-deprecated";

type Args = {
  apiKey: string;
  latitude: number;
  longitude: number;
  zoom: number;
};

const Default: StoryFn<Args> = args => (
  <>
    <LoadScript googleMapsApiKey={args.apiKey} />
    <MapViewDeprecated
      center={{lat: args.latitude, lng: args.longitude}}
      mapContainerStyle={{width: "400px", height: "400px"}}
      onCenterChange={newPosition => console.log("center", newPosition)}
      options={{fullscreenControl: false, mapTypeControl: false}}
      zoom={args.zoom}
    />
  </>
);

const meta: Meta<Args> = {
  title: "map-view-deprecated",
  argTypes: {
    apiKey: {
      control: "text"
    },
    latitude: {
      control: "number"
    },
    longitude: {
      control: "number"
    },
    zoom: {
      control: "number"
    }
  },
  args: {
    latitude: 0,
    longitude: 0,
    zoom: 9
  }
};

export {meta as default, Default};
