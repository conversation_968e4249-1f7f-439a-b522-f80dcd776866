import {GoogleMap as GoogleMapView} from "@react-google-maps/api";
import areDeepEqual from "fast-deep-equal";
import {useEffect, useState} from "react";
import type {ComponentProps, ReactNode} from "react";
import {useDebouncedCallback} from "use-debounce";

import * as withGoogleMap from "./google-map";
import type {MapBounds} from "./types";

type ChildrenRenderer = (map: google.maps.Map) => ReactNode;

type GoogleMapViewProps = ComponentProps<typeof GoogleMapView>;

type ExposedGoogleMapViewProps = Omit<
  GoogleMapViewProps,
  "children" | "onBoundsChanged" | "onLoad" | "onUnmount"
>;

type Props = ExposedGoogleMapViewProps & {
  bounds?: MapBounds;
  children?: ChildrenRenderer;
  onBoundsChange?: (mapBounds: MapBounds) => unknown;
  onCenterChange?: (position: google.maps.LatLngLiteral | undefined) => void;
};

const MapViewDeprecated = ({
  bounds,
  children,
  onBoundsChange,
  onCenterChange,
  ...rest
}: Props) => {
  const [map, setMap] = useState<google.maps.Map | undefined>(undefined);

  const onBoundsChangeDebounced = useDebouncedCallback(
    (mapBounds: MapBounds) => onBoundsChange?.(mapBounds),
    500
  );

  useEffect(() => {
    if (!bounds || !map) {
      return;
    }

    const currentBounds = withGoogleMap.getBounds(map);

    // we need this as the reported bounds are not the real ones and if we give
    // again to the map, it changes the current viewport
    if (areDeepEqual(bounds, currentBounds)) {
      return;
    }

    map.fitBounds(
      new google.maps.LatLngBounds(bounds.southWest, bounds.northEast)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bounds]);

  const handleOnDragEnd = () => {
    if (!onCenterChange) {
      return;
    }
    onCenterChange(map?.getCenter()?.toJSON());
  };

  return (
    <GoogleMapView
      {...rest}
      onBoundsChanged={() => {
        if (!onBoundsChange) {
          return;
        }

        const newBounds = map && withGoogleMap.getBounds(map);

        if (newBounds && !areDeepEqual(newBounds, bounds)) {
          onBoundsChangeDebounced(newBounds);
        }
      }}
      onDragEnd={handleOnDragEnd}
      onLoad={setMap}
      onUnmount={() => setMap(undefined)}
    >
      {map && children?.(map)}
    </GoogleMapView>
  );
};

export default MapViewDeprecated;
