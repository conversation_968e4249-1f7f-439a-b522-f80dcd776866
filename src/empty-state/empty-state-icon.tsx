import type {Icon} from "@phosphor-icons/react";

import {IconWithColor} from "@/icons/icon-with-color";
import {getColorByAlias} from "@/theme-provider/theme";

import {emptyStateSizes} from "./empty-state-size";
import type {EmptyStateSize} from "./empty-state-size";

type Props = {
  icon: Icon;
  size: EmptyStateSize;
};

const emptyStateIconSizes = {
  [emptyStateSizes.large]: 40,
  [emptyStateSizes.medium]: 32,
  [emptyStateSizes.small]: 24
} as const;

const EmptyStateIcon = ({icon, size}: Props) => (
  <IconWithColor
    getColor={getColorByAlias("textSecondary")}
    icon={icon}
    size={emptyStateIconSizes[size]}
    weight="regular"
  />
);

export {EmptyStateIcon};
