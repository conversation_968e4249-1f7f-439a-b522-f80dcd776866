import type {Icon} from "@phosphor-icons/react";
import styled, {css} from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import {EmptyStateIcon} from "./empty-state-icon";
import {emptyStateSizes} from "./empty-state-size";
import type {EmptyStateSize} from "./empty-state-size";

type Props = {
  className?: string;
  icon: Icon;
  message: string;
  noBackground?: boolean;
  size: EmptyStateSize;
};

type MessageStyledProps = {
  $size: EmptyStateSize;
};

type ContainerStyledProps = {
  $noBackground?: boolean;
};

const backgroundCss = css`
  background-color: ${getColor("gray", "040")};
  border-radius: 8px;
  padding: 24px;
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => !props.$noBackground && backgroundCss}

  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
`;

const getMessageTypography = (size: EmptyStateSize) => {
  switch (size) {
    case emptyStateSizes.large:
      return getTypography("title", "l", 600);

    case emptyStateSizes.medium:
      return getTypography("body", "l", 400);

    case emptyStateSizes.small:
      return getTypography("body", "s", 400);
  }
};

const Message = styled.div<MessageStyledProps>`
  ${({$size}) => getMessageTypography($size)}

  color: ${getColorByAlias("textSecondary")};
  text-align: center;
`;

const EmptyState = ({className, icon, message, noBackground, size}: Props) => (
  <Container {...{className}} $noBackground={noBackground}>
    <EmptyStateIcon {...{icon, size}} />
    <Message $size={size}>{message}</Message>
  </Container>
);

export {EmptyState};
