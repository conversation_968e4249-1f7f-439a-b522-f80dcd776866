import {Buildings} from "@phosphor-icons/react";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {EmptyState} from "./empty-state";
import {emptyStateSizes} from "./empty-state-size";

type EmptyStateProps = ComponentProps<typeof EmptyState>;

type Args = Pick<EmptyStateProps, "message" | "noBackground" | "size">;

const Default: StoryFn<Args> = args => (
  <EmptyState {...args} icon={Buildings} />
);

const meta: Meta<Args> = {
  title: "empty-state",
  argTypes: {
    message: {
      control: "text"
    },
    noBackground: {
      control: "boolean"
    },
    size: {
      control: "radio",
      options: Object.values(emptyStateSizes)
    }
  },
  args: {
    message: "There are no lease expirations in this property",
    noBackground: false,
    size: "medium"
  }
};

export {meta as default, Default};
