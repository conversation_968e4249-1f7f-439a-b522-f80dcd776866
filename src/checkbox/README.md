# Checkbox

## About

This component renders a checkbox.

## Examples

### Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Checkbox from "@unlockre/components-library/dist/checkbox";

const MyComponent = () => {
  const [isChecked, setIsChecked] = useState();

  return (
    <ThemeProvider>
      <Checkbox
        {...{isChecked}}
        label="My Checkbox"
        onChange={() => setIsChecked(currentState => !currentState)}
        size="medium"
      />
    </ThemeProvider>
  );
};

export default MyComponent;
```

### Example with custom color

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {getColor} from "@unlockre/components-library/dist/theme-provider/theme";

import Checkbox from "@unlockre/components-library/dist/checkbox";

const MyComponent = () => {
  const [isChecked, setIsChecked] = useState();

  return (
    <ThemeProvider>
      <Checkbox
        {...{isChecked}}
        getColor={getColor("red", "300")}
        label="My Checkbox"
        onChange={() => setIsChecked(currentState => !currentState)}
        size="medium"
      />
    </ThemeProvider>
  );
};

export default MyComponent;
```

### Example with indeterminate state

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Checkbox from "@unlockre/components-library/dist/checkbox";

const MyComponent = () => {
  const [isChecked, setIsChecked] = useState();

  return (
    <ThemeProvider>
      <Checkbox
        {...{isChecked}}
        isIndeterminated
        label="My Checkbox"
        onChange={() => setIsChecked(currentState => !currentState)}
        size="medium"
      />
    </ThemeProvider>
  );
};

export default MyComponent;
```

### Example with label position left

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Checkbox from "@unlockre/components-library/dist/checkbox";

const MyComponent = () => {
  const [isChecked, setIsChecked] = useState();

  return (
    <ThemeProvider>
      <Checkbox
        {...{isChecked}}
        label="My Checkbox"
        labelPosition="left"
        onChange={() => setIsChecked(currentState => !currentState)}
        size="medium"
      />
    </ThemeProvider>
  );
};

export default MyComponent;
```
