import {CheckIcon, MinusIcon} from "@/icons";
import {getColor} from "@/theme-provider/theme";

import {checkboxSizes} from "./checkbox-size";
import type {CheckboxSize} from "./checkbox-size";

type Props = {
  isDisabled?: boolean;
  isIndeterminated?: boolean;
  size: CheckboxSize;
};

const getDefaultColor = getColor("gray", "000");

const getDisabledColor = getColor("gray", "550");

const getIconColor = (isDisabled?: boolean) =>
  isDisabled ? getDisabledColor : getDefaultColor;

const iconSizesByCheckboxSize = {
  [checkboxSizes.small]: 12,
  [checkboxSizes.medium]: 16
};

const getIconSize = (checkboxSize: CheckboxSize) =>
  iconSizesByCheckboxSize[checkboxSize];

const CheckboxIcon = ({isDisabled, isIndeterminated, size}: Props) =>
  isIndeterminated ? (
    <MinusIcon getColor={getIconColor(isDisabled)} size={getIconSize(size)} />
  ) : (
    <CheckIcon getColor={getIconColor(isDisabled)} size={getIconSize(size)} />
  );

export default CheckboxIcon;
