import type {ComponentProps} from "react";
import styled, {css} from "styled-components";
import type {StyledProps} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";

import {checkboxSizes} from "./checkbox-size";
import type {CheckboxSize} from "./checkbox-size";

type ContainerStyledProps = {
  $getColor?: GetColor;
  $isChecked?: boolean;
  $isDisabled?: boolean;
  $size: CheckboxSize;
};

type Props = StyledProps<ComponentProps<"div"> & ContainerStyledProps>;

const borderSize = 1;

const defaultBorderCss = css`
  border: ${borderSize}px solid ${getColor("gray", "250")};

  &:hover {
    border: ${borderSize}px solid ${getColorByAlias("accentTertiary")};
  }
`;

const getCustomColorBorderCss = ({$getColor}: Props) => css`
  border: ${borderSize}px solid ${$getColor};

  &:hover {
    filter: brightness(0.8);
  }
`;

const getNonDisabledNonCheckedCss = (props: Props) =>
  props.$getColor ? getCustomColorBorderCss(props) : defaultBorderCss;

const disabledNonCheckedCss = css`
  border: ${borderSize}px solid ${getColor("gray", "100")};
`;

const getNonCheckedCss = (props: Props) =>
  props.$isDisabled
    ? disabledNonCheckedCss
    : getNonDisabledNonCheckedCss(props);

const defaultBackgroundCss = css`
  background: ${getColorByAlias("accentSecondary")};

  &:hover {
    background: ${getColorByAlias("accentTertiary")};
  }
`;

const getCustomColorBackgroundCss = ({$getColor}: Props) => css`
  background: ${$getColor};

  &:hover {
    filter: brightness(0.8);
  }
`;

const getNonDisabledCheckedCss = (props: Props) =>
  props.$getColor ? getCustomColorBackgroundCss(props) : defaultBackgroundCss;

const disabledCheckedCss = css`
  background: ${getColor("gray", "250")};
`;

const getCheckedCss = (props: Props) =>
  props.$isDisabled ? disabledCheckedCss : getNonDisabledCheckedCss(props);

const getColorCss = (props: Props) =>
  props.$isChecked ? getCheckedCss(props) : getNonCheckedCss(props);

const containerSizes = {
  [checkboxSizes.small]: 16,
  [checkboxSizes.medium]: 20
};

const getSizeCss = ({$size}: Props) => css`
  height: ${containerSizes[$size]}px;
  width: ${containerSizes[$size]}px;
`;

const CheckboxIconContainer = styled.div<ContainerStyledProps>`
  ${getSizeCss};
  ${getColorCss};

  align-items: center;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
`;

export default CheckboxIconContainer;
