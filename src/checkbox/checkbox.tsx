import type {MouseE<PERSON>} from "react";
import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import CheckboxIcon from "./checkbox-icon";
import CheckboxIconContainer from "./checkbox-icon-container";
import {checkboxLabelPositions} from "./checkbox-label-position";
import type {CheckboxLabelPosition} from "./checkbox-label-position";
import * as withCheckboxSize from "./checkbox-size";
import {checkboxSizes} from "./checkbox-size";
import type {CheckboxSize} from "./checkbox-size";

type LabelStyledProps = {
  $size: CheckboxSize;
};

type ContainerStyledProps = {
  $labelPosition?: CheckboxLabelPosition;
  $size: CheckboxSize;
};

type Props = {
  getColor?: GetColor;
  isChecked?: boolean;
  isDisabled?: boolean;
  isIndeterminated?: boolean;
  label?: string;
  labelPosition?: CheckboxLabelPosition;
  onChange: (e: MouseEvent<HTMLButtonElement>) => unknown;
  size: CheckboxSize;
};

const typographiesByCheckboxSize = {
  [checkboxSizes.small]: getTypography("body", "s"),
  [checkboxSizes.medium]: getTypography("body", "m")
};

const getTypographyByCheckboxSize = (checkboxSize: CheckboxSize) =>
  typographiesByCheckboxSize[checkboxSize];

const Label = styled.span<LabelStyledProps>`
  ${props => getTypographyByCheckboxSize(props.$size)}
  color: ${getColorByAlias("textPrimary")};
`;

const Container = styled(UnstyledButton)<ContainerStyledProps>`
  align-items: center;
  display: flex;
  flex-direction: ${props =>
    props.$labelPosition === checkboxLabelPositions.right
      ? "row"
      : "row-reverse"};
  gap: ${props => (withCheckboxSize.isSmall(props.$size) ? 8 : 12)}px;
`;

const Checkbox = ({
  getColor,
  isChecked,
  isDisabled,
  isIndeterminated,
  label,
  labelPosition = checkboxLabelPositions.right,
  onChange,
  size
}: Props) => (
  <Container
    $labelPosition={labelPosition}
    $size={size}
    disabled={isDisabled}
    onClick={e => onChange(e)}
    type="button"
  >
    <CheckboxIconContainer
      $getColor={getColor}
      $isChecked={isChecked}
      $isDisabled={isDisabled}
      $size={size}
    >
      {isChecked && <CheckboxIcon {...{size, isDisabled, isIndeterminated}} />}
    </CheckboxIconContainer>
    {label && <Label $size={size}>{label}</Label>}
  </Container>
);

export default Checkbox;
