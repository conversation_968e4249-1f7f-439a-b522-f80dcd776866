import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";

import Checkbox from "./checkbox";
import {checkboxLabelPositions} from "./checkbox-label-position";
import type {CheckboxLabelPosition} from "./checkbox-label-position";
import {checkboxSizes} from "./checkbox-size";
import type {CheckboxSize} from "./checkbox-size";

type Args = {
  isChecked?: boolean;
  isDisabled?: boolean;
  isIndeterminated?: boolean;
  label?: string;
  labelPosition?: CheckboxLabelPosition;
  size: CheckboxSize;
};

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return (
    <Checkbox
      {...args}
      onChange={() => updateArgs({isChecked: !args.isChecked})}
    />
  );
};

const meta: Meta<Args> = {
  title: "checkbox",
  argTypes: {
    isChecked: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    },
    isIndeterminated: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    labelPosition: {
      control: "select",
      options: Object.values(checkboxLabelPositions)
    },
    size: {
      control: "select",
      options: Object.values(checkboxSizes)
    }
  },
  args: {
    isChecked: false,
    isDisabled: false,
    isIndeterminated: false,
    label: "My checkbox",
    labelPosition: checkboxLabelPositions.right,
    size: checkboxSizes.medium
  }
};

export {meta as default, Default};
