import {CalendarPicker} from "@mui/x-date-pickers";
import type {CalendarPickerProps} from "@mui/x-date-pickers";
import {AdapterDateFns} from "@mui/x-date-pickers/AdapterDateFns";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";

import MuiThemeProvider from "@/mui-theme-provider";

type Props = CalendarPickerProps<Date>;

const MuiCalendarPicker = (props: Props) => (
  <LocalizationProvider dateAdapter={AdapterDateFns}>
    <MuiThemeProvider>
      <CalendarPicker {...props} />
    </MuiThemeProvider>
  </LocalizationProvider>
);

export default MuiCalendarPicker;
