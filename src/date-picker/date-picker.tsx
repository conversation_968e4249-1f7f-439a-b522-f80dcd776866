import {PickersDay} from "@mui/x-date-pickers";
import type {PickersDayProps} from "@mui/x-date-pickers";
import {forwardRef} from "react";
import type {
  CSSProperties,
  ComponentProps,
  ComponentType,
  ReactNode
} from "react";
import styled, {useTheme} from "styled-components";

import {ThemeProvider} from "@/theme-provider";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import MuiCalendarPicker from "./mui-calendar-picker";

type StyledDatePickerContainerProps = {
  style?: CSSProperties;
};

type MuiPickersDayProps = PickersDayProps<Date>;

type MuiCalendarPickerProps = ComponentProps<typeof MuiCalendarPicker>;

type Props = Omit<
  MuiCalendarPickerProps,
  | "components"
  | "componentsProps"
  | "reduceAnimations"
  | "renderDay"
  | "renderLoading"
> & {
  footer?: ReactNode;
  style?: CSSProperties;
};

type ContainerElement = HTMLDivElement;

// eslint-disable-next-line @typescript-eslint/naming-convention
const StyledPickersDay = styled(PickersDay)`
  &.MuiPickersDay-dayWithMargin {
    ${getTypography("body", "s", 400)}
    color: ${getColorByAlias("textPrimary")};
  }

  &.Mui-selected {
    ${getTypography("body", "s", 400)}
    background-color: ${getColor("blue", "500")};
    color: ${getColor("gray", "000")};
  }
` as ComponentType<MuiPickersDayProps>;

const StyledMuiCalendarPicker = styled(MuiCalendarPicker)`
  & .MuiCalendarPicker-viewTransitionContainer {
    max-height: 280px;
  }

  & .MuiTypography-caption {
    ${getTypography("body", "s", 400)}
    color: ${getColorByAlias("textSecondary")};
  }

  & [role="presentation"] {
    ${getTypography("body", "l", 600)}
    color: ${getColorByAlias("textPrimary")};
  }
`;

const DatePickerContainer = styled.div<StyledDatePickerContainerProps>`
  background: ${getColorByAlias("backgroundWhite")};
  border: 1px solid ${getColor("gray", "070")};
  border-radius: 10px;
  box-shadow: 0px 4px 30px 6px rgba(7, 24, 84, 0.14);
  display: flex;
  flex-direction: column;
  padding: 24px;
  width: fit-content;
  z-index: 1;
`;

const DatePicker = forwardRef<ContainerElement, Props>(
  ({footer, style, ...rest}, ref) => {
    const theme = useTheme();

    return (
      <DatePickerContainer ref={ref} {...{style}}>
        <StyledMuiCalendarPicker
          {...rest}
          renderDay={(day, selectedDates, pickersDayProps) => (
            <ThemeProvider {...{theme}}>
              <StyledPickersDay {...pickersDayProps} />
            </ThemeProvider>
          )}
        />
        {footer}
      </DatePickerContainer>
    );
  }
);

export default DatePicker;
