import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import styled from "styled-components";

import Button from "@/button";

import DatePicker, {DatePickerFooter, useDatePicker} from "./";

type Args = {
  withFooter?: boolean;
};

const StyledButton = styled(Button)`
  margin: 0;
`;

const Default: StoryFn<Args> = ({withFooter}) => {
  const [date, setDate] = useState<Date | null>(() => new Date());

  const {datePickerProps, isDatePickerOpened, reference, toggleDatePicker} =
    useDatePicker();

  return (
    <>
      <StyledButton
        onClick={toggleDatePicker}
        ref={reference}
        size="small"
        variant="primary"
      >
        {isDatePickerOpened ? "Hide Date Picker" : "Show Date Picker"}
      </StyledButton>
      {isDatePickerOpened && (
        <DatePicker
          {...datePickerProps}
          date={date}
          footer={
            withFooter && (
              <DatePickerFooter>This is the Footer!</DatePickerFooter>
            )
          }
          onChange={date => setDate(date)}
        />
      )}
    </>
  );
};

const meta: Meta<Args> = {
  title: "date-picker",
  argTypes: {
    withFooter: {
      control: "boolean"
    }
  }
};

export {meta as default, Default};
