import {autoUpdate, flip, offset, useFloating} from "@floating-ui/react-dom";
import {useCallback, useState} from "react";

const useDatePicker = () => {
  const [isDatePickerOpened, setIsDatePickerOpened] = useState(false);

  const toggleDatePicker = useCallback(
    () => setIsDatePickerOpened(currentState => !currentState),
    [setIsDatePickerOpened]
  );

  const {
    floating,
    reference,
    strategy: position,
    x,
    y
  } = useFloating({
    placement: "bottom-end",
    middleware: [flip(), offset(4)],
    strategy: "fixed",
    whileElementsMounted: autoUpdate
  });

  const datePickerProps = {
    ref: floating,
    style: {
      position,
      top: y ?? "",
      left: x ?? ""
    }
  };

  return {
    datePickerProps,
    isDatePickerOpened,
    reference,
    toggleDatePicker
  };
};

export default useDatePicker;
