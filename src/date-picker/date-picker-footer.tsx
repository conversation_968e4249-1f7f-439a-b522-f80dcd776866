import colorAlpha from "color-alpha";
import type {ReactNode} from "react";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

type Props = {
  children: ReactNode;
  className?: string;
};

const ChildrenContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;

  padding-top: 12px;
  padding-bottom: 8px;
`;

const Separator = styled.div`
  height: 1px;
  background: ${props => colorAlpha(getColor("gray", "1000")(props), 0.1)};
`;

const Container = styled.div`
  padding: 0 8px;
`;

const DatePickerFooter = ({children, className}: Props) => (
  <Container>
    <Separator />
    <ChildrenContainer {...{className}}>{children}</ChildrenContainer>
  </Container>
);

export default DatePickerFooter;
