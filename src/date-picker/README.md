# DatePicker

## About

This component provides a calendar pop-up to select a date.

## Example

```tsx
import {useState} from "react";
import styled from "styled-components";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

import Button from "@unlockre/components-library/dist/button";

import DatePicker, {DatePickerFooter, useDatePicker} from "@unlockre/components-library/dist/date-picker";

const StyledButton = styled(Button)`
  margin: 0;
`;

const MyApp = () => {
  const [date, setDate] = useState<Date | null>(() => new Date());

  const {datePickerProps, isDatePickerOpened, reference, toggleDatePicker} =
    useDatePicker();

  return (
    <ThemeProvider>
      <StyledButton
        onClick={toggleDatePicker}
        ref={reference}
        size="small"
        variant="primary"
      >
        Show date picker
      </StyledButton>
      {isDatePickerOpened && (
        <DatePicker
          {...datePickerProps}
          date={date}
          footer={<DatePickerFooter>This is the Footer!</DatePickerFooter>}
          onChange={date => setDate(date)}
        />
      )}
    </ThemeProvider>
  );
};

export default MyApp;
```
