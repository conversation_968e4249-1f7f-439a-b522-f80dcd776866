import styled, {css} from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import ChevronRightIcon from "./chevron-right-icon";

type Size = "medium" | "small";

type StyledSpanStyledProps = {
  $size: Size;
  onClick?: () => unknown;
};

type Props = {
  isLast?: boolean;
  label: string;
  onClick?: () => unknown;
  size: Size;
};

const BreadcrumbSeparator = styled(ChevronRightIcon)`
  margin: 0 6px;
`;

const unlinkedItemCss = css`
  color: ${getColorByAlias("textSecondary")};
`;

const linkedItemCss = css`
  cursor: pointer;
  color: ${getColorByAlias("accentSecondary")};
  &:hover {
    color: ${getColorByAlias("accentTertiary")};
  }
`;

const mediumTypographyCss = css`
  ${getTypography("body", "m", 400)}
`;

const smallTypographyCss = css`
  ${getTypography("body", "s", 400)}
`;

const cssBySize = {
  small: smallTypographyCss,
  medium: mediumTypographyCss
};

const StyledSpan = styled.span<StyledSpanStyledProps>`
  ${props => cssBySize[props.$size]}
  ${props => (props.onClick ? linkedItemCss : unlinkedItemCss)}
`;

const StyledLi = styled.li`
  align-items: center;
  display: flex;
`;

const Breadcrumb = ({isLast, label, onClick, size}: Props) => (
  <StyledLi>
    <StyledSpan {...{onClick}} $size={size}>
      {label}
    </StyledSpan>
    {!isLast && <BreadcrumbSeparator />}
  </StyledLi>
);

export default Breadcrumb;

export type {Size as BreadcrumbSize};
