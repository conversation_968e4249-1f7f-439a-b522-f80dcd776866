# BreadcrumbList

## About

This component provides a navigation aid to help the user understand their location on the page.

### Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import BreadcrumbList from "@unlockre/components-library/dist/breadcrumb-list";

const selectableItems = [
  {label: "First step", url: "some-url"},
  {label: "Second step", url: "another-url"}
];

const MyApp = () => (
  <ThemeProvider>
    <BreadcrumbList
      getBreadcrumbLabel={item => item.label}
      lastBreadcrumbLabel="Last step"
      selectableItems={selectableItems}
      onItemSelect={item => console.log(item)}
      size="small"
      variant="simple"
    />
  </ThemeProvider>
);

export default MyApp;
```