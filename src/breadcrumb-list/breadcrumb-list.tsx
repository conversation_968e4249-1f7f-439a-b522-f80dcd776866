import styled, {css} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";
import {UnstyledUl} from "@/unstyled";

import Breadcrumb from "./breadcrumb";
import type {BreadcrumbSize} from "./breadcrumb";

type Variant = "filled" | "simple" | "underlined";

type ContainerStyledProps = {
  $variant: Variant;
};

type Props<TItem> = {
  className?: string;
  getBreadcrumbLabel: (selectableItem: TItem) => string;
  lastBreadcrumbLabel: string;
  onItemSelect: (selectableItem: TItem) => unknown;
  selectableItems: TItem[];
  size?: BreadcrumbSize;
  variant: Variant;
};

const underlinedCss = css`
  padding-bottom: 8px;
  border-bottom: 1px solid ${getColorByAlias("accentSecondary")};
`;

const filledCss = css`
  border-radius: 6px;
  background: ${getColor("blue", "040")};
  padding: 12px;
`;

const cssByVariant = {
  filled: filledCss,
  simple: undefined,
  underlined: underlinedCss
};

const Container = styled(UnstyledUl)<ContainerStyledProps>`
  display: flex;
  width: fit-content;
  ${props => cssByVariant[props.$variant]};
`;

const BreadcrumbList = <TItem,>({
  getBreadcrumbLabel,
  lastBreadcrumbLabel,
  onItemSelect,
  selectableItems,
  size = "small",
  variant,
  ...rest
}: Props<TItem>) => (
  <Container $variant={variant} {...rest}>
    {selectableItems.map((item, index) => (
      <Breadcrumb
        {...{size}}
        key={index}
        label={getBreadcrumbLabel(item)}
        onClick={() => onItemSelect(item)}
      />
    ))}
    <Breadcrumb {...{size}} isLast label={lastBreadcrumbLabel} />
  </Container>
);

export default BreadcrumbList;

export type {Props as BreadcrumbListProps, Variant as BreadcrumbListVariant};
