import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

type Props = {
  className?: string;
};

const StyledPath = styled.path`
  fill: ${getColor("gray", "250")};
`;

const ChevronRightIcon = (props: Props) => (
  <svg
    fill="none"
    height="16"
    viewBox="0 0 16 16"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <StyledPath
      clipRule="evenodd"
      d="M5.22825 3.50501C5.50162 3.23165 5.94483 3.23165 6.2182 3.50501L10.2182 7.50501C10.4916 7.77838 10.4916 8.2216 10.2182 8.49496L6.2182 12.495C5.94483 12.7683 5.50162 12.7683 5.22825 12.495C4.95488 12.2216 4.95488 11.7784 5.22825 11.505L8.73327 7.99999L5.22825 4.49496C4.95488 4.2216 4.95488 3.77838 5.22825 3.50501Z"
      fillRule="evenodd"
    />
  </svg>
);

export default ChevronRightIcon;
