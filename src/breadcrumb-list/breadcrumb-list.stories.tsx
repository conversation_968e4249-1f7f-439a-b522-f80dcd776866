import type {Meta, StoryFn} from "@storybook/react";

import BreadcrumbList from "./breadcrumb-list";
import type {BreadcrumbListProps} from "./breadcrumb-list";

type Breadcrumb = {
  label: string;
  url?: string;
};

type Args = Pick<
  BreadcrumbListProps<Breadcrumb>,
  "lastBreadcrumbLabel" | "selectableItems" | "size" | "variant"
>;

const Default: StoryFn<Args> = args => (
  <BreadcrumbList
    getBreadcrumbLabel={item => item.label}
    onItemSelect={item => console.log(item)}
    {...args}
  />
);

const meta: Meta<Args> = {
  title: "breadcrumb-list",
  argTypes: {
    selectableItems: {
      control: "object"
    },
    size: {
      control: "radio",
      options: ["small", "medium"]
    },
    variant: {
      control: "radio",
      options: ["filled", "simple", "underlined"]
    }
  },
  args: {
    lastBreadcrumbLabel: "Last Step",
    selectableItems: [
      {label: "First step", url: "some-url"},
      {label: "Second step", url: "another-url"}
    ],
    size: "small",
    variant: "simple"
  }
};

export {meta as default, Default};
