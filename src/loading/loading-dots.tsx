import type {ComponentProps} from "react";
import styled, {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import {AnimatedDots} from "./animated-dots";

type Size = "large" | "small";

type Props = {
  size: Size;
  variant: ComponentProps<typeof AnimatedDots>["variant"];
  withoutContainer?: boolean;
};

type ContainerStyledProps = {size: Size};

const smallCss = css`
  width: 64px;
  height: 32px;
`;

const largeCss = css`
  width: 86px;
  height: 50px;
`;

const Container = styled.div<ContainerStyledProps>`
  background-color: ${getColorByAlias("backgroundWhite")};
  border-radius: 16px;
  box-shadow: 0px 17px 13px 0px rgba(66, 141, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  ${({size}) => (size === "small" ? smallCss : largeCss)};
`;

const LoadingDots = ({size, variant, withoutContainer}: Props) =>
  withoutContainer ? (
    <AnimatedDots variant={variant} />
  ) : (
    <Container size={size}>
      <AnimatedDots variant={variant} />
    </Container>
  );

export {LoadingDots};
