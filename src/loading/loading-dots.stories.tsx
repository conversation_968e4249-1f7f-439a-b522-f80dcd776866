import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {LoadingDots} from "./loading-dots";

type LoadingDotsProps = ComponentProps<typeof LoadingDots>;

type Args = LoadingDotsProps;

const Default: StoryFn<Args> = args => <LoadingDots {...args} />;

const meta: Meta<Args> = {
  title: "loading-dots",
  argTypes: {
    size: {
      control: "radio",
      options: ["large", "small"]
    },
    variant: {
      control: "radio",
      options: ["blue", "white"]
    },
    withoutContainer: {
      control: "boolean"
    }
  },
  args: {
    size: "large",
    variant: "blue",
    withoutContainer: false
  }
};

export {meta as default, Default};
