import type {CSSProperties} from "react";
import styled, {keyframes} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

type Variant = "blue" | "violet" | "white";

type Props = {
  variant: Variant;
};

type DotStyledProps = {
  animationDelay: CSSProperties["animationDelay"];
  variant: Variant;
};

const bounceKeyframes = keyframes`
  0% {
    transform: translateY(0);
    opacity: 0.15;
  }
  33% {
    transform: translateY(-4px);
    opacity: 1;
  }
  66% {
    transform: translateY(0);
    opacity: 0.15;
  }
  100% {
    transform: translateY(0);
    opacity: 0.15;
  }
`;

const getVariantColor = (variant: Variant) => {
  switch (variant) {
    case "blue":
      return getColor("blue", "800");

    case "white":
      return getColorByAlias("backgroundWhite");

    case "violet":
      return getColor("latest", "violetAlpha400");
  }
};

const Dot = styled.div<DotStyledProps>`
  opacity: 0.15;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: ${bounceKeyframes} 1s ease-in-out infinite;
  animation-delay: ${({animationDelay}) => animationDelay};
  background-color: ${({variant}) => getVariantColor(variant)};
`;

const Container = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
`;

const AnimatedDots = ({variant}: Props) => (
  <Container>
    <Dot animationDelay="0s" variant={variant} />
    <Dot animationDelay="0.25s" variant={variant} />
    <Dot animationDelay="0.5s" variant={variant} />
  </Container>
);

export {AnimatedDots};
