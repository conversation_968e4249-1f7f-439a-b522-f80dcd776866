import {getGeocode} from "use-places-autocomplete";

import * as withAddress from "./address";
import type {Address} from "./address";
import * as withGeoLocation from "./geo-location";
import type {GeoLocation} from "./geo-location";

type Place = {
  address: Address;
  id: string;
  location: GeoLocation;
};

const createFrom = (geoCodeResult: google.maps.GeocoderResult): Place => ({
  address: withAddress.createFrom(geoCodeResult),
  id: geoCodeResult.place_id,
  location: withGeoLocation.createFrom(geoCodeResult)
});

const get = (placeId: string) =>
  getGeocode({placeId}).then(([geoCodeResult]) => createFrom(geoCodeResult));

export {createFrom, get};
export type {Place};
