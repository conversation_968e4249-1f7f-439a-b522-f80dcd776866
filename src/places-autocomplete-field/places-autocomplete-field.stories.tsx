import {useJs<PERSON>piLoader} from "@react-google-maps/api";
import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import type {ComponentProps} from "react";

import PlacesAutocompleteField from "./places-autocomplete-field";

type PlacesAutocompleteFieldProps = ComponentProps<
  typeof PlacesAutocompleteField
>;

type Value = PlacesAutocompleteFieldProps["value"];

type StoryArgs = {
  googleMapsApiKey: string;
};

type UseLoadScriptOptions = Parameters<typeof useJsApiLoader>[0];

const googleApiLibraries: UseLoadScriptOptions["libraries"] = ["places"];

const Default: StoryFn<StoryArgs> = args => {
  const [value, setValue] = useState<Value>(null);

  const {isLoaded: isGoogleApiReady} = useJsApiLoader({
    ...args,
    id: "google-map-script",
    libraries: googleApiLibraries
  });

  return (
    <PlacesAutocompleteField
      {...{isGoogleApiReady, value}}
      onChange={value => {
        console.log(value);

        setValue(value);
      }}
    />
  );
};

const meta: Meta<StoryArgs> = {
  title: "places-autocomplete-field",
  argTypes: {
    googleMapsApiKey: {
      control: "text"
    }
  }
};

export {meta as default, Default};
