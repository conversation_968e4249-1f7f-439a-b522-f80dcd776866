import {Search as SearchIcon} from "@mui/icons-material";
import {useEffect} from "react";
import type {ComponentType} from "react";
import styled from "styled-components";
import usePlacesAutocomplete from "use-places-autocomplete";
import type {RequestOptions} from "use-places-autocomplete";

import AutocompleteField from "@/autocomplete-field";
import type {AutocompleteFieldProps} from "@/autocomplete-field";

import type {PlaceSuggestion} from "./types";

type ConcreteAutocompleteFieldProps = AutocompleteFieldProps<PlaceSuggestion>;

type ConcreteAutocompleteFieldComponent =
  ComponentType<ConcreteAutocompleteFieldProps>;

type ExposedAutocompleteFieldProps = Omit<
  ConcreteAutocompleteFieldProps,
  | "getOptionLabel"
  | "InputProps"
  | "isLoading"
  | "left"
  | "onInputChange"
  | "options"
  | "right"
>;

type Props = ExposedAutocompleteFieldProps & {
  hideSearchIcon?: boolean;
  isGoogleApiReady?: boolean;
  requestOptions?: RequestOptions;
};

const StyledSearchIcon = styled(SearchIcon)`
  color: rgba(26, 26, 26);
  height: 18px;
  margin-right: 6px;
  width: 18px;
`;

// eslint-disable-next-line @typescript-eslint/naming-convention
const StyledAutocompleteField = styled(AutocompleteField)`
  width: 60%;
` as ConcreteAutocompleteFieldComponent;

const requestOptions: RequestOptions = {
  componentRestrictions: {country: "us"},
  types: ["point_of_interest", "premise", "street_address"]
};

const PlacesAutocompleteField = ({
  hideSearchIcon,
  isGoogleApiReady,
  onChange,
  ...rest
}: Props) => {
  const {init, setValue, suggestions} = usePlacesAutocomplete({
    initOnMount: false,
    requestOptions
  });

  useEffect(() => {
    if (isGoogleApiReady) {
      init();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isGoogleApiReady]);

  return (
    <StyledAutocompleteField
      {...{onChange}}
      {...rest}
      filterOptions={option => option}
      getOptionLabel={suggestion =>
        typeof suggestion === "string" ? suggestion : suggestion.description
      }
      isLoading={suggestions.loading}
      left={hideSearchIcon ? undefined : <StyledSearchIcon />}
      onInputChange={({hasSelectedOption, inputValue}) =>
        setValue(inputValue, !hasSelectedOption)
      }
      options={suggestions.data}
    />
  );
};

export default PlacesAutocompleteField;
