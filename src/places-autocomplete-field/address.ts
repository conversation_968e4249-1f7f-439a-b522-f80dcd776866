import {ensureIsDefined} from "@unlockre/utils-validation/dist";

type Address = {
  city: string;
  state: string;
  street: string;
  zipCode: string;
};

const findAddressComponent = (
  addressComponents: google.maps.GeocoderAddressComponent[],
  type: string
) =>
  ensureIsDefined(
    addressComponents.find(addressComponent =>
      addressComponent.types.includes(type)
    ),
    "No address component found for type: " + type
  );

const createFrom = (geoCodeResult: google.maps.GeocoderResult): Address => {
  const addressComponents = geoCodeResult.address_components;

  const city = findAddressComponent(addressComponents, "locality").long_name;

  const state = findAddressComponent(
    addressComponents,
    "administrative_area_level_1"
  ).long_name;

  const streetNumber = findAddressComponent(
    addressComponents,
    "street_number"
  ).long_name;

  const route = findAddressComponent(addressComponents, "route").long_name;

  const street = `${streetNumber} ${route}`;

  const zipCode = findAddressComponent(
    addressComponents,
    "postal_code"
  ).long_name;

  return {city, state, street, zipCode};
};

export {createFrom};
export type {Address};
