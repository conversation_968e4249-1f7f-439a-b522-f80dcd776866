import mergeRefs from "merge-refs";
import {forwardRef} from "react";
import type {ComponentProps, FunctionComponent, ReactElement} from "react";
import styled, {css} from "styled-components";

import {getTypography} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {buttonSizes} from "./button-size";
import type {ButtonSize} from "./button-size";
import {buttonStatuses} from "./button-status";
import {buttonVariants} from "./button-variant";
import type {ButtonVariant} from "./button-variant";
import {
  disabledOutlineButtonCss,
  enabledOutlineButtonCss,
  getDisabledOutlineButtonColor,
  getEnabledOutlineButtonColor,
  getHoveredOutlineButtonColor,
  getPressedOutlineButtonColor,
  pressedOutlineButtonCss
} from "./outline-button-css";
import {
  disabledPrimaryButtonCss,
  enabledPrimaryButtonCss,
  getDisabledPrimaryButtonColor,
  getEnabledPrimaryButtonColor,
  getHoveredPrimaryButtonColor,
  getPressedPrimaryButtonColor,
  pressedPrimaryButtonCss
} from "./primary-button-css";
import {
  disabledSecondaryButtonCss,
  enabledSecondaryButtonCss,
  getDisabledSecondaryButtonColor,
  getEnabledSecondaryButtonColor,
  getHoveredSecondaryButtonColor,
  getPressedSecondaryButtonColor,
  pressedSecondaryButtonCss
} from "./secondary-button-css";
import {
  disabledTransparentButtonCss,
  enabledTransparentButtonCss,
  getDisabledTransparentButtonColor,
  getEnabledTransparentButtonColor,
  getHoveredTransparentButtonColor,
  getPressedTransparentButtonColor,
  pressedTransparentButtonCss
} from "./transparent-button-css";
import {useButton} from "./use-button";
import type {ButtonElement} from "./use-button-ref";

type ButtonIconRendererParams = {
  getColor: GetColor;
  size: number;
};

type ButtonIconRenderer = (params: ButtonIconRendererParams) => ReactElement;

type UnstyledButtonProps = ComponentProps<typeof UnstyledButton>;

type ExposedUnstyledButtonProps = Omit<UnstyledButtonProps, "disabled">;

type Props = ExposedUnstyledButtonProps & {
  isDisabled?: boolean;
  renderIconLeft?: ButtonIconRenderer;
  renderIconRight?: ButtonIconRenderer;
  size: ButtonSize;
  testId?: string;
  variant: ButtonVariant;
};

type StyledButtonStyledProps = {
  $isPressed?: boolean;
  $size: ButtonSize;
  $variant: ButtonVariant;
};

type StyledButtonProps = StyledButtonStyledProps & UnstyledButtonProps;

type StyledButtonComponent = FunctionComponent<StyledButtonProps>;

const pressedCssByVariant = {
  [buttonVariants.outline]: pressedOutlineButtonCss,
  [buttonVariants.primary]: pressedPrimaryButtonCss,
  [buttonVariants.secondary]: pressedSecondaryButtonCss,
  [buttonVariants.transparent]: pressedTransparentButtonCss
};

const getPressedCss = (props: StyledButtonProps) =>
  props.$isPressed && pressedCssByVariant[props.$variant];

const enabledCssByVariant = {
  [buttonVariants.outline]: enabledOutlineButtonCss,
  [buttonVariants.primary]: enabledPrimaryButtonCss,
  [buttonVariants.secondary]: enabledSecondaryButtonCss,
  [buttonVariants.transparent]: enabledTransparentButtonCss
};

const disabledCssByVariant = {
  [buttonVariants.outline]: disabledOutlineButtonCss,
  [buttonVariants.primary]: disabledPrimaryButtonCss,
  [buttonVariants.secondary]: disabledSecondaryButtonCss,
  [buttonVariants.transparent]: disabledTransparentButtonCss
};

const getDisabledOrEnabledCss = (props: StyledButtonProps) =>
  props.disabled
    ? disabledCssByVariant[props.$variant]
    : enabledCssByVariant[props.$variant];

const largeCss = css`
  ${getTypography("body", "m", 600)}

  border-radius: 8px;
  gap: 6px;
  padding: 8px 16px;
`;

const mediumCss = css`
  ${getTypography("body", "s", 600)}

  border-radius: 8px;
  gap: 6px;
  padding: 6px 12px;
`;

const smallCss = css`
  ${getTypography("body", "xs", 600)}

  border-radius: 4px;
  gap: 4px;
  padding: 4px 8px;
`;

const cssBySize = {
  [buttonSizes.small]: smallCss,
  [buttonSizes.medium]: mediumCss,
  [buttonSizes.large]: largeCss
};

const getSizeCss = (props: StyledButtonProps) => cssBySize[props.$size];

// prettier-ignore
const StyledButton: StyledButtonComponent = styled(UnstyledButton)<StyledButtonStyledProps>`
  display: inline-flex;
  user-select: none;
  align-items: center;
  ${getSizeCss}

  ${getDisabledOrEnabledCss}

  &:hover {
    ${getPressedCss}
  }
`;

const iconColorGetters = {
  [buttonVariants.outline]: {
    [buttonStatuses.disabled]: getDisabledOutlineButtonColor,
    [buttonStatuses.enabled]: getEnabledOutlineButtonColor,
    [buttonStatuses.hovered]: getHoveredOutlineButtonColor,
    [buttonStatuses.pressed]: getPressedOutlineButtonColor
  },
  [buttonVariants.primary]: {
    [buttonStatuses.disabled]: getDisabledPrimaryButtonColor,
    [buttonStatuses.enabled]: getEnabledPrimaryButtonColor,
    [buttonStatuses.hovered]: getHoveredPrimaryButtonColor,
    [buttonStatuses.pressed]: getPressedPrimaryButtonColor
  },
  [buttonVariants.secondary]: {
    [buttonStatuses.disabled]: getDisabledSecondaryButtonColor,
    [buttonStatuses.enabled]: getEnabledSecondaryButtonColor,
    [buttonStatuses.hovered]: getHoveredSecondaryButtonColor,
    [buttonStatuses.pressed]: getPressedSecondaryButtonColor
  },
  [buttonVariants.transparent]: {
    [buttonStatuses.disabled]: getDisabledTransparentButtonColor,
    [buttonStatuses.enabled]: getEnabledTransparentButtonColor,
    [buttonStatuses.hovered]: getHoveredTransparentButtonColor,
    [buttonStatuses.pressed]: getPressedTransparentButtonColor
  }
};

const iconSizes = {
  [buttonSizes.large]: 20,
  [buttonSizes.medium]: 16,
  [buttonSizes.small]: 14
};

const Button = forwardRef<ButtonElement, Props>(
  (
    {
      children,
      isDisabled,
      renderIconLeft,
      renderIconRight,
      size,
      testId,
      variant,
      ...rest
    },
    ref
  ) => {
    const {buttonRef, buttonStatus} = useButton({
      isButtonEnabled: !isDisabled
    });

    const renderIconParams = {
      getColor: iconColorGetters[variant][buttonStatus],
      size: iconSizes[size]
    };

    return (
      <StyledButton
        {...rest}
        $isPressed={buttonStatus === buttonStatuses.pressed}
        $size={size}
        $variant={variant}
        data-testid={testId}
        disabled={isDisabled}
        ref={mergeRefs(buttonRef, ref)}
      >
        {renderIconLeft && renderIconLeft(renderIconParams)}
        {children}
        {renderIconRight && renderIconRight(renderIconParams)}
      </StyledButton>
    );
  }
);

export {Button};
export type {ButtonIconRenderer};
