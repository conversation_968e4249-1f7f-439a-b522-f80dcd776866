import {useEffect, useRef, useState} from "react";

import type {ButtonRef} from "./use-button-ref";

const releaseTimeoutMs = 120;

const useIsButtonPressed = (buttonRef: ButtonRef) => {
  const releaseButtonTimeoutRef = useRef<number | null>(null);

  const [isButtonPressed, setIsButtonPressed] = useState(false);

  // eslint-disable-next-line max-statements
  useEffect(() => {
    const buttonElement = buttonRef.current;

    if (!buttonElement) {
      return;
    }

    const releaseButton = () => setIsButtonPressed(false);

    const releaseButtonLater = () => {
      // we need to use window.setTimeout to avoid getting node type def
      releaseButtonTimeoutRef.current = window.setTimeout(
        releaseButton,
        releaseTimeoutMs
      );
    };

    const clearReleaseButtonTimeout = () => {
      if (releaseButtonTimeoutRef.current) {
        // we need to use window.clearTimeout to avoid getting node type def
        window.clearTimeout(releaseButtonTimeoutRef.current);
      }
    };

    const pressButton = () => setIsButtonPressed(true);

    const handleMouseDown = () => {
      clearReleaseButtonTimeout();

      pressButton();
    };

    buttonElement.addEventListener("mousedown", handleMouseDown);

    buttonElement.addEventListener("mouseup", releaseButtonLater);

    return () => {
      buttonElement.removeEventListener("mousedown", handleMouseDown);

      buttonElement.removeEventListener("mouseup", releaseButtonLater);

      clearReleaseButtonTimeout();
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {isButtonPressed};
};

export {useIsButtonPressed};
