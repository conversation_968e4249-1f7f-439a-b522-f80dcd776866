# Button

## About

Just a `Button` component with different **sizes** and **variants**.

> This component aligns with the [latest design](https://www.figma.com/design/E8eTOTmhxUkhViwxXT2DfY/Components?node-id=981-1388&node-type=canvas), use this instead of the one under `button/button`

### Sizes

- `large`
- `medium`
- `small`

### Variants

- `primary`
- `outline`
- `secondary`
- `transparent`

## Testing

Sometimes, having a [data-testid](https://playwright.dev/docs/locators#locate-by-test-id) attribute is useful for testing.

This component exposes a `testId?: string` prop, that if passed, it sets a `data-testid` with the given value.

> To avoid collisions between test ids, these have to be set in the apps, that's where we have control of all the rendered components.

## Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {Button} from "@unlockre/components-library/dist/buttons";

const Application = () => (
  <ThemeProvider>
    <Button
      onClick={() => console.log("clicked!")}
      size="medium"
      variant="primary"
    >
      Label
    </Button>
  </ThemeProvider>
);

export {Application};
```
