import * as withButtonStatus from "./button-status";
import {useButtonRef} from "./use-button-ref";
import {useIsButtonPressed} from "./use-is-button-pressed";
import {useIsElementHovered} from "./use-is-element-hovered";

type Params = {
  isButtonEnabled: boolean;
};

const useButton = ({isButtonEnabled}: Params) => {
  const buttonRef = useButtonRef();

  const {isElementHovered} = useIsElementHovered(buttonRef);

  const {isButtonPressed} = useIsButtonPressed(buttonRef);

  const buttonStatus = withButtonStatus.getFrom({
    isButtonEnabled,
    isButtonHovered: isElementHovered,
    isButtonPressed
  });

  return {
    buttonRef,
    buttonStatus
  };
};

export {useButton};
