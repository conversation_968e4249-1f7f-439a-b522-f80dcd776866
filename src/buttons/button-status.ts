type GetFromParams = {
  isButtonEnabled: boolean;
  isButtonHovered: boolean;
  isButtonPressed: boolean;
};

const buttonStatuses = {
  disabled: "disabled",
  enabled: "enabled",
  hovered: "hovered",
  pressed: "pressed"
} as const;

type ButtonStatus = (typeof buttonStatuses)[keyof typeof buttonStatuses];

const getFrom = ({
  isButtonEnabled,
  isButtonHovered,
  isButtonPressed
}: GetFromParams) => {
  if (!isButtonEnabled) {
    return buttonStatuses.disabled;
  }

  if (isButtonPressed) {
    return buttonStatuses.pressed;
  }

  if (isButtonHovered) {
    return buttonStatuses.hovered;
  }

  return buttonStatuses.enabled;
};

export {buttonStatuses, getFrom};
export type {ButtonStatus};
