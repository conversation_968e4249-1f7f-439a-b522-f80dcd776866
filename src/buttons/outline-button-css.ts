import {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

const getEnabledOutlineButtonColor = getColorByAlias("textAccent");

const getPressedOutlineButtonColor = getEnabledOutlineButtonColor;

const pressedOutlineButtonCss = css`
  background-color: ${getColorByAlias("bgTransparentPressed")};
`;

const getHoveredOutlineButtonColor = getEnabledOutlineButtonColor;

const enabledOutlineButtonCss = css`
  border: 1px solid ${getColorByAlias("borderAccent")};
  color: ${getEnabledOutlineButtonColor};

  &:focus {
    box-shadow: 0px 0px 0px 2px ${getColorByAlias("borderFocusOuter")};
  }

  &:hover {
    background-color: ${getColorByAlias("bgTransparentHover")};
  }
`;

const getDisabledOutlineButtonColor = getColorByAlias("textInert");

const disabledOutlineButtonCss = css`
  border: 1px solid ${getColorByAlias("borderRegular")};
  color: ${getDisabledOutlineButtonColor};
`;

export {
  disabledOutlineButtonCss,
  enabledOutlineButtonCss,
  getDisabledOutlineButtonColor,
  getEnabledOutlineButtonColor,
  getHoveredOutlineButtonColor,
  getPressedOutlineButtonColor,
  pressedOutlineButtonCss
};
