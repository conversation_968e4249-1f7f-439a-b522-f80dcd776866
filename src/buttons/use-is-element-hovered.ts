// TODO: Move this to @unlockre/utils-react package

import {useEffect, useState} from "react";
import type {RefObject} from "react";

const useIsElementHovered = <TElement extends HTMLElement>(
  elementRef: RefObject<TElement>
) => {
  const [isElementHovered, setIsElementHovered] = useState(false);

  useEffect(() => {
    const element = elementRef.current;

    if (!element) {
      return;
    }

    const handleMouseLeave = () => {
      setIsElementHovered(false);
    };

    const handleMouseEnter = () => {
      setIsElementHovered(true);
    };

    element.addEventListener("mouseenter", handleMouseEnter);

    element.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      element.removeEventListener("mouseenter", handleMouseEnter);

      element.removeEventListener("mouseleave", handleMouseLeave);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {isElementHovered};
};

export {useIsElementHovered};
