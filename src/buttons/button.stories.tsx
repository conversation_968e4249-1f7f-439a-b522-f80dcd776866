import {Confetti} from "@phosphor-icons/react";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import {IconWithColor} from "@/icons";

import {Button} from "./button";
import type {ButtonIconRenderer} from "./button";
import {buttonSizes} from "./button-size";
import {buttonVariants} from "./button-variant";

type ButtonProps = ComponentProps<typeof Button>;

type ExposedButtonProps = Pick<
  ButtonProps,
  "children" | "isDisabled" | "size" | "variant"
>;

type Args = ExposedButtonProps & {
  hasLeftIcon: boolean;
  hasRightIcon: boolean;
};

const renderButtonIcon: ButtonIconRenderer = params => (
  <IconWithColor {...params} icon={Confetti} weight="regular" />
);

const Default: StoryFn<Args> = ({hasLeftIcon, hasRightIcon, ...rest}) => (
  <Button
    {...rest}
    renderIconLeft={hasLeftIcon ? renderButtonIcon : undefined}
    renderIconRight={hasRightIcon ? renderButtonIcon : undefined}
  />
);

const meta: Meta<Args> = {
  title: "buttons/button",
  argTypes: {
    children: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    hasLeftIcon: {
      control: "boolean"
    },
    hasRightIcon: {
      control: "boolean"
    },
    size: {
      control: "radio",
      options: Object.values(buttonSizes)
    },
    variant: {
      control: "radio",
      options: Object.values(buttonVariants)
    }
  },
  args: {
    children: "Label",
    variant: buttonVariants.primary,
    size: buttonSizes.large
  }
};

export {meta as default, Default};
