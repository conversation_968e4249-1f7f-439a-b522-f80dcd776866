import {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import {disabledButtonCss, getDisabledButtonColor} from "./button-css";

const getPressedSecondaryButtonColor = getColorByAlias(
  "textOnAccentSecondaryPressed"
);

const pressedSecondaryButtonCss = css`
  background-color: ${getColorByAlias("bgAccentSecondaryPressed")};
  color: ${getPressedSecondaryButtonColor};
`;

const getHoveredSecondaryButtonColor = getColorByAlias(
  "textOnAccentSecondaryHover"
);

const getEnabledSecondaryButtonColor = getColorByAlias("textOnAccentSecondary");

const enabledSecondaryButtonCss = css`
  background-color: ${getColorByAlias("bgAccentSecondary")};
  border: 1px solid transparent;
  color: ${getEnabledSecondaryButtonColor};

  &:focus {
    border-color: ${getColorByAlias("borderFocusInner")};
    box-shadow: 0px 0px 0px 2px ${getColorByAlias("borderFocusOuter")};
  }

  &:hover {
    background-color: ${getColorByAlias("bgAccentSecondaryHover")};
    color: ${getHoveredSecondaryButtonColor};
  }
`;

const getDisabledSecondaryButtonColor = getDisabledButtonColor;

const disabledSecondaryButtonCss = disabledButtonCss;

export {
  disabledSecondaryButtonCss,
  enabledSecondaryButtonCss,
  getDisabledSecondaryButtonColor,
  getEnabledSecondaryButtonColor,
  getHoveredSecondaryButtonColor,
  getPressedSecondaryButtonColor,
  pressedSecondaryButtonCss
};
