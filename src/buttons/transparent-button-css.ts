import {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

const getEnabledTransparentButtonColor = getColorByAlias("textAccent");

const getPressedTransparentButtonColor = getEnabledTransparentButtonColor;

const pressedTransparentButtonCss = css`
  background-color: ${getColorByAlias("bgTransparentPressed")};
`;

const getHoveredTransparentButtonColor = getEnabledTransparentButtonColor;

const enabledTransparentButtonCss = css`
  border: 1px solid transparent;
  color: ${getEnabledTransparentButtonColor};

  &:focus {
    border-color: ${getColorByAlias("borderFocusInner")};
    box-shadow: 0px 0px 0px 2px ${getColorByAlias("borderFocusOuter")};
  }

  &:hover {
    background-color: ${getColorByAlias("bgTransparentHover")};
  }
`;

const getDisabledTransparentButtonColor = getColorByAlias("textInert");

const disabledTransparentButtonCss = css`
  border: 1px solid transparent;
  color: ${getDisabledTransparentButtonColor};
`;

export {
  disabledTransparentButtonCss,
  enabledTransparentButtonCss,
  getDisabledTransparentButtonColor,
  getEnabledTransparentButtonColor,
  getHoveredTransparentButtonColor,
  getPressedTransparentButtonColor,
  pressedTransparentButtonCss
};
