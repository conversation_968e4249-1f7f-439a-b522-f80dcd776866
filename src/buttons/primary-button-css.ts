import {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import {disabledButtonCss, getDisabledButtonColor} from "./button-css";

const getPressedPrimaryButtonColor = getColorByAlias("textOnAccentPressed");

const pressedPrimaryButtonCss = css`
  background-color: ${getColorByAlias("bgAccentPressed")};
  color: ${getPressedPrimaryButtonColor};
`;

const getHoveredPrimaryButtonColor = getColorByAlias("textOnAccentHover");

const getEnabledPrimaryButtonColor = getColorByAlias("textOnAccent");

const enabledPrimaryButtonCss = css`
  background-color: ${getColorByAlias("bgAccent")};
  border: 1px solid transparent;
  color: ${getEnabledPrimaryButtonColor};

  &:focus {
    border-color: ${getColorByAlias("borderFocusInner")};
    box-shadow: 0px 0px 0px 2px ${getColorByAlias("borderFocusOuter")};
  }

  &:hover {
    background-color: ${getColorByAlias("bgAccentHover")};
    color: ${getHoveredPrimaryButtonColor};
  }
`;

const getDisabledPrimaryButtonColor = getDisabledButtonColor;

const disabledPrimaryButtonCss = disabledButtonCss;

export {
  disabledPrimaryButtonCss,
  enabledPrimaryButtonCss,
  getDisabledPrimaryButtonColor,
  getEnabledPrimaryButtonColor,
  getHoveredPrimaryButtonColor,
  getPressedPrimaryButtonColor,
  pressedPrimaryButtonCss
};
