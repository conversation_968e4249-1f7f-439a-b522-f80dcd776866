import type {AnyObject} from "@unlockre/utils-object/dist";
import styled from "styled-components";

import {EditIcon} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {OpenLocationButton} from "./open-location-button";
import type {Folder, FolderItem} from "./types";

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  folder: Folder<TFolderData>;
  isSearching?: boolean;
  onFolderEditClick: (folder: Folder<TFolderData>) => unknown;
  onParentFolderOpen: (
    folderItem: FolderItem<TFileData, TFolderData>
  ) => unknown;
};

const Container = styled(UnstyledButton)`
  height: 16px;
`;

const FolderActions = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  folder,
  isSearching,
  onFolderEditClick,
  onParentFolderOpen
}: Props<TFileData, TFolderData>) => {
  if (isSearching) {
    return (
      <OpenLocationButton folderItem={folder} onClick={onParentFolderOpen} />
    );
  }

  return (
    <Container
      onClick={event => {
        event.stopPropagation();
        onFolderEditClick(folder);
      }}
    >
      <EditIcon getColor={getColorByAlias("accentSecondary")} size={16} />
    </Container>
  );
};

export {FolderActions};

export type {Props as FolderActionsProps};
