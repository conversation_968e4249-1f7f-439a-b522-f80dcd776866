import styled, {css} from "styled-components";

import {TrashIcon} from "@/icons";
import {getColor, getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type StyledButtonStyledProps = {
  $isDisabled?: boolean;
};

type Props = {
  canDeleteItems?: boolean;
  onBulkDeleteClick: () => unknown;
};

const enabledDeleteButtonCss = css`
  background: ${getColorByAlias("feedbackError")};

  &:hover {
    background: ${getColor("red", "600")};
  }

  &:active {
    background: ${getColor("red", "700")};
  }
`;

const disabledDeleteButtonCss = css`
  background: ${getColor("gray", "100")};
`;

//TODO: Remove this once we support just an icon inside a button on the Library.
const StyledButton = styled(UnstyledButton)<StyledButtonStyledProps>`
  border-radius: 5px;
  height: 32px;
  padding: 8px;
  width: 32px;

  ${props =>
    props.$isDisabled ? disabledDeleteButtonCss : enabledDeleteButtonCss}
`;

const BulkDeleteButton = ({canDeleteItems, onBulkDeleteClick}: Props) => (
  <StyledButton
    $isDisabled={!canDeleteItems}
    disabled={!canDeleteItems}
    onClick={onBulkDeleteClick}
  >
    <TrashIcon getColor={getColor("gray", "000")} />
  </StyledButton>
);

export {BulkDeleteButton};
