import type {AnyObject} from "@unlockre/utils-object/dist";
import styled from "styled-components";

import {FolderIcon} from "@/icons";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import * as withFolderItem from "./folder-item";
import * as withFolderItemType from "./folder-item-type";
import type {File, Folder, FolderItem} from "./types";

type FolderNameStyledProps = {
  $hasFilesInside?: boolean;
};

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  folderItem: FolderItem<TFileData, TFolderData>;
};

const FileName = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const ChildsAmount = styled.span`
  ${getTypography("body", "xxs", 400)};
  color: ${getColorByAlias("textSecondary")};
  padding: 2px 0 0;
`;

const renderFilesCount = (filesCount: number) => {
  const filesCountText =
    "(" + filesCount + (filesCount > 1 ? " Files" : " File") + ")";

  return <ChildsAmount>{filesCountText}</ChildsAmount>;
};

const FolderName = styled.span<FolderNameStyledProps>`
  ${getTypography("body", "s", 600)};
  color: ${getColorByAlias("textPrimary")};
  padding-top: 2px;
  max-width: ${props => (props.$hasFilesInside ? 150 : 200)}px;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
`;

const renderFolderName = <TFolderData extends AnyObject>(
  folder: Folder<TFolderData>
) => {
  const hasFilesInside =
    folder.childCount !== undefined && folder.childCount > 0;

  return (
    <Container>
      <FolderIcon
        getColor={getColorByAlias("accentPrimary")}
        isFilled
        size={16}
      />
      <FolderName $hasFilesInside={hasFilesInside}>{folder.name}</FolderName>
      {hasFilesInside && renderFilesCount(folder.childCount as number)}
    </Container>
  );
};

const renderFileName = <TFileData extends AnyObject>(file: File<TFileData>) => (
  <FileName title={file.name}>{file.name}</FileName>
);

const FolderItemName = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  folderItem
}: Props<TFileData, TFolderData>) =>
  withFolderItemType.isFile(folderItem.type)
    ? renderFileName(withFolderItem.ensureIsFile(folderItem))
    : renderFolderName(withFolderItem.ensureIsFolder(folderItem));

export {FolderItemName};
