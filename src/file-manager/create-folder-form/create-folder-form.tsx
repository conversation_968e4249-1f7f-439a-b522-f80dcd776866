import type {ReactNode} from "react";
import {Controller, useFormContext} from "react-hook-form";
import type {FormState} from "react-hook-form";
import styled from "styled-components";

import TextField from "@/text-field";

import type {CreateFolderFormValues} from "./use-create-folder-form";

type Props = {
  className?: string;
  onSubmit: (
    createFolderFormValues: CreateFolderFormValues
  ) => Promise<unknown>;
  renderButtons?: (formState: FormState<CreateFolderFormValues>) => ReactNode;
};

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 32px;
`;

const CreateFolderForm = ({onSubmit, renderButtons, ...rest}: Props) => {
  const {control, formState, handleSubmit} =
    useFormContext<CreateFolderFormValues>();

  return (
    <Form {...rest} onSubmit={handleSubmit(onSubmit)}>
      <div>
        <Controller
          {...{control}}
          name="folderName"
          render={({field, fieldState: {error}}) => (
            <TextField
              {...field}
              errorMessage="Please input a folder name"
              hasError={error !== undefined}
              isRequired
              label="Folder Name"
              placeholder="Enter the Folder Name"
            />
          )}
        />
      </div>
      {renderButtons?.(formState)}
    </Form>
  );
};

export {CreateFolderForm};
