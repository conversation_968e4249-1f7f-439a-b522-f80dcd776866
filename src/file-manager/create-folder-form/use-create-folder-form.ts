import {zodResolver} from "@hookform/resolvers/zod";
import {useForm} from "react-hook-form";
import * as z from "zod";

const createFolderFormSchema = z.object({
  folderName: z
    .string({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      required_error: "The folder name is mandatory"
    })
    .min(1)
});

type CreateFolderFormValues = z.infer<typeof createFolderFormSchema>;

const useCreateFolderForm = (defaultFolderName?: string) =>
  useForm<CreateFolderFormValues>({
    mode: "onChange",
    resolver: zodResolver(createFolderFormSchema),
    defaultValues: {
      folderName: defaultFolderName ?? ""
    }
  });

export {useCreateFolderForm};

export type {CreateFolderFormValues};
