import type {ComponentProps} from "react";
import {FormProvider} from "react-hook-form";
import styled from "styled-components";

import Button from "@/button";
import Modal, {ModalHeader} from "@/modal";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import {CreateFolderForm, useCreateFolderForm} from "./create-folder-form";

type CreateFolderFormProps = ComponentProps<typeof CreateFolderForm>;

type Props = {
  folderName?: string;
  onClose: () => unknown;
  onConfirm: (folderName: string) => Promise<unknown>;
};

type CreateFolderFormButtonsRenderer = (
  onClose: Props["onClose"],
  isEditingFolder?: boolean
) => CreateFolderFormProps["renderButtons"];

const StyledButton = styled(Button)`
  margin-right: 16px;
`;

const ButtonsContainer = styled.div`
  display: block;
  margin-left: auto;
`;

const renderCreateFolderFormButtons: CreateFolderFormButtonsRenderer =
  (onClose, isEditingFolder) => formState => (
    <ButtonsContainer>
      <StyledButton onClick={onClose} size="xlarge" variant="secondary">
        Cancel
      </StyledButton>
      <Button
        disabled={formState.isSubmitting}
        size="xlarge"
        type="submit"
        variant="primary"
      >
        {isEditingFolder ? "Save Changes" : "Create Folder"}
      </Button>
    </ButtonsContainer>
  );

const Subtitle = styled.span`
  ${getTypography("body", "s")};
  color: ${getColorByAlias("textSecondary")};
`;

const Title = styled.span`
  ${getTypography("title", "s", 600)};
  color: ${getColorByAlias("textPrimary")};
`;

const TitleAndSubtitleContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const renderTitleAndSubtitle = (isEditingFolder?: boolean) => (
  <TitleAndSubtitleContainer>
    <Title>{isEditingFolder ? "Edit Folder" : "Create Folder"}</Title>
    <Subtitle>Enter the name you want to assign to the folder</Subtitle>
  </TitleAndSubtitleContainer>
);

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0 32px 24px;
`;

const StyledModalHeader = styled(ModalHeader)`
  height: 65px;
`;

const StyledModal = styled(Modal)`
  width: 540px;
`;

const FolderModal = ({folderName, onClose, onConfirm}: Props) => {
  const formContext = useCreateFolderForm(folderName);

  const isEditingFolder = folderName !== undefined;

  return (
    <StyledModal {...{onClose}}>
      <StyledModalHeader onModalClose={onClose} />
      <Container>
        {renderTitleAndSubtitle(isEditingFolder)}
        <FormProvider {...formContext}>
          <CreateFolderForm
            onSubmit={({folderName}) => onConfirm(folderName)}
            renderButtons={renderCreateFolderFormButtons(
              onClose,
              isEditingFolder
            )}
          />
        </FormProvider>
      </Container>
    </StyledModal>
  );
};

export {FolderModal};

export type {Props as FolderModalProps};
