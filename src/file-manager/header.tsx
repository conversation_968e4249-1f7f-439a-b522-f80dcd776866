import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps} from "react";
import styled from "styled-components";

import Button from "@/button";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import {BulkActions} from "./bulk-actions";
import type {BulkActionsProps} from "./bulk-actions";
import {ParentFoldersBreadcrumb} from "./parent-folders-breadcrumb";
import type {ParentFoldersBreadcrumbProps} from "./parent-folders-breadcrumb";
import {SearchField} from "./search-field";

type SearchFieldProps = ComponentProps<typeof SearchField>;

type ExposedSearchFieldProps = Omit<SearchFieldProps, "placeholder">;

// prettier-ignore
type Props<TFileData extends AnyObject, TFolderData extends AnyObject> =
  & BulkActionsProps<TFileData, TFolderData>
  & ExposedSearchFieldProps
  & ParentFoldersBreadcrumbProps<TFolderData>
  & {
    onFolderCreateClick?: () => unknown;
    title: string;
  };

const HeaderRightContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const renderHeaderRight = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  isSearching,
  onFolderCreateClick,
  onSearchChange,
  searchTerm
}: Props<TFileData, TFolderData>) => (
  <HeaderRightContainer>
    <Button onClick={onFolderCreateClick} size="medium" variant="primary">
      Create Folder
    </Button>
    <SearchField
      {...{searchTerm, isSearching, onSearchChange}}
      placeholder="Search a file or folder"
    />
  </HeaderRightContainer>
);

const Title = styled.span`
  ${getTypography("body", "m", 600)};
  color: ${getColorByAlias("textPrimary")};
`;

const renderHeaderLeft = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  currentFolder,
  onFolderClick,
  onFolderCreate,
  parentFolders,
  selectedItems,
  title,
  ...bulkActionsProps
}: Props<TFileData, TFolderData>) => {
  if (selectedItems.length) {
    return (
      <BulkActions
        {...bulkActionsProps}
        {...{selectedItems}}
        onFolderCreate={onFolderCreate}
      />
    );
  }

  if (currentFolder) {
    return (
      <ParentFoldersBreadcrumb
        {...{currentFolder, onFolderClick, parentFolders}}
      />
    );
  }

  return <Title>{title}</Title>;
};

const Container = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
`;

const Header = <TFileData extends AnyObject, TFolderData extends AnyObject>(
  props: Props<TFileData, TFolderData>
) => (
  <Container>
    {renderHeaderLeft(props)}
    {renderHeaderRight(props)}
  </Container>
);

export {Header};

export type {Props as HeaderProps};
