import type {AnyObject} from "@unlockre/utils-object/dist";
import styled from "styled-components";

import {DownloadIcon} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {OpenLocationButton} from "./open-location-button";
import type {File, FolderItem} from "./types";

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  file: File<TFileData>;
  isSearching?: boolean;
  onFileDownload: (file: File<TFileData>) => unknown;
  onParentFolderOpen: (
    folderItem: FolderItem<TFileData, TFolderData>
  ) => unknown;
};

const IconContainer = styled(UnstyledButton)`
  height: 16px;
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
`;

const FileActions = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  file,
  isSearching,
  onFileDownload,
  onParentFolderOpen
}: Props<TFileData, TFolderData>) => (
  <Container>
    {isSearching && (
      <OpenLocationButton folderItem={file} onClick={onParentFolderOpen} />
    )}
    <IconContainer
      onClick={event => {
        event.stopPropagation();
        onFileDownload(file);
      }}
    >
      <DownloadIcon getColor={getColorByAlias("accentSecondary")} size={16} />
    </IconContainer>
  </Container>
);

export {FileActions};

export type {Props as FileActionsProps};
