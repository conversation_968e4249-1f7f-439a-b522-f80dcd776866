import type {AnyObject} from "@unlockre/utils-object/dist";

import {folderItemTypes} from "./folder-item-type";
import type {FolderItem} from "./types";

const ensureIsFolder = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  folderItem: FolderItem<TFileData, TFolderData>
) => {
  if (folderItem.type !== folderItemTypes.folder) {
    throw new Error(
      "Expected folderItem to be a Folder, but instead got " + folderItem.type
    );
  }

  return folderItem;
};

const ensureIsFile = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  folderItem: FolderItem<TFileData, TFolderData>
) => {
  if (folderItem.type !== folderItemTypes.file) {
    throw new Error(
      "Expected folderItem to be a File, but instead got " + folderItem.type
    );
  }

  return folderItem;
};

export {ensureIsFolder, ensureIsFile};
