import {useState} from "react";

import type {UploadableFile} from "@/file-fields/uploadable-file";
import {useSnackbar} from "@/snackbar";

type Params = {
  onFileUpload: (file: UploadableFile) => Promise<unknown>;
};

/* eslint-disable @typescript-eslint/naming-convention */
const acceptedFileTypes = {
  "application/pdf": [".pdf"],
  "application/msword": [".doc"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
    ".docx"
  ],
  "application/vnd.ms-powerpoint": [".ppt"],
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": [
    ".pptx"
  ],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx"
  ],
  "application/zip": [".zip"],
  "image/jpg": [".jpeg", ".jpg"],
  "image/png": [".png"]
};
/* eslint-enable */

const isZipFile = (fileName: string) => {
  const fileExtension = fileName.split(".").pop();

  return fileExtension === "zip";
};

const useFileManagerFilesField = ({onFileUpload}: Params) => {
  const [uploadableFiles, setUploadableFiles] = useState<UploadableFile[]>([]);

  const {isSnackbarVisible, setSnackbarState, snackbarProps} = useSnackbar();

  const handleFileRemove = (file: UploadableFile) => {
    setUploadableFiles(currentFiles =>
      currentFiles.filter(currentFile => currentFile !== file)
    );
  };

  const handleFilesSelect = (files: UploadableFile[]) => {
    const filesBeingUploaded = files.map(file => ({
      ...file,
      isUploading: true
    }));

    setUploadableFiles(currentUploadableFiles => [
      ...currentUploadableFiles,
      ...filesBeingUploaded
    ]);

    filesBeingUploaded.forEach((fileBeingUploaded, fileIndex) =>
      onFileUpload(fileBeingUploaded)
        .then(() => {
          if (fileIndex === filesBeingUploaded.length - 1) {
            const isUploadingAZip = files.some(uploadableFile =>
              isZipFile(uploadableFile.name)
            );

            const successMessage = isUploadingAZip
              ? "The files were succesfully uploaded, the zip file may take a few seconds to be processed"
              : "The files were succesfully uploaded";

            setSnackbarState({
              message: successMessage,
              variant: "success"
            });
          }
        })
        .catch(() => {
          setSnackbarState({
            message:
              "There was an error while trying to upload the file: " +
              fileBeingUploaded.name,
            variant: "error"
          });
        })
        .finally(() => handleFileRemove(fileBeingUploaded))
    );
  };

  return {
    multiFileFieldProps: {
      acceptedFileTypes,
      files: uploadableFiles,
      onFilesSelect: handleFilesSelect,
      onFileRemove: handleFileRemove
    },
    isSnackbarVisible,
    snackbarProps
  };
};

export {useFileManagerFilesField};

export type {Params as UseFileManagerFilesFieldParams};
