import type {ChangeEvent} from "react";
import styled from "styled-components";

import {LoadingIcon, SearchIcon} from "@/icons";
import TextField from "@/text-field";
import {getColorByAlias} from "@/theme-provider/theme";

type Props = {
  isSearching?: boolean;
  onSearchChange: (event: ChangeEvent<HTMLInputElement>) => unknown;
  placeholder: string;
  searchTerm: string;
};

const iconHeight = 22;

const StyledTextField = styled(TextField)`
  background: ${getColorByAlias("backgroundWhite")};

  div {
    height: 32px;
    width: 266px;
    padding-left: 6px;
  }
`;

const SearchField = ({
  isSearching,
  onSearchChange,
  placeholder,
  searchTerm
}: Props) => (
  <StyledTextField
    {...{placeholder}}
    left={
      isSearching ? (
        <LoadingIcon
          getColor={getColorByAlias("accentPrimary")}
          size={iconHeight}
        />
      ) : (
        <SearchIcon
          getColor={getColorByAlias("textSecondary")}
          height={iconHeight}
        />
      )
    }
    onChange={onSearchChange}
    value={searchTerm}
  />
);

export {SearchField};
