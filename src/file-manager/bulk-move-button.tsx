import type {AnyObject} from "@unlockre/utils-object/dist";

import But<PERSON> from "@/button";

import {FileManagerPicker, useFileManagerPicker} from "./file-manager-picker";
import * as withFolderItemType from "./folder-item-type";
import type {Folder, FolderItem} from "./types";

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  currentPickerFolder?: Folder<TFolderData>;
  isFetchingPickerItems?: boolean;
  onBulkMove: (folder?: Folder<TFolderData>) => unknown;
  onFileManagerPickerClose?: () => unknown;
  onFolderCreate: (folderName: string) => Promise<unknown>;
  onPickerFolderClick: (folder: Folder<TFolderData>) => unknown;
  pickerItems: FolderItem<TFileData, TFolderData>[];
  pickerParentFolders: Folder<TFolderData>[];
};

const BulkMoveButton = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  currentPickerFolder,
  isFetchingPickerItems,
  onBulkMove,
  onFileManagerPickerClose,
  onFolderCreate,
  onPickerFolderClick,
  pickerItems,
  pickerParentFolders
}: Props<TFileData, TFolderData>) => {
  const {
    fileManagerPickerProps,
    isFileManagerPickerOpen,
    reference,
    toggleFileManagerPicker
  } = useFileManagerPicker();

  const handleClose = () => {
    onFileManagerPickerClose?.();
    toggleFileManagerPicker();
  };

  const handleBulkMove = (folder?: Folder<TFolderData>) => {
    onBulkMove(folder);
    toggleFileManagerPicker();
  };

  return (
    <div onClick={event => event.stopPropagation()}>
      <Button
        onClick={isFileManagerPickerOpen ? undefined : toggleFileManagerPicker}
        ref={reference}
        size="medium"
        variant="primary"
      >
        Move to
      </Button>
      {isFileManagerPickerOpen && (
        <FileManagerPicker
          {...fileManagerPickerProps}
          {...{onFolderCreate}}
          currentFolder={currentPickerFolder}
          isFetchingItems={isFetchingPickerItems}
          items={pickerItems}
          onBulkMove={handleBulkMove}
          onClose={handleClose}
          onFolderClick={onPickerFolderClick}
          parentFolders={pickerParentFolders}
          shouldShowPickerItem={folderItem =>
            withFolderItemType.isFolder(folderItem.type)
          }
        />
      )}
    </div>
  );
};

export {BulkMoveButton};

export type {Props as BulkMoveButtonProps};
