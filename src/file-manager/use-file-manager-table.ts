import type {AnyObject} from "@unlockre/utils-object/dist";
import {useCallback} from "react";

import * as withFolderItem from "./folder-item";
import * as withFolderItemType from "./folder-item-type";
import type {File, Folder, FolderItem} from "./types";

type Params<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  onFileClick?: (file: File<TFileData>) => unknown;
  onFolderClick: (folder: Folder<TFolderData>) => unknown;
};

const useFileManagerTable = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  onFileClick,
  onFolderClick
}: Params<TFileData, TFolderData>) => {
  const handleFolderItemClick = (
    folderItem: FolderItem<TFileData, TFolderData>
  ) => {
    if (withFolderItemType.isFile(folderItem.type)) {
      const file = withFolderItem.ensureIsFile(folderItem);

      return onFileClick?.(file);
    }

    const folder = withFolderItem.ensureIsFolder(folderItem);

    onFolderClick(folder);
  };

  const getItemId = useCallback(
    (item: FolderItem<TFileData, TFolderData>) => item.name + "-" + item.path,
    []
  );

  return {
    handleFolderItemClick,
    getItemId
  };
};

export {useFileManagerTable};

export type {Params as UseFileManagerTableParams};
