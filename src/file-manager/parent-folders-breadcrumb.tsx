import type {AnyObject} from "@unlockre/utils-object/dist";
import {useMemo} from "react";

import BreadcrumbList from "@/breadcrumb-list";

import type {Folder} from "./types";

type BreadcrumbItem<TFolderData extends AnyObject> = {
  folder?: Folder<TFolderData>;
  label: string;
};

type Props<TFolderData extends AnyObject> = {
  currentFolder?: Folder<TFolderData>;
  onFolderClick: (folder?: Folder<TFolderData>) => unknown;
  parentFolders: Folder<TFolderData>[];
};

const ParentFoldersBreadcrumb = <TFolderData extends AnyObject>({
  currentFolder,
  onFolderClick,
  parentFolders
}: Props<TFolderData>) => {
  const defaultBreadcrumbItem: BreadcrumbItem<TFolderData> = {
    label: "Files"
  };

  const breadcrumbItems = useMemo(
    () => [
      defaultBreadcrumbItem,
      ...parentFolders.map(parentFolder => ({
        folder: parentFolder,
        label: parentFolder.name
      }))
    ],
    [parentFolders]
  );

  return (
    <BreadcrumbList
      getBreadcrumbLabel={item => item.label}
      lastBreadcrumbLabel={
        currentFolder ? currentFolder.name : defaultBreadcrumbItem.label
      }
      onItemSelect={selectedBreadcrumbItem =>
        onFolderClick(selectedBreadcrumbItem.folder)
      }
      selectableItems={breadcrumbItems}
      size="medium"
      variant="simple"
    />
  );
};

export {ParentFoldersBreadcrumb};

export type {Props as ParentFoldersBreadcrumbProps};
