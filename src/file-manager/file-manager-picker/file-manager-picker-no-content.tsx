import styled from "styled-components";

import {CircledExclamationIcon} from "@/icons";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

const Text = styled.span`
  ${getTypography("body", "s")};
  color: ${getColorByAlias("textDisabled")};
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 42px 0;
`;

const FileManagerPickerNoContent = () => (
  <Container>
    <CircledExclamationIcon size={40} />
    <Text>This folder is empty</Text>
  </Container>
);

export {FileManagerPickerNoContent};
