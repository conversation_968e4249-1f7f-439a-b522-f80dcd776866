import {autoUpdate, flip, offset, useFloating} from "@floating-ui/react-dom";
import {useClickOutside} from "@unlockre/utils-react/dist";
import {useCallback, useState} from "react";

// eslint-disable-next-line max-statements
const useFileManagerPicker = () => {
  const [
    openFileManagerPickerButtonElement,
    setOpenFileManagerPickerButtonElement
  ] = useState<HTMLButtonElement | null>(null);

  const [fileManagerPickerElement, setFileManagerPickerElement] =
    useState<HTMLDivElement | null>(null);

  const [isFileManagerPickerOpen, setIsFileManagerPickerOpen] = useState(false);

  const toggleFileManagerPicker = useCallback(
    () => setIsFileManagerPickerOpen(currentState => !currentState),
    [setIsFileManagerPickerOpen]
  );

  const {
    floating,
    reference,
    strategy: position,
    x,
    y
  } = useFloating({
    placement: "bottom-end",
    middleware: [flip(), offset(4)],
    strategy: "fixed",
    whileElementsMounted: autoUpdate
  });

  const onOpenFileManagerPickerButtonRef = useCallback(
    (buttonElement: HTMLButtonElement | null) => {
      reference(buttonElement);
      setOpenFileManagerPickerButtonElement(buttonElement);
    },
    [reference, setOpenFileManagerPickerButtonElement]
  );

  const onFileManagerPickerRef = useCallback(
    (fileManagerPickerElement: HTMLDivElement | null) => {
      floating(fileManagerPickerElement);
      setFileManagerPickerElement(fileManagerPickerElement);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [floating, setFileManagerPickerElement]
  );

  const customFileManagerPickerProps = {
    ref: onFileManagerPickerRef,
    style: {
      position,
      top: y ?? undefined,
      left: x ?? undefined
    }
  };

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (!openFileManagerPickerButtonElement?.contains(event.target as Node)) {
        toggleFileManagerPicker();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [toggleFileManagerPicker]
  );

  useClickOutside(fileManagerPickerElement, handleClickOutside);

  return {
    fileManagerPickerProps: customFileManagerPickerProps,
    isFileManagerPickerOpen,
    reference: onOpenFileManagerPickerButtonRef,
    toggleFileManagerPicker
  };
};

export {useFileManagerPicker};
