import type {AnyObject} from "@unlockre/utils-object/dist";
import {useState} from "react";
import styled from "styled-components";

import {ChevronIcon, FolderIcon} from "@/icons";
import {chevronDirections} from "@/icons/chevron-icon";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import type {Folder} from "../types";

type Props<TFolderData extends AnyObject> = {
  folder: Folder<TFolderData>;
  onClick: () => unknown;
};

const FolderName = styled.span`
  ${getTypography("body", "xs")};
  color: ${getColorByAlias("textPrimary")};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 165px;
`;

const IconAndNameContainer = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
`;

const Container = styled(UnstyledButton)`
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  width: 100%;
`;

const FileManagerPickerFolderItem = <TFolderData extends AnyObject>({
  folder,
  onClick
}: Props<TFolderData>) => {
  const [isMouseHover, setIsMouseHover] = useState(false);

  return (
    <Container
      {...{onClick}}
      onMouseEnter={() => setIsMouseHover(true)}
      onMouseLeave={() => setIsMouseHover(false)}
    >
      <IconAndNameContainer>
        <FolderIcon getColor={getColorByAlias("accentPrimary")} size={16} />
        <FolderName title={folder.name}>{folder.name}</FolderName>
      </IconAndNameContainer>
      {isMouseHover && (
        <ChevronIcon
          direction={chevronDirections.right}
          getColor={getColorByAlias("accentSecondary")}
          height={12}
        />
      )}
    </Container>
  );
};

export {FileManagerPickerFolderItem};
