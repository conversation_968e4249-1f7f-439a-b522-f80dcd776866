import type {AnyObject} from "@unlockre/utils-object/dist";
import colorAlpha from "color-alpha";
import {forwardRef, useState} from "react";
import type {CSSProperties, ReactElement, Ref} from "react";
import {FormProvider} from "react-hook-form";
import styled from "styled-components";

import Portal from "@/portal";
import {getColor, getColorByAlias} from "@/theme-provider/theme";

import {useCreateFolderForm} from "../create-folder-form";
import type {CreateFolderFormValues} from "../create-folder-form";
import type {File, Folder, FolderItem} from "../types";

import {FileManagerPickerContent} from "./file-manager-picker-content";
import {FileManagerPickerFooter} from "./file-manager-picker-footer";
import {FileManagerPickerHeader} from "./file-manager-picker-header";

type ContainerElement = HTMLDivElement;

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  currentFolder?: Folder<TFolderData>;
  isFetchingItems?: boolean;
  items: FolderItem<TFileData, TFolderData>[];
  onBulkMove: (folder?: Folder<TFolderData>) => unknown;
  onClose: () => unknown;
  onFileClick?: (file: File<TFileData>) => unknown;
  onFolderClick: (folder: Folder<TFolderData>) => unknown;
  onFolderCreate: (folderName: string) => Promise<unknown>;
  parentFolders: Folder<TFolderData>[];
  shouldShowPickerItem?: (
    folderItem: FolderItem<TFileData, TFolderData>
  ) => boolean;
  style?: CSSProperties;
  withoutFooter?: boolean;
};

type FileManagerPickerComponent = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  props: Props<TFileData, TFolderData> & {ref?: Ref<ContainerElement>}
) => ReactElement;

const Container = styled.div`
  background: ${getColorByAlias("backgroundWhite")};
  border: 1px solid ${getColor("gray", "100")};
  border-radius: 10px;
  box-shadow: 0px 4px 58px
    ${props => colorAlpha(getColor("blue", "900")(props), 0.06)};
  width: 334px;
`;

const FileManagerPickerRenderer = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  {
    currentFolder,
    isFetchingItems,
    items,
    onBulkMove,
    onClose,
    onFileClick,
    onFolderClick,
    onFolderCreate,
    parentFolders,
    shouldShowPickerItem,
    style,
    withoutFooter
  }: Props<TFileData, TFolderData>,
  ref: Ref<ContainerElement>
) => {
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  const handleGoBack = () => {
    const prevFolder = parentFolders[parentFolders.length - 1];

    onFolderClick(prevFolder);
    setIsCreatingFolder(false);
  };

  const formContext = useCreateFolderForm();

  const handleFolderCreateAndResetValue = (
    createFolderFormValues: CreateFolderFormValues
  ) => {
    formContext.reset();
    return onFolderCreate(createFolderFormValues.folderName);
  };

  return (
    <Portal>
      <Container {...{ref, style}}>
        <FileManagerPickerHeader
          {...{isCreatingFolder, onClose}}
          folderName={currentFolder?.name}
          onGoBack={handleGoBack}
        />
        <FormProvider {...formContext}>
          <FileManagerPickerContent
            {...{
              isCreatingFolder,
              isFetchingItems,
              items,
              onFileClick,
              onFolderClick,
              shouldShowPickerItem
            }}
            onFolderCreate={handleFolderCreateAndResetValue}
          />
        </FormProvider>
        {!withoutFooter && (
          <FileManagerPickerFooter
            {...{isCreatingFolder}}
            isCreateFolderButtonDisabled={
              !formContext.formState.isDirty || !formContext.formState.isValid
            }
            onFolderCreate={formContext.handleSubmit(
              handleFolderCreateAndResetValue
            )}
            onMoveHere={() => onBulkMove(currentFolder)}
            onShowFolderCreation={() => setIsCreatingFolder(true)}
          />
        )}
      </Container>
    </Portal>
  );
};

const FileManagerPicker = forwardRef(
  FileManagerPickerRenderer
) as FileManagerPickerComponent;

export {FileManagerPicker};
