import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ReactElement} from "react";
import styled from "styled-components";

import ShadowedScroll from "@/shadowed-scroll";
import Spinner from "@/spinner";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledUl} from "@/unstyled";

import {CreateFolderForm} from "../create-folder-form";
import type {CreateFolderFormValues} from "../create-folder-form/use-create-folder-form";
import * as withFolderItem from "../folder-item";
import * as withFolderItemType from "../folder-item-type";
import type {File, Folder, FolderItem} from "../types";

import {FileManagerPickerFolderItem} from "./file-manager-picker-folder-item";
import {FileManagerPickerNoContent} from "./file-manager-picker-no-content";
import {ItemContainer} from "./item-container";

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  isCreatingFolder?: boolean;
  isFetchingItems?: boolean;
  items: FolderItem<TFileData, TFolderData>[];
  onFileClick?: (file: File<TFileData>) => unknown;
  onFolderClick: (folder: Folder<TFolderData>) => unknown;
  onFolderCreate: (
    createFolderFormValues: CreateFolderFormValues
  ) => Promise<unknown>;
  renderFileRight?: (file: File<TFileData>) => ReactElement;
};

const FileName = styled.span`
  ${getTypography("body", "xs", 400)};
  color: ${getColorByAlias("textPrimary")};

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
`;

const FileItemContainer = styled(ItemContainer)`
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  width: 100%;
`;

const renderFolderItem = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  folderItem: FolderItem<TFileData, TFolderData>,
  onFolderClick: (folder: Folder<TFolderData>) => unknown,
  onFileClick?: (file: File<TFileData>) => unknown,
  renderFileRight?: (file: File<TFileData>) => ReactElement
) => {
  if (withFolderItemType.isFolder(folderItem.type)) {
    const folder = withFolderItem.ensureIsFolder(folderItem);

    return (
      <ItemContainer key={folderItem.path}>
        <FileManagerPickerFolderItem
          {...{folder}}
          onClick={() => onFolderClick(folder)}
        />
      </ItemContainer>
    );
  }

  const file = withFolderItem.ensureIsFile(folderItem);

  return (
    <FileItemContainer
      key={folderItem.path}
      onClick={() => onFileClick?.(file)}
    >
      <FileName title={file.name}>{file.name}</FileName>
      {renderFileRight?.(file)}
    </FileItemContainer>
  );
};

const containerHeight = 164;

const StyledShadowedScroll = styled(ShadowedScroll)`
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  max-height: ${containerHeight}px;
`;

const renderFolderItems = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  items,
  onFileClick,
  onFolderClick,
  renderFileRight
}: Props<TFileData, TFolderData>) => (
  <StyledShadowedScroll>
    <UnstyledUl>
      {items.map(folderItem =>
        renderFolderItem(
          folderItem,
          onFolderClick,
          onFileClick,
          renderFileRight
        )
      )}
    </UnstyledUl>
  </StyledShadowedScroll>
);

const StyledCreateFolderForm = styled(CreateFolderForm)`
  padding: 8px 16px 0;
`;

const renderCreatingFolderContent = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  onFolderCreate
}: Props<TFileData, TFolderData>) => (
  <StyledCreateFolderForm onSubmit={onFolderCreate} />
);

const SpinnerContainer = styled.div`
  align-items: center;
  background: ${getColorByAlias("backgroundWhite")};
  display: flex;
  height: ${containerHeight}px;
  justify-content: center;
  opacity: 0.7;
  position: absolute;
  width: 100%;
  z-index: 10;
`;

const renderFetchingItemsContent = () => (
  <SpinnerContainer>
    <Spinner size={45} />
  </SpinnerContainer>
);

const renderContent = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  props: Props<TFileData, TFolderData>
) => {
  if (props.isFetchingItems) {
    return renderFetchingItemsContent();
  }

  if (props.isCreatingFolder) {
    return renderCreatingFolderContent(props);
  }

  return props.items.length > 0 ? (
    renderFolderItems(props)
  ) : (
    <FileManagerPickerNoContent />
  );
};

const Container = styled.div`
  height: ${containerHeight}px;
  position: relative;
`;

const FileManagerPickerContent = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  props: Props<TFileData, TFolderData>
) => <Container>{renderContent(props)}</Container>;

export {FileManagerPickerContent};
