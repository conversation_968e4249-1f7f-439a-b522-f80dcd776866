import styled from "styled-components";

import {ChevronIcon, CloseIcon} from "@/icons";
import {chevronDirections} from "@/icons/chevron-icon";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type Props = {
  folderName?: string;
  isCreatingFolder?: boolean;
  onClose: () => unknown;
  onGoBack: () => unknown;
};

const HeaderTitle = styled.span`
  ${getTypography("body", "s", 600)};
  color: ${getColorByAlias("textPrimary")};
`;

const LeftSideContainer = styled.div`
  align-items: center;
  display: flex;
  gap: 16px;
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 16px;
`;

const defaultTitle = "Files";

const getTitle = ({folderName, isCreatingFolder}: Props) => {
  if (isCreatingFolder) {
    return "Create folder";
  }

  return folderName || defaultTitle;
};

const FileManagerPickerHeader = (props: Props) => {
  const title = getTitle(props);

  const isNestedOrCreatingFolder = title !== defaultTitle;

  return (
    <Container>
      <LeftSideContainer>
        {isNestedOrCreatingFolder && (
          <UnstyledButton onClick={props.onGoBack}>
            <ChevronIcon
              direction={chevronDirections.left}
              getColor={getColorByAlias("accentSecondary")}
              height={12}
            />
          </UnstyledButton>
        )}
        <HeaderTitle>{title}</HeaderTitle>
      </LeftSideContainer>
      <UnstyledButton onClick={props.onClose}>
        <CloseIcon getColor={getColorByAlias("accentSecondary")} size={16} />
      </UnstyledButton>
    </Container>
  );
};

export {FileManagerPickerHeader};
