import styled from "styled-components";

import Button from "@/button";

type Props = {
  isCreateFolderButtonDisabled?: boolean;
  isCreatingFolder?: boolean;
  onFolderCreate: () => Promise<unknown>;
  onMoveHere: () => unknown;
  onShowFolderCreation: () => unknown;
};

const Container = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 16px;
`;

const FileManagerPickerFooter = ({
  isCreateFolderButtonDisabled,
  isCreatingFolder,
  onFolderCreate,
  onMoveHere,
  onShowFolderCreation
}: Props) => (
  <Container>
    {isCreatingFolder ? (
      <Button
        disabled={isCreateFolderButtonDisabled}
        onClick={onFolderCreate}
        size="medium"
        variant="primary"
      >
        Create folder
      </Button>
    ) : (
      <>
        <Button
          onClick={onShowFolderCreation}
          size="medium"
          variant="transparent"
        >
          Create folder
        </Button>
        <Button onClick={onMoveHere} size="medium" variant="primary">
          Move here
        </Button>
      </>
    )}
  </Container>
);

export {FileManagerPickerFooter};
