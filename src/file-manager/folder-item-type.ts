const folderItemTypes = {
  file: "FILE",
  folder: "FOLDER"
} as const;

type FolderItemType = (typeof folderItemTypes)[keyof typeof folderItemTypes];

const isFile = (folderItemType: FolderItemType) =>
  folderItemType === folderItemTypes.file;

const isFolder = (folderItemType: FolderItemType) =>
  folderItemType === folderItemTypes.folder;

export {folderItemTypes, isFile, isFolder};

export type {FolderItemType};
