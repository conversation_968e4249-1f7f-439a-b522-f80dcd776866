import styled from "styled-components";

import {MultiFileField} from "@/file-fields";
import Snackbar from "@/snackbar";
import {getColorByAlias} from "@/theme-provider/theme";

import {useFileManagerFilesField} from "./use-file-manager-files-field";
import type {UseFileManagerFilesFieldParams} from "./use-file-manager-files-field";

// prettier-ignore
type Props = 
  & UseFileManagerFilesFieldParams
  & {
    canUploadItems?: boolean;
  };

const Container = styled.div`
  background: ${getColorByAlias("backgroundWhite")};
  padding: 24px 32px 16px;
`;

const FileManagerFilesField = ({canUploadItems, onFileUpload}: Props) => {
  const {isSnackbarVisible, multiFileFieldProps, snackbarProps} =
    useFileManagerFilesField({
      onFileUpload
    });

  return (
    <Container>
      <MultiFileField
        {...multiFileFieldProps}
        isDisabled={!canUploadItems}
        onFileDownload={() => console.log("download file")}
      />
      {isSnackbarVisible && snackbarProps?.message && (
        <Snackbar {...snackbarProps} />
      )}
    </Container>
  );
};

export {FileManagerFilesField};

export type {Props as FileManagerFilesFieldProps};
