import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import {BulkDeleteButton} from "./bulk-delete-button";
import {BulkMoveButton} from "./bulk-move-button";
import type {BulkMoveButtonProps} from "./bulk-move-button";
import type {FolderItem} from "./types";

type BulkDeleteButtonProps = ComponentProps<typeof BulkDeleteButton>;

// prettier-ignore
type Props<TFileData extends AnyObject, TFolderData extends AnyObject> =
  & BulkDeleteButtonProps
  & BulkMoveButtonProps<TFileData, TFolderData>
  & {
    selectedItems: FolderItem<TFileData, TFolderData>[];
  };

const presentSelectedItems = (selectedItemsQuantity: number) =>
  selectedItemsQuantity +
  (selectedItemsQuantity > 1 ? " Items " : " Item ") +
  "Selected";

const SelectedItems = styled.span`
  ${getTypography("body", "s")};
  color: ${getColorByAlias("textPrimary")};
`;

const ButtonsContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  gap: 16px;
`;

const BulkActions = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  canDeleteItems,
  onBulkDeleteClick,
  selectedItems,
  ...bulkMoveButtonProps
}: Props<TFileData, TFolderData>) => (
  <Container>
    <ButtonsContainer>
      <BulkMoveButton {...bulkMoveButtonProps} />
      <BulkDeleteButton {...{canDeleteItems, onBulkDeleteClick}} />
    </ButtonsContainer>
    {selectedItems.length > 0 && (
      <SelectedItems>
        {presentSelectedItems(selectedItems.length)}
      </SelectedItems>
    )}
  </Container>
);

export {BulkActions};

export type {Props as BulkActionsProps};
