import {fromIso8601Date} from "@unlockre/utils-date/dist";
import {formatDate} from "@unlockre/utils-formatting/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";
import prettyBytes from "pretty-bytes";

import {VirtualTable} from "@/virtual-table";
import type {TableColumn, VirtualTableProps} from "@/virtual-table";

import {EmptyState} from "./empty-state";
import {FileManagerTableRowActions} from "./file-manager-table-row-actions";
import * as withFolderItem from "./folder-item";
import {FolderItemName} from "./folder-item-name";
import * as withFolderItemType from "./folder-item-type";
import type {File, Folder, FolderItem} from "./types";
import {useFileManagerTable} from "./use-file-manager-table";
import type {UseFileManagerTableParams} from "./use-file-manager-table";

type FileManagerTableColumn<
  TFileData extends AnyObject,
  TFolderData extends AnyObject
> = TableColumn<FolderItem<TFileData, TFolderData>>;

type ExposedVirtualTableProps<
  TFileData extends AnyObject,
  TFolderData extends AnyObject
> = Pick<
  VirtualTableProps<FolderItem<TFileData, TFolderData>>,
  "onSelectedItemsChange" | "selectedItems"
>;

// prettier-ignore
type Props<TFileData extends AnyObject, TFolderData extends AnyObject> =
  & ExposedVirtualTableProps<TFileData, TFolderData>
  & UseFileManagerTableParams<TFileData, TFolderData>
  & {
    folderItems: FolderItem<TFileData, TFolderData>[];
    innerColumns?: FileManagerTableColumn<TFileData, TFolderData>[];
    isFetchingItems?: boolean
    isSearching?: boolean;
    onFileDownload: (file: File<TFileData>) => unknown;
    onFolderEditClick: (folder: Folder<TFolderData>) => unknown;
    onParentFolderOpen: (folderItem: FolderItem<TFileData, TFolderData>) => unknown;
  };

const formatFolderItemSize = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  folderItem: FolderItem<TFileData, TFolderData>
) => {
  if (withFolderItemType.isFile(folderItem.type)) {
    const file = withFolderItem.ensureIsFile(folderItem);

    return prettyBytes(file.sizeInBytes);
  }

  return "";
};

const formatFolderItemLastModified = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  folderItem: FolderItem<TFileData, TFolderData>
) => {
  if (withFolderItemType.isFile(folderItem.type)) {
    const file = withFolderItem.ensureIsFile(folderItem);

    return formatDate(fromIso8601Date(file.lastModified));
  }

  return "";
};

const getColumns = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  innerColumns,
  isSearching,
  onFileDownload,
  onFolderEditClick,
  onParentFolderOpen
}: Props<TFileData, TFolderData>): TableColumn<
  FolderItem<TFileData, TFolderData>
>[] => [
  {
    isSortable: true,
    key: "name",
    title: "Name",
    width: 300,
    renderTableCell: folderItem => <FolderItemName {...{folderItem}} />
  },
  {
    isSortable: true,
    key: "lastModified",
    title: "Last Modified",
    format: formatFolderItemLastModified,
    width: 120
  },
  {
    key: "size",
    title: "File Size",
    format: formatFolderItemSize,
    width: 100
  },
  ...(innerColumns ?? []),
  {
    key: "actions",
    title: "",
    renderTableCell: folderItem => (
      <FileManagerTableRowActions
        {...{
          folderItem,
          isSearching,
          onFileDownload,
          onFolderEditClick,
          onParentFolderOpen
        }}
      />
    ),
    width: isSearching ? 200 : 48
  }
];

const isRowClickable = () => true;
const isRowSelectable = () => true;

const FileManagerTable = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>(
  props: Props<TFileData, TFolderData>
) => {
  const {
    folderItems,
    isFetchingItems,
    isSearching,
    onFileClick,
    onFolderClick,
    onSelectedItemsChange,
    selectedItems
  } = props;

  const {getItemId, handleFolderItemClick} = useFileManagerTable({
    onFileClick,
    onFolderClick
  });

  const columns = getColumns(props);

  return (
    <VirtualTable
      {...{
        columns,
        getItemId,
        isRowClickable,
        isRowSelectable,
        onSelectedItemsChange,
        selectedItems
      }}
      height={500}
      isLoading={isFetchingItems}
      items={folderItems}
      onRowClick={handleFolderItemClick}
      renderEmptyElement={() =>
        !isFetchingItems ? <EmptyState {...{isSearching}} /> : null
      }
    />
  );
};

export {FileManagerTable};

export type {Props as FileManagerTableProps};
