import styled from "styled-components";

import Button from "@/button";
import {InfoIcon} from "@/icons";
import Modal, {ModalHeader} from "@/modal";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

type Props = {
  onBulkDelete: () => unknown;
  onClose: () => unknown;
};

const ActionsContainer = styled.div`
  margin-top: 32px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
`;

const Title = styled.h2`
  ${getTypography("title", "s", 600)};
  color: ${getColorByAlias("textPrimary")};
`;

const StyledInfoIcon = styled(InfoIcon)`
  display: block;
  margin-bottom: 25px;
`;

const Container = styled.div`
  padding: 0 32px 40px;
`;

const StyledModal = styled(Modal)`
  width: 540px;
`;

const BulkDeleteModal = ({onBulkDelete, onClose}: Props) => (
  <StyledModal {...{onClose}}>
    <ModalHeader onModalClose={onClose} />
    <Container>
      <StyledInfoIcon getColor={getColorByAlias("accentTertiary")} size={46} />
      <Title>Are you sure that you want to delete the selected items?</Title>
      <ActionsContainer>
        <Button onClick={onClose} size="xlarge" variant="secondary">
          Cancel
        </Button>
        <Button onClick={onBulkDelete} size="xlarge" variant="primary">
          Delete
        </Button>
      </ActionsContainer>
    </Container>
  </StyledModal>
);

export {BulkDeleteModal};
