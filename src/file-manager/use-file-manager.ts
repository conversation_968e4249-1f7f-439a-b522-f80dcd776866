import type {AnyObject} from "@unlockre/utils-object/dist";
import {useState} from "react";
import type {ChangeEvent} from "react";

import type {Folder} from "./types";
import useDebounce from "./use-debounce";

// eslint-disable-next-line max-statements
const useFileManager = <TFolderData extends AnyObject>() => {
  const [currentPickerFolder, setCurrentPickerFolder] = useState<
    Folder<TFolderData> | undefined
  >(undefined);

  const [currentFolder, setCurrentFolder] = useState<
    Folder<TFolderData> | undefined
  >(undefined);

  const [inEditionFolder, setInEditionFolder] = useState<
    Folder<TFolderData> | undefined
  >(undefined);

  const [searchTerm, setSearchTerm] = useState<string>("");

  const debouncedSearchTerm = useDebounce({
    value: searchTerm,
    delay: 500
  });

  const onPickerFolderClick = (folder?: Folder<TFolderData>) => {
    setCurrentPickerFolder(folder);
  };

  const onFolderClick = (folder?: Folder<TFolderData>) => {
    setCurrentFolder(folder);
  };

  const onFolderEditClick = (folder: Folder<TFolderData>) => {
    setInEditionFolder(folder);
  };

  const onSearchChange = (e: ChangeEvent<HTMLInputElement>) =>
    setSearchTerm(e.target.value);

  const clearSearchTerm = () => setSearchTerm("");

  return {
    clearSearchTerm,
    currentFolder,
    currentPickerFolder,
    debouncedSearchTerm,
    onFolderEditClick,
    inEditionFolder,
    onFolderClick,
    onPickerFolderClick,
    onSearchChange,
    searchTerm
  };
};

export {useFileManager};
