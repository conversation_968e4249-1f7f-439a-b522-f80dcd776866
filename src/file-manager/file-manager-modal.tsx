import type {AnyObject} from "@unlockre/utils-object/dist";

import {BulkDeleteModal} from "./bulk-delete-modal";
import {fileManagerModalNames} from "./file-manager-modal-name";
import type {FileManagerModalName} from "./file-manager-modal-name";
import {FolderModal} from "./folder-modal";
import type {Folder} from "./types";

type Props<TFolderData extends AnyObject> = {
  inEditionFolder?: Folder<TFolderData>;
  modalName: FileManagerModalName;
  onBulkDelete: () => unknown;
  onFolderCreate: (folderName: string) => Promise<unknown>;
  onFolderEdit: (folderName: string) => Promise<unknown>;
  onModalClose: () => unknown;
};

const FileManagerModal = <TFolderData extends AnyObject>({
  inEditionFolder,
  modalName,
  onBulkDelete,
  onFolderCreate,
  onFolderEdit,
  onModalClose
}: Props<TFolderData>) => {
  switch (modalName) {
    case fileManagerModalNames.bulkDelete:
      return <BulkDeleteModal {...{onBulkDelete}} onClose={onModalClose} />;

    case fileManagerModalNames.createFolder:
      return <FolderModal onClose={onModalClose} onConfirm={onFolderCreate} />;

    case fileManagerModalNames.editFolder:
      return (
        <FolderModal
          folderName={inEditionFolder?.name}
          onClose={onModalClose}
          onConfirm={onFolderEdit}
        />
      );
  }
};

export {FileManagerModal};

export type {Props as FileManagerModalProps};
