import type {AnyObject} from "@unlockre/utils-object/dist";
import type {MouseEvent} from "react";

import Button from "@/button";

import * as withFolderItemType from "./folder-item-type";
import type {FolderItem} from "./types";

type Props<TFileData extends AnyObject, TFolderData extends AnyObject> = {
  folderItem: FolderItem<TFileData, TFolderData>;
  onClick: (folderItem: FolderItem<TFileData, TFolderData>) => unknown;
};

const OpenLocationButton = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  folderItem,
  onClick
}: Props<TFileData, TFolderData>) => {
  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    onClick(folderItem);
  };

  return (
    <Button onClick={handleClick} size="small" variant="transparent">
      {withFolderItemType.isFile(folderItem.type)
        ? "Open file location"
        : "Open folder"}
    </Button>
  );
};

export {OpenLocationButton};
