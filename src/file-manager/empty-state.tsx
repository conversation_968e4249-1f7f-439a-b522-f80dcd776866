import styled from "styled-components";

import {CircledExclamationIcon} from "@/icons";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

type Props = {
  isSearching?: boolean;
};

const Subtitle = styled.span`
  ${getTypography("body", "s", 400)};
  color: ${getColorByAlias("textDisabled")};
`;

const Title = styled.span`
  ${getTypography("body", "s", 600)};
  color: ${getColorByAlias("textPrimary")};
`;

const renderNonSearchingEmptyState = () => <Title>No files uploaded yet</Title>;

const renderSearchingEmptyState = () => (
  <>
    <Title>None of the file or folder names match with your search</Title>
    <Subtitle>Please, try another search</Subtitle>
  </>
);

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin: 40px 0 24px;
`;

const EmptyState = ({isSearching}: Props) => (
  <Container>
    <CircledExclamationIcon size={40} />
    {isSearching ? renderSearchingEmptyState() : renderNonSearchingEmptyState()}
  </Container>
);

export {EmptyState};
