import type {AnyObject} from "@unlockre/utils-object/dist";

import {FileActions} from "./file-actions";
import type {FileActionsProps} from "./file-actions";
import {FolderActions} from "./folder-actions";
import type {FolderActionsProps} from "./folder-actions";
import * as withFolderItem from "./folder-item";
import * as withFolderItemType from "./folder-item-type";
import type {FolderItem} from "./types";

type ExposedFileActionsProps<
  TFileData extends AnyObject,
  TFolderData extends AnyObject
> = Omit<FileActionsProps<TFileData, TFolderData>, "file">;

type ExposedFolderActionsProps<
  TFileData extends AnyObject,
  TFolderData extends AnyObject
> = Omit<FolderActionsProps<TFileData, TFolderData>, "folder">;

// prettier-ignore
type Props<TFileData extends AnyObject, TFolderData extends AnyObject> =
  & ExposedFileActionsProps<TFileData, TFolderData>
  & ExposedFolderActionsProps<TFileData, TFolderData>
  & {
    folderItem: FolderItem<TFileData, TFolderData>;
  };

const FileManagerTableRowActions = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  folderItem,
  isSearching,
  onFileDownload,
  onFolderEditClick,
  onParentFolderOpen
}: Props<TFileData, TFolderData>) => {
  if (withFolderItemType.isFile(folderItem.type)) {
    const file = withFolderItem.ensureIsFile(folderItem);

    return (
      <FileActions
        {...{file, isSearching, onFileDownload, onParentFolderOpen}}
      />
    );
  }

  const folder = withFolderItem.ensureIsFolder(folderItem);

  return (
    <FolderActions
      {...{folder, isSearching, onFolderEditClick, onParentFolderOpen}}
    />
  );
};

export {FileManagerTableRowActions};
