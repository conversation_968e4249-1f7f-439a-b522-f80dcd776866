import type {AnyObject} from "@unlockre/utils-object/dist";
import {useState} from "react";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

import {FileManagerFilesField} from "./file-manager-files-field";
import type {FileManagerFilesFieldProps} from "./file-manager-files-field";
import {FileManagerModal} from "./file-manager-modal";
import type {FileManagerModalProps} from "./file-manager-modal";
import {fileManagerModalNames} from "./file-manager-modal-name";
import type {FileManagerModalName} from "./file-manager-modal-name";
import {FileManagerTable} from "./file-manager-table";
import type {FileManagerTableProps} from "./file-manager-table";
import {Header} from "./header";
import type {HeaderProps} from "./header";
import type {Folder, FolderItem} from "./types";

type ExposedHeaderProps<
  TFileData extends AnyObject,
  TFolderData extends AnyObject
> = Omit<
  HeaderProps<TFileData, TFolderData>,
  | "onBulkDeleteClick"
  | "onFolderClick"
  | "onFolderCreate"
  | "onFolderCreateClick"
  | "selectedItems"
  | "shouldClearSearchValue"
>;

type ExposedFileManagerTableProps<
  TFileData extends AnyObject,
  TFolderData extends AnyObject
> = Omit<
  FileManagerTableProps<TFileData, TFolderData>,
  "folderItems" | "isSearching" | "onFolderClick" | "selectedItems"
>;

type ExposedFileManagerModalProps<TFolderData extends AnyObject> = Omit<
  FileManagerModalProps<TFolderData>,
  "modalName" | "onFolderCreate" | "onModalClose"
>;

// prettier-ignore
type Props<TFileData extends AnyObject, TFolderData extends AnyObject> =
  & ExposedFileManagerModalProps<TFolderData>
  & ExposedFileManagerTableProps<TFileData, TFolderData>
  & ExposedHeaderProps<TFileData, TFolderData>
  & FileManagerFilesFieldProps
  & {
    isSearching?: boolean;
    items: FolderItem<TFileData, TFolderData>[];
    onFolderClick: (folder?: Folder<TFolderData>) => unknown;
    onFolderCreate: FileManagerModalProps<TFolderData>["onFolderCreate"];
    selectedItems: FolderItem<TFileData, TFolderData>[];
  };

const TableAndFilesFieldContainer = styled.div`
  border: 1px solid ${getColor("gray", "070")};
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FileManager = <
  TFileData extends AnyObject,
  TFolderData extends AnyObject
>({
  canDeleteItems,
  canUploadItems,
  currentFolder,
  currentPickerFolder,
  inEditionFolder,
  innerColumns,
  isFetchingItems,
  isFetchingPickerItems,
  isSearching,
  items,
  onBulkDelete,
  onBulkMove,
  onFileClick,
  onFileDownload,
  onFileManagerPickerClose,
  onFileUpload,
  onFolderClick,
  onFolderCreate,
  onFolderEdit,
  onFolderEditClick,
  onParentFolderOpen,
  onPickerFolderClick,
  onSearchChange,
  onSelectedItemsChange,
  parentFolders,
  pickerItems,
  pickerParentFolders,
  searchTerm,
  selectedItems,
  title
}: Props<TFileData, TFolderData>) => {
  const [currentVisibleModal, setCurrentVisibleModal] = useState<
    FileManagerModalName | undefined
  >(undefined);

  const handleModalClose = () => setCurrentVisibleModal(undefined);

  return (
    <Container>
      <Header
        {...{
          canDeleteItems,
          currentFolder,
          currentPickerFolder,
          isFetchingPickerItems,
          isSearching,
          onBulkMove,
          onFileManagerPickerClose,
          onFolderClick,
          onFolderCreate,
          onPickerFolderClick,
          parentFolders,
          pickerParentFolders,
          pickerItems,
          selectedItems,
          onSearchChange,
          searchTerm,
          title
        }}
        onBulkDeleteClick={() =>
          setCurrentVisibleModal(fileManagerModalNames.bulkDelete)
        }
        onFolderCreateClick={() =>
          setCurrentVisibleModal(fileManagerModalNames.createFolder)
        }
      />
      <TableAndFilesFieldContainer>
        <FileManagerTable
          {...{
            innerColumns,
            isFetchingItems,
            isSearching,
            onFolderClick,
            onFileClick,
            onFileDownload,
            onParentFolderOpen,
            onSelectedItemsChange,
            selectedItems
          }}
          folderItems={items}
          onFolderEditClick={folder => {
            onFolderEditClick(folder);
            setCurrentVisibleModal(fileManagerModalNames.editFolder);
          }}
        />
        {!isSearching && (
          <FileManagerFilesField {...{canUploadItems, onFileUpload}} />
        )}
      </TableAndFilesFieldContainer>
      {currentVisibleModal !== undefined && (
        <FileManagerModal
          {...{
            inEditionFolder
          }}
          modalName={currentVisibleModal}
          onBulkDelete={() => {
            onBulkDelete();
            handleModalClose();
          }}
          onFolderCreate={folderName =>
            onFolderCreate(folderName).then(handleModalClose)
          }
          onFolderEdit={folderName =>
            onFolderEdit(folderName).then(handleModalClose)
          }
          onModalClose={handleModalClose}
        />
      )}
    </Container>
  );
};

export {FileManager};
