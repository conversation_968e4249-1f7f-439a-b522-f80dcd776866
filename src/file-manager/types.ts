import type {AnyObject} from "@unlockre/utils-object/dist";

import type {FolderItemType, folderItemTypes} from "./folder-item-type";

type AbstractFolderItem<
  TType extends FolderItemType,
  TData extends AnyObject
> = {
  data?: TData;
  name: string;
  path: string;
  type: TType;
};

type File<TFileData extends AnyObject> = AbstractFolderItem<
  typeof folderItemTypes.file,
  TFileData
> & {
  lastModified: string;
  sizeInBytes: number;
};

type Folder<TFolderData extends AnyObject> = AbstractFolderItem<
  typeof folderItemTypes.folder,
  TFolderData
> & {
  childCount?: number;
};

type FolderItem<TFileData extends AnyObject, TFolderData extends AnyObject> =
  | File<TFileData>
  | Folder<TFolderData>;

export type {File, Folder, FolderItem};
