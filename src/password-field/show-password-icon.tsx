import IconPath from "./icon-path";

type Props = {
  isDisabled?: boolean;
  size: number;
};

const ShowPasswordIcon = ({isDisabled, size}: Props) => (
  <svg
    fill="none"
    height={size}
    viewBox="0 0 16 16"
    width={size}
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_4208_40593)">
      <IconPath
        $isDisabled={isDisabled}
        d="M0.666687 8.00002C0.666687 8.00002 3.33335 2.66669 8.00002 2.66669C12.6667 2.66669 15.3334 8.00002 15.3334 8.00002C15.3334 8.00002 12.6667 13.3334 8.00002 13.3334C3.33335 13.3334 0.666687 8.00002 0.666687 8.00002Z"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.3595"
      />
      <IconPath
        $isDisabled={isDisabled}
        d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.3595"
      />
    </g>
    <defs>
      <clipPath id="clip0_4208_40593">
        <rect fill="white" height="16" width="16" />
      </clipPath>
    </defs>
  </svg>
);

export default ShowPasswordIcon;
