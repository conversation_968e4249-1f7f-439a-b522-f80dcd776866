import {forwardRef, useState} from "react";
import type {ComponentProps, ComponentRef} from "react";

import {TextInput} from "@/text-field";

import PasswordVisibilityButton from "./password-visibility-button";

type TextInputProps = ComponentProps<typeof TextInput>;

type TextInputElement = ComponentRef<typeof TextInput>;

type Props = Omit<TextInputProps, "left" | "right">;

const PasswordInput = forwardRef<TextInputElement, Props>(
  ({isDisabled, ...rest}, ref) => {
    const [isPasswordVisible, setIsPasswordVisible] = useState(false);

    return (
      <TextInput
        {...{ref, isDisabled}}
        {...rest}
        right={
          <PasswordVisibilityButton
            {...{isDisabled, isPasswordVisible}}
            onToggle={setIsPasswordVisible}
          />
        }
        type={isPasswordVisible ? "text" : "password"}
      />
    );
  }
);

export default PasswordInput;
