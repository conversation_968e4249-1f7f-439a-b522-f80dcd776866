import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

type StyledProps = {
  $isDisabled?: boolean;
};

const getNotDisabledColor = getColor("gray", "900");

const getDisabledColor = getColor("gray", "250");

const IconPath = styled.path<StyledProps>`
  stroke: ${props =>
    props.$isDisabled ? getDisabledColor : getNotDisabledColor};
`;

export default IconPath;
