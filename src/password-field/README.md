# PasswordField

## About

This field allows the user to enter a password and show or hide it clicking on a button inside of the component.

### Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import PasswordField from "@unlockre/components-library/dist/password-field";
import {useState} from "react";

const MyApp = () => {
 const [password, setPassword] = useState("");

 return (
	<ThemeProvider>
    <PasswordField
      label="Password"
      onChange={setPassword}
      placeholder="Enter your Password"
      value={password}
    />
	</ThemeProvider>
 );
}

export default MyApp;
```