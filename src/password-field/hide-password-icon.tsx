import IconPath from "./icon-path";

type Props = {
  isDisabled?: boolean;
  size: number;
};

const HidePasswordIcon = ({isDisabled, size}: Props) => (
  <svg
    fill="none"
    height={size}
    viewBox="0 0 16 16"
    width={size}
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_4208_40592)">
      <IconPath
        $isDisabled={isDisabled}
        d="M11.96 11.96C10.8204 12.8287 9.43274 13.3099 8.00001 13.3334C3.33334 13.3334 0.666672 8.00004 0.666672 8.00004C1.49593 6.45463 2.6461 5.10444 4.04001 4.04004M6.60001 2.8267C7.05889 2.71929 7.52871 2.6656 8.00001 2.6667C12.6667 2.6667 15.3333 8.00004 15.3333 8.00004C14.9287 8.75711 14.446 9.46986 13.8933 10.1267M9.41334 9.41337C9.23024 9.60987 9.00944 9.76747 8.76411 9.87679C8.51878 9.9861 8.25394 10.0449 7.9854 10.0496C7.71686 10.0544 7.45011 10.005 7.20108 9.90436C6.95204 9.80378 6.72582 9.65406 6.5359 9.46414C6.34599 9.27422 6.19627 9.048 6.09568 8.79896C5.99509 8.54993 5.94569 8.28318 5.95043 8.01464C5.95517 7.7461 6.01394 7.48127 6.12326 7.23594C6.23257 6.9906 6.39017 6.7698 6.58667 6.5867"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.3595"
      />
      <IconPath
        $isDisabled={isDisabled}
        d="M0.666672 0.666687L15.3333 15.3334"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.3595"
      />
    </g>
    <defs>
      <clipPath id="clip0_4208_40592">
        <rect fill="white" height="16" width="16" />
      </clipPath>
    </defs>
  </svg>
);

export default HidePasswordIcon;
