import {forwardRef} from "react";
import type {ComponentProps, ComponentRef} from "react";

import {FieldContainer} from "@/field";

import PasswordInput from "./password-input";

type PasswordInputRef = ComponentRef<typeof PasswordInput>;

type PasswordInputProps = ComponentProps<typeof PasswordInput>;

type FieldContainerProps = ComponentProps<typeof FieldContainer>;

type ExposedFieldContainerProps = Omit<FieldContainerProps, "children">;

type Props = ExposedFieldContainerProps & PasswordInputProps;

const PasswordField = forwardRef<PasswordInputRef, Props>(
  (
    {
      boxRef,
      defaultValue,
      hasError,
      isDisabled,
      onBlur,
      onChange,
      onFocus,
      placeholder,
      value,
      ...containerProps
    },
    ref
  ) => (
    <FieldContainer {...{hasError, isDisabled}} {...containerProps}>
      <PasswordInput
        {...{
          defaultValue,
          hasError,
          boxRef,
          isDisabled,
          onBlur,
          onChange,
          onFocus,
          placeholder,
          ref,
          value
        }}
      />
    </FieldContainer>
  )
);

export default PasswordField;
