import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import PasswordField from "./password-field";

type PasswordFieldProps = ComponentProps<typeof PasswordField>;

type Args = Pick<
  PasswordFieldProps,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "isRequired"
  | "label"
  | "placeholder"
  | "value"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return (
    <PasswordField
      {...args}
      onChange={event => updateArgs({value: event.target.value})}
    />
  );
};

const meta: Meta<Args> = {
  title: "password-field",
  argTypes: {
    errorMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    infoMessage: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    },
    value: {
      control: "text"
    }
  }
};

export {meta as default, Default};
