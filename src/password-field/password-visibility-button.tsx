import {UnstyledButton} from "@/unstyled";

import HidePasswordIcon from "./hide-password-icon";
import ShowPasswordIcon from "./show-password-icon";

type Props = {
  isDisabled?: boolean;
  isPasswordVisible?: boolean;
  onToggle: (isPasswordVisible: boolean) => unknown;
};

const iconSize = 16;

const PasswordVisibilityButton = ({
  isDisabled,
  isPasswordVisible,
  onToggle
}: Props) => (
  <UnstyledButton
    disabled={isDisabled}
    onClick={() => onToggle(!isPasswordVisible)}
    type="button"
  >
    {isPasswordVisible ? (
      <HidePasswordIcon {...{isDisabled}} size={iconSize} />
    ) : (
      <ShowPasswordIcon {...{isDisabled}} size={iconSize} />
    )}
  </UnstyledButton>
);

export default PasswordVisibilityButton;
