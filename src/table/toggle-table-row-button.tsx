import styled, {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import TableArrowIcon from "./table-arrow-icon";

type Props = {
  isRowExpanded?: boolean;
};

type ToggleTableRowIconStyledProps = {
  $isRowExpanded?: boolean;
};

const Container = styled(UnstyledButton)`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  border-radius: 50%;
  width: 39px;
  height: 39px;

  &:hover {
    background: ${getColorByAlias("backgroundTertiary")};
  }
`;

const expandedToggleTableRowCss = css`
  transform: rotate(180deg);
`;

const ToggleTableRowIcon = styled(
  TableArrowIcon
)<ToggleTableRowIconStyledProps>`
  ${props => props.$isRowExpanded && expandedToggleTableRowCss}
`;

const ToggleTableRowButton = ({isRowExpanded}: Props) => (
  <Container data-tag="allowRowEvents">
    <ToggleTableRowIcon
      $isRowExpanded={isRowExpanded}
      data-tag="allowRowEvents"
    />
  </Container>
);

export default ToggleTableRowButton;
