import {useElementStyle} from "@unlockre/utils-react/dist/use-element-style";
import {forwardRef, useMemo, useState} from "react";
import type {ReactNode} from "react";
import styled, {css} from "styled-components";

type ContainerElement = HTMLDivElement;

type Props = {
  children?: ReactNode;
  className?: string;
  data?: Record<string, unknown>;
  getFixedElement: (
    tableCellElement: ContainerElement | null
  ) => HTMLElement | null;
  isFixed?: boolean;
  isLastFixed?: boolean;
  withHorizontalPadding?: boolean;
};

type StyledProps = {
  $withHorizontalPadding?: boolean;
};

const getDataProps = (data: Record<string, unknown>) =>
  Object.entries(data).reduce<Record<string, unknown>>(
    (result, [key, value]) => ({
      ...result,
      ["data-" + key]: value
    }),
    {}
  );

const horizontalPadding = 16;

const horizontalPaddingCss = css`
  padding: 0 ${horizontalPadding}px;
`;

const Container = styled.div<StyledProps>`
  ${props => props.$withHorizontalPadding && horizontalPaddingCss}
`;

const getFixedStyle = (
  fixedElement: HTMLElement,
  isLastFixed?: boolean
): Partial<CSSStyleDeclaration> => ({
  position: "sticky",
  left: fixedElement.offsetLeft + "px",
  zIndex: "1",
  boxShadow: isLastFixed ? "6px 0px 0px rgba(7, 24, 84, 0.07)" : "none"
});

const TableCell = forwardRef<ContainerElement, Props>(
  (
    {
      data,
      getFixedElement,
      isFixed,
      isLastFixed,
      withHorizontalPadding,
      ...rest
    },
    ref
  ) => {
    const [containerElement, setContainerElement] =
      useState<ContainerElement | null>(null);

    const fixedElement = getFixedElement(containerElement);

    const fixedStyle = useMemo(
      () =>
        fixedElement && isFixed
          ? getFixedStyle(fixedElement, isLastFixed)
          : undefined,
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [fixedElement, isLastFixed, isFixed]
    );

    useElementStyle({
      element: fixedElement,
      style: fixedStyle
    });

    const handleContainerRef = (containerElement: ContainerElement | null) => {
      setContainerElement(containerElement);

      if (typeof ref === "function") {
        ref(containerElement);
      } else if (ref) {
        ref.current = containerElement;
      }
    };

    return (
      <Container
        {...rest}
        {...(data && getDataProps(data))}
        $withHorizontalPadding={withHorizontalPadding}
        ref={handleContainerRef}
      />
    );
  }
);

export default Object.assign(TableCell, {horizontalPadding});
