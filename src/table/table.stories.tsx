import type {Meta, StoryFn} from "@storybook/react";
import type {ExpanderComponentProps} from "react-data-table-component";

import Table from "./table";
import type {TableColumn, TableProps} from "./table";
import TableEmpty from "./table-empty";
import {tableHeaderVariants} from "./table-header-variant";

type Movie = {
  id: number;
  title: string;
  year: string;
};

type ExposedTableProps = Pick<
  TableProps<Movie>,
  | "areRowsExpansible"
  | "bodyHeight"
  | "data"
  | "headerVariant"
  | "pagination"
  | "paginationDefaultPage"
  | "paginationPerPage"
  | "selectableRows"
  | "withoutRowBorders"
>;

type Args = ExposedTableProps & {
  emptyText?: string;
  hideRowHoverEffect?: boolean;
  highlightedRowId?: number;
};

const columns: TableColumn<Movie>[] = [
  {
    name: "Id",
    selector: row => row.id,
    sortable: true,
    width: 100,
    isFixed: true
  },
  {
    name: "Title",
    selector: row => row.title,
    sortable: true,
    grow: 4,
    width: 500,
    isFixed: true
  },
  {
    name: "Year",
    selector: row => row.year,
    width: 500
  },
  {
    name: "Year",
    selector: row => row.year,
    width: 500
  },
  {
    name: "Year",
    selector: row => row.year,
    width: 500
  },
  {
    name: "Year",
    selector: row => row.year,
    width: 500
  },
  {
    name: "Year",
    selector: row => row.year,
    width: 500
  }
];

const MovieDetails = ({data: movie}: ExpanderComponentProps<Movie>) => (
  <div>
    <h3>{movie.title}</h3>
    <p>year: {movie.year}</p>
  </div>
);

const Default: StoryFn<Args> = ({emptyText, highlightedRowId, ...rest}) => (
  <Table
    {...{columns}}
    {...rest}
    emptyElement={<TableEmpty>{emptyText}</TableEmpty>}
    expandableRowsComponent={MovieDetails}
    isRowHighlighted={row => row.id === highlightedRowId}
  />
);

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=336%3A4133"
  }
};

const meta: Meta<Args> = {
  title: "table",
  argTypes: {
    data: {
      control: "object"
    },
    areRowsExpansible: {
      control: "boolean"
    },
    headerVariant: {
      control: "radio",
      options: Object.values(tableHeaderVariants)
    },
    pagination: {
      control: "boolean"
    },
    paginationDefaultPage: {
      control: "number"
    },
    paginationPerPage: {
      control: "number"
    },
    emptyText: {
      control: "text"
    },
    bodyHeight: {
      control: "number"
    },
    selectableRows: {
      control: "boolean"
    },
    hideRowHoverEffect: {
      control: "boolean"
    },
    highlightedRowId: {
      control: "number"
    },
    withoutRowBorders: {
      control: "boolean"
    }
  },
  args: {
    data: [
      {
        id: 1,
        title: "Beetlejuice",
        year: "1988"
      },
      {
        id: 2,
        title: "Ghostbusters",
        year: "1984"
      },
      {
        id: 3,
        title: "Beetlejuice",
        year: "1988"
      },
      {
        id: 4,
        title: "Ghostbusters",
        year: "1984"
      },
      {
        id: 5,
        title: "Beetlejuice",
        year: "1988"
      },
      {
        id: 6,
        title: "Ghostbusters",
        year: "1984"
      },
      {
        id: 7,
        title: "Beetlejuice",
        year: "1988"
      },
      {
        id: 8,
        title: "Ghostbusters",
        year: "1984"
      },
      {
        id: 9,
        title: "Beetlejuice",
        year: "1988"
      },
      {
        id: 10,
        title: "Ghostbusters",
        year: "1984"
      }
    ],
    areRowsExpansible: true,
    headerVariant: tableHeaderVariants.secondary,
    paginationDefaultPage: 1,
    paginationPerPage: 3,
    emptyText: "No movies",
    bodyHeight: 500
  }
};

export {meta as default, Default};
