import {useElementStyle} from "@unlockre/utils-react/dist/use-element-style";
import {useMemo, useState} from "react";
import type {ComponentProps, ComponentRef} from "react";
import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import TableCell from "./table-cell";

type TableCellElement = ComponentRef<typeof TableCell>;

type TableCellProps = ComponentProps<typeof TableCell>;

type ExposedTableCellProps = Omit<
  TableCellProps,
  "data" | "getFixedElement" | "key" | "ref" | "withHorizontalPadding"
>;

type Props = ExposedTableCellProps & {
  isClickable?: boolean;
  isLastFixed?: boolean;
  isRowHovered?: boolean;
};

const StyledTableCell = styled(TableCell)`
  display: flex;
  flex: 1;
  align-items: center;
  height: 64px;
  width: 100%;
  color: ${getColorByAlias("textPrimary")};
  ${getTypography("body", "s")}
  cursor: ${props =>
    props.data?.tag === "allowRowEvents" ? "pointer" : "default"};
`;

const getFixedElement = (tableCellElement: TableCellElement | null) =>
  tableCellElement?.parentElement || null;

const TableRowCell = ({isClickable, isFixed, ...rest}: Props) => {
  const [tableCellElement, setTableCellElement] =
    useState<TableCellElement | null>(null);

  const tableCellFixedStyle = useMemo(
    () =>
      isFixed
        ? {
            backgroundColor: "inherit"
          }
        : undefined,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isFixed]
  );

  useElementStyle({
    element: getFixedElement(tableCellElement),
    style: tableCellFixedStyle
  });

  return (
    <StyledTableCell
      {...{isFixed}}
      {...rest}
      data={isClickable ? {tag: "allowRowEvents"} : undefined}
      getFixedElement={getFixedElement}
      ref={setTableCellElement}
      withHorizontalPadding
    />
  );
};

export default TableRowCell;
