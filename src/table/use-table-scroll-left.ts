import {useElementEvent} from "@unlockre/utils-react/dist/use-element-event";
import type {EventHandler} from "@unlockre/utils-react/dist/use-element-event";
import {useCallback, useState} from "react";

const useTableScrollLeft = () => {
  const [tableScrollLeft, setTableScrollLeft] = useState(0);

  const [tableElement, setTableElement] = useState<HTMLDivElement | null>(null);

  const setContainerElement = <TParentElement extends HTMLElement>(
    containerElement: TParentElement | null
  ) => {
    setTableElement(
      containerElement ? (containerElement.firstChild as HTMLDivElement) : null
    );
  };

  const handleTableScroll: EventHandler<HTMLDivElement> = useCallback(
    (event, target) => {
      setTableScrollLeft(target.scrollLeft);
    },
    [setTableScrollLeft]
  );

  useElementEvent(tableElement, "scroll", handleTableScroll);

  return {
    setContainerElement,
    tableScrollLeft
  };
};

export default useTableScrollLeft;
