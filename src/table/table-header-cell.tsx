import type {ComponentProps} from "react";
import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import TableCell from "./table-cell";

type TableCellProps = ComponentProps<typeof TableCell>;

type ExposedTableCellProps = Omit<
  TableCellProps,
  "data" | "getFixedElement" | "key" | "ref" | "withHorizontalPadding"
>;

type Props = ExposedTableCellProps & {
  isClickable?: boolean;
  isRowHovered?: boolean;
};

const StyledTableCell = styled(TableCell)`
  display: flex;
  align-items: center;
  height: 100%;
  ${getTypography("body", "xs")}
  color: ${getColorByAlias("textSecondary")};
`;

const getFixedElement = (tableCellElement: HTMLElement | null) =>
  tableCellElement?.parentElement?.parentElement || null;

const TableHeaderCell = (props: Props) => (
  <StyledTableCell {...props} getFixedElement={getFixedElement} />
);

export default TableHeaderCell;
