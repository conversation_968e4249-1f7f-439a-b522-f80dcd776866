import type {ConditionalStyles, TableStyles} from "react-data-table-component";
import {useTheme} from "styled-components";
import type {CSSObject, DefaultTheme} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

import TableCell from "./table-cell";
import type {TableHeaderVariant} from "./table-header-variant";

type GetCustomStylesProps = {
  headerVariant: TableHeaderVariant;
  hideHover?: boolean;
  theme: DefaultTheme;
  withoutRowBorders?: boolean;
};

type GetConditionalStylesProps<TRow> = {
  conditionalHighlight?: (row: TRow) => boolean;
  theme: DefaultTheme;
};

type UseTableStylesProps<TRow> = {
  conditionalHighlight?: (row: TRow) => boolean;
  headerVariant: TableHeaderVariant;
  hideHover?: boolean;
  withoutRowBorders?: boolean;
};

type UseTableStylesReturn<TRow> = {
  conditionalRowStyles: ConditionalStyles<TRow>[] | undefined;
  customStyles: TableStyles;
};

const backgroundColors = {
  white: getColorByAlias("backgroundWhite"),
  secondary: getColorByAlias("backgroundSecondary"),
  blue: getColor("blue", "040")
};

const getTableHeaderBackgroundColor = (
  theme: DefaultTheme,
  headerVariant: TableHeaderVariant
) => backgroundColors[headerVariant]({theme});

const getTableRowBorderBottomColor = getColor("gray", "100");

const getHighlightedRowStyle = (theme: DefaultTheme): CSSObject => ({
  background: theme.colors.palette.blue[100]
});

const getCustomStyles = ({
  headerVariant,
  hideHover,
  theme,
  withoutRowBorders
}: GetCustomStylesProps) => ({
  head: {
    style: {
      zIndex: "2"
    }
  },
  headCells: {
    style: {
      alignItems: "stretch",
      paddingLeft: TableCell.horizontalPadding + "px",
      paddingRight: TableCell.horizontalPadding + "px",
      backgroundColor: getTableHeaderBackgroundColor(theme, headerVariant),
      height: "48px"
    }
  },
  headRow: {
    style: {
      minHeight: "unset",
      border: "none"
    }
  },
  cells: {
    style: {
      paddingLeft: 0,
      paddingRight: 0
    }
  },
  rows: {
    style: {
      minHeight: "unset",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "&:not(:last-of-type)": {
        borderBottomColor: getTableRowBorderBottomColor({theme}),
        ...(withoutRowBorders ? {borderBottomStyle: undefined} : {})
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "&:hover": hideHover ? undefined : getHighlightedRowStyle(theme)
    }
  }
});

const getConditionalStyles = <TRow>({
  conditionalHighlight,
  theme
}: GetConditionalStylesProps<TRow>) => {
  if (!conditionalHighlight) {
    return;
  }

  return [
    {
      when: conditionalHighlight,
      style: getHighlightedRowStyle(theme)
    }
  ];
};

const useTableStyles = <TRow>({
  conditionalHighlight,
  headerVariant,
  hideHover,
  withoutRowBorders
}: UseTableStylesProps<TRow>): UseTableStylesReturn<TRow> => {
  const theme = useTheme();

  const customStyles = getCustomStyles({
    headerVariant,
    hideHover,
    theme,
    withoutRowBorders
  });
  const conditionalRowStyles = getConditionalStyles<TRow>({
    conditionalHighlight,
    theme
  });

  return {customStyles, conditionalRowStyles};
};

export default useTableStyles;
