# Table

## About

This component provides a table with the following main features:

- sorting (enabled by default)
- pagination (server and client)
- expandable rows
- columns reordering
- fixed columns

## Examples

### Example Basic

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Table from "@unlockre/components-library/dist/table";

import type {TableColumn} from "@unlockre/components-library/dist/table";

type Movie = {
  id: number;
  title: string;
  year: string;
};

const columns: TableColumn<Movie>[] = [
  {
    name: "Id",
    selector: row => row.id,
    sortable: true
  },
  {
    name: "Title",
    selector: row => row.title,
    sortable: true
  },
  {
    name: "Year",
    selector: row => row.year
  }
];

const movies: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Beetlejuice",
    year: "1988"
  }
];

const ExampleBasic = () => (
  <ThemeProvider>
    <Table {...{columns}} data={movies} headerVariant="white" />
  </ThemeProvider>
);

export default ExampleBasic;
```

### Example with pagination

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Table from "@unlockre/components-library/dist/table";

import type {TableColumn} from "@unlockre/components-library/dist/table";

type Movie = {
  id: number;
  title: string;
  year: string;
};

const columns: TableColumn<Movie>[] = [
  {
    name: "Id",
    selector: row => row.id,
    sortable: true
  },
  {
    name: "Title",
    selector: row => row.title,
    sortable: true
  },
  {
    name: "Year",
    selector: row => row.year
  }
];

const movies: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 4,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 5,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 6,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 7,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 8,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 9,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 10,
    title: "Ghostbusters",
    year: "1984"
  }
];

const ExampleWithPagination = () => (
  <ThemeProvider>
    <Table
      {...{columns}}
      data={movies}
      headerVariant="white"
      pagination
      paginationPerPage={3}
    />
  </ThemeProvider>
);

export default ExampleWithPagination;
```

### Example with expandable rows

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Table from "@unlockre/components-library/dist/table";

import type {TableColumn} from "@unlockre/components-library/dist/table";

type Movie = {
  id: number;
  title: string;
  year: string;
};

const MovieDetails = ({data: movie}: ExpanderComponentProps<Movie>) => (
  <div>
    <h3>{movie.title}</h3>
    <p>year: {movie.year}</p>
  </div>
);

const columns: TableColumn<Movie>[] = [
  {
    name: "Id",
    selector: row => row.id,
    sortable: true
  },
  {
    name: "Title",
    selector: row => row.title,
    sortable: true
  },
  {
    name: "Year",
    selector: row => row.year
  }
];

const movies: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 4,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 5,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 6,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 7,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 8,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 9,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 10,
    title: "Ghostbusters",
    year: "1984"
  }
];

const ExampleWithExpandableRows = () => (
  <ThemeProvider>
    <Table
      {...{columns}}
      data={movies}
      headerVariant="white"
      expandableRowsComponent={MovieDetails}
    />
  </ThemeProvider>
);

export default ExampleWithExpandableRows;
```

### Example with fixed columns

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Table from "@unlockre/components-library/dist/table";

import type {TableColumn} from "@unlockre/components-library/dist/table";

type Movie = {
  id: number;
  title: string;
  year: string;
};

const columns: TableColumn<Movie>[] = [
  {
    name: "Id",
    selector: row => row.id,
    sortable: true,
    isFixed: true
  },
  {
    name: "Title",
    selector: row => row.title,
    sortable: true,
    isFixed: true
  },
  {
    name: "Year",
    selector: row => row.year
  }
];

const movies: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Beetlejuice",
    year: "1988"
  }
];

const ExampleWithFixedColumns = () => (
  <ThemeProvider>
    <Table {...{columns}} data={movies} headerVariant="white" />
  </ThemeProvider>
);

export default ExampleWithFixedColumns;
```