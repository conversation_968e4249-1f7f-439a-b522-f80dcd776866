import type {CSSProperties} from "react";

import Checkbox from "@/checkbox";

// This props are defined here:
// https://github.com/jbetancur/react-data-table-component/blob/02cfb7285cbb4d3f337e5f3b56c4e9ee3a1a24e2/src/DataTable/Checkbox.tsx#L50

type Props = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  "aria-label": string;
  // eslint-disable-next-line react/boolean-prop-naming
  checked?: boolean;
  // eslint-disable-next-line react/boolean-prop-naming
  disabled?: boolean;
  name: string;
  onChange: () => unknown;
  onClick: () => unknown;
  style?: CSSProperties;
  type: string;
};

const TableRowCheckbox = ({checked, onClick}: Props) => (
  <Checkbox isChecked={checked} onChange={onClick} size="medium" />
);

export default TableRowCheckbox;
