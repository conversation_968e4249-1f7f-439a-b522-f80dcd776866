import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

type Props = {
  className?: string;
};

const StyledPath = styled.path`
  fill: ${getColorByAlias("textSecondary")};
`;

const TableArrowIcon = (props: Props) => (
  <svg
    fill="none"
    height="20"
    viewBox="0 0 20 20"
    width="20"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <StyledPath
      clipRule="evenodd"
      d="M4.29289 6.29289C4.68342 5.90237 5.31658 5.90237 5.70711 6.29289L10 10.5858L14.2929 6.29289C14.6834 5.90237 15.3166 5.90237 15.7071 6.29289C16.0976 6.68342 16.0976 7.31658 15.7071 7.70711L10.7071 12.7071C10.3166 13.0976 9.68342 13.0976 9.29289 12.7071L4.29289 7.70711C3.90237 7.31658 3.90237 6.68342 4.29289 6.29289Z"
      fillRule="evenodd"
    />
  </svg>
);

export default TableArrowIcon;
