import type {PaginationComponentProps as DataTablePaginationOptions} from "react-data-table-component";
import styled from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import TableArrowIcon from "./table-arrow-icon";

const TablePaginationContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  min-height: 52px;
  align-items: center;
  border-top: 1px solid ${getColor("gray", "100")};
`;

const Paragraph = styled.p`
  color: ${getColorByAlias("textPrimary")};
  ${getTypography("body", "xs")};
  margin-bottom: 0;
`;

const IconsContainer = styled.div`
  display: flex;
  margin-left: 16px;
`;

const IconsContainerButton = styled.button`
  background: none;
  padding: 8px;
  border: 0;
  cursor: pointer;
  border-radius: 50%;
  width: 36px;
  height: 36px;

  &:hover {
    background: ${getColorByAlias("backgroundTertiary")};
  }

  &:disabled {
    background-color: transparent;
    opacity: 0.4;
    pointer-events: none;
  }
`;

const StyledTableArrowIconPrev = styled(TableArrowIcon)`
  transform: rotate(90deg);
`;

const StyledTableArrowIconNext = styled(TableArrowIcon)`
  transform: rotate(-90deg);
`;

const isPrevDisabled = (currentPage: number) => currentPage === 1;

const isNextDisabled = (
  rowsPerPage: number,
  currentPage: number,
  rowCount: number
) => rowsPerPage * currentPage >= rowCount;

const TablePagination = ({
  currentPage,
  onChangePage,
  rowCount,
  rowsPerPage
}: DataTablePaginationOptions) => {
  const goPrev = () => {
    onChangePage(currentPage - 1, rowCount);
  };

  const goNext = () => {
    onChangePage(currentPage + 1, rowCount);
  };

  const getActivePage = () =>
    currentPage > 1 ? rowsPerPage * (currentPage - 1) + 1 : 1;

  const getPerPageCount = () => Math.min(rowsPerPage * currentPage, rowCount);

  const renderPageInformation = () => (
    <Paragraph>
      {getActivePage()} - {getPerPageCount()} of {rowCount}
    </Paragraph>
  );

  return (
    <TablePaginationContainer>
      {renderPageInformation()}
      <IconsContainer>
        <IconsContainerButton
          disabled={isPrevDisabled(currentPage)}
          onClick={goPrev}
        >
          <StyledTableArrowIconPrev />
        </IconsContainerButton>
        <IconsContainerButton
          disabled={isNextDisabled(rowsPerPage, currentPage, rowCount)}
          onClick={goNext}
        >
          <StyledTableArrowIconNext />
        </IconsContainerButton>
      </IconsContainer>
    </TablePaginationContainer>
  );
};

export default TablePagination;
