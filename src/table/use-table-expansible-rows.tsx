import {remove} from "@unlockre/utils-array/dist";
import {useState} from "react";
import type {
  TableColumn as DataTableColumn,
  TableProps as DataTableProps
} from "react-data-table-component";

import ToggleTableRowButton from "./toggle-table-row-button";

type HandleRowExpandToggled<TRow> = NonNullable<
  DataTableProps<TRow>["onRowExpandToggled"]
>;

const useTableExpansibleRows = <TRow,>(areRowsExpansible: boolean) => {
  const [expandedRows, setExpandedRows] = useState<TRow[]>([]);

  if (!areRowsExpansible) {
    return {
      expansibleColumn: undefined,
      dataTableProps: {}
    };
  }

  const expansibleColumn: DataTableColumn<TRow> = {
    cell: row => (
      <ToggleTableRowButton isRowExpanded={expandedRows.includes(row)} />
    ),
    button: true
  };

  const handleRowExpandToggled: HandleRowExpandToggled<TRow> = (
    isRowExpanded,
    row
  ) =>
    setExpandedRows(
      isRowExpanded
        ? [...expandedRows, row]
        : remove(expandedRows, expandedRows.indexOf(row), 1)
    );

  const dataTableProps = {
    expandableRows: true,
    expandOnRowClicked: true,
    expandableRowsHideExpander: true
  };

  return {
    expansibleColumn,
    dataTableProps,
    handleRowExpandToggled
  };
};

export default useTableExpansibleRows;
