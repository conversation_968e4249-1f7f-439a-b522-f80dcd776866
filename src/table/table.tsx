import type {ReactElement} from "react";
import DataTable from "react-data-table-component";
import type {
  TableColumn as DataTableColumn,
  TableProps as DataTableProps
} from "react-data-table-component";
import styled from "styled-components";

import Spinner from "@/spinner";

import TableHeaderCell from "./table-header-cell";
import type {TableHeaderVariant} from "./table-header-variant";
import TablePagination from "./table-pagination";
import TableRowCell from "./table-row-cell";
import TableRowCheckbox from "./table-row-checkbox";
import TableSortIcon from "./table-sort-icon";
import useTableExpansibleRows from "./use-table-expansible-rows";
import useTableScrollLeft from "./use-table-scroll-left";
import useTableStyles from "./use-table-styles";

type TableColumn<TRow> = Omit<
  DataTableColumn<TRow>,
  | "allowOverflow"
  | "center"
  | "compact"
  | "conditionalCellStyles"
  | "ignoreRowClick"
  | "maxWidth"
  | "minWidth"
  | "right"
  | "style"
  | "width"
  | "wrap"
> & {
  isFixed?: boolean;
  width?: number;
};

type ExposedDataTableProps<TRow> = Omit<
  DataTableProps<TRow>,
  | "className"
  | "columns"
  | "conditionalRowStyles"
  | "contextActions"
  | "contextComponent"
  | "contextMessage"
  | "customStyles"
  | "expandableInheritConditionalStyles"
  | "expandableRows"
  | "expandableRowsHideExpander"
  | "expandOnRowClicked"
  | "fixedHeader"
  | "highlightOnHover"
  | "noDataComponent"
  | "onChangeRowsPerPage"
  | "onRowExpandToggled"
  | "paginationComponent"
  | "paginationComponentOptions"
  | "paginationIconFirstPage"
  | "paginationIconLastPage"
  | "paginationIconNext"
  | "paginationIconPrevious"
  | "paginationRowsPerPageOptions"
  | "persistTableHead"
  | "pointerOnHover"
  | "progressComponent"
  | "selectableRowsComponent"
  | "selectableRowsComponentProps"
  | "selectableRowsHighlight"
  | "selectableRowsSingle"
  | "selectableRowsVisibleOnly"
  | "sortIcon"
  | "style"
  | "theme"
  | "title"
>;

type ContainerProgressProps = {
  $bodyHeight?: number;
};

type Props<TRow> = ExposedDataTableProps<TRow> & {
  areRowsClickable?: boolean;
  areRowsExpansible?: boolean;
  bodyHeight: number;
  className?: string;
  columns: TableColumn<TRow>[];
  emptyElement?: ReactElement;
  headerVariant: TableHeaderVariant;
  hideRowHoverEffect?: boolean;
  isRowHighlighted?: (row: TRow) => boolean;
  onRowExpandToggled?: DataTableProps<TRow>["onRowExpandToggled"];
  withoutRowBorders?: boolean;
};

const ContainerProgress = styled.div<ContainerProgressProps>`
  display: flex;
  justify-content: center;
  align-items: center;
  height: ${props => props.$bodyHeight}px;
`;

const ensureFixedConsecutiveColumns = <TRow,>(columns: TableColumn<TRow>[]) => {
  const areFixedColumnsConsecutives = columns.every(
    (column, index) =>
      !column.isFixed || index === 0 || columns[index - 1].isFixed
  );

  if (!areFixedColumnsConsecutives) {
    throw new Error("Not consecutive columns");
  }
};

const isLastFixed = <TRow,>(columns: TableColumn<TRow>[], index: number) =>
  columns[index].isFixed &&
  index < columns.length - 1 &&
  !columns[index + 1].isFixed;

/**
 * @deprecated Use VirtualTable instead.
 */
const Table = <TRow,>({
  areRowsClickable,
  areRowsExpansible = false,
  bodyHeight,
  className,
  columns,
  emptyElement,
  headerVariant,
  hideRowHoverEffect,
  isRowHighlighted,
  onRowExpandToggled,
  withoutRowBorders,
  ...rest
}: Props<TRow>) => {
  ensureFixedConsecutiveColumns(columns);

  const {setContainerElement, tableScrollLeft} = useTableScrollLeft();

  const {dataTableProps, expansibleColumn, handleRowExpandToggled} =
    useTableExpansibleRows<TRow>(areRowsExpansible);

  const dataTableColumns: DataTableColumn<TRow>[] = columns.map(
    ({isFixed, name, selector, width, ...colRest}, index) => ({
      cell: row => (
        <TableRowCell
          isClickable={areRowsClickable}
          isFixed={isFixed && tableScrollLeft > 0}
          isLastFixed={isLastFixed(columns, index) && tableScrollLeft > 0}
        >
          {/* We need to wrap with a fragment because selector can return a
             Bigint and it's not compatible with ReactNode*/}
          <>{selector?.(row)}</>
        </TableRowCell>
      ),
      ignoreRowClick: true,
      name: (
        <TableHeaderCell
          isFixed={isFixed && tableScrollLeft > 0}
          isLastFixed={isLastFixed(columns, index) && tableScrollLeft > 0}
        >
          {name}
        </TableHeaderCell>
      ),
      selector,
      width: width ? `${width}px` : undefined,
      ...colRest
    })
  );

  const {conditionalRowStyles, customStyles} = useTableStyles<TRow>({
    conditionalHighlight: isRowHighlighted,
    headerVariant,
    hideHover: hideRowHoverEffect,
    withoutRowBorders
  });

  return (
    <div {...{className}} ref={setContainerElement}>
      <DataTable
        {...rest}
        {...dataTableProps}
        columns={
          expansibleColumn
            ? [...dataTableColumns, expansibleColumn]
            : dataTableColumns
        }
        conditionalRowStyles={conditionalRowStyles}
        customStyles={customStyles}
        fixedHeader
        fixedHeaderScrollHeight={`${bodyHeight}px`}
        noDataComponent={emptyElement}
        onRowExpandToggled={(isExpanded: boolean, row: TRow) => {
          handleRowExpandToggled?.(isExpanded, row);
          onRowExpandToggled?.(isExpanded, row);
        }}
        paginationComponent={TablePagination}
        persistTableHead
        progressComponent={
          <ContainerProgress $bodyHeight={bodyHeight}>
            <Spinner size={45} />
          </ContainerProgress>
        }
        // we need this because the following prop is not well typed
        // @ts-expect-error
        selectableRowsComponent={TableRowCheckbox}
        sortIcon={<TableSortIcon />}
      />
    </div>
  );
};

export default Table;

export type {Props as TableProps, TableColumn};
