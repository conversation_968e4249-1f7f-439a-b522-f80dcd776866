import {forwardRef} from "react";
import type {ComponentProps, ComponentType, LegacyRef} from "react";

import {ErrorBoundary} from "./error-boundary";

type ErrorBoundaryProps = ComponentProps<typeof ErrorBoundary>;

type Params = Omit<ErrorBoundaryProps, "children" | "onError">;

type ExtractRefValue<TProps extends object> = TProps extends {
  ref?: LegacyRef<infer RefValue> | undefined;
}
  ? RefValue
  : never;

const withErrorBoundary = <TProps extends object>(
  Component: ComponentType<TProps>,
  params?: Params
) => {
  const ComponentWithErrorBoundary = forwardRef<
    ExtractRefValue<TProps>,
    TProps
  >((props, ref) => (
    <ErrorBoundary {...params}>
      <Component {...(props as TProps)} ref={ref} />
    </ErrorBoundary>
  ));

  const componentName = Component.displayName || Component.name || "Unknown";

  ComponentWithErrorBoundary.displayName = `withErrorBoundary(${componentName})`;

  return ComponentWithErrorBoundary;
};

export {withErrorBoundary};
