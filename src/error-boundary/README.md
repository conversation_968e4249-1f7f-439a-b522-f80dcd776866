# ErrorBoundary

## About

This component implements the [React Error Boundary](https://reactjs.org/docs/error-boundaries.html) API.

It provides a way to render something when an error is thrown in any of the descendant components.

> If no renderError function is given, it will hide the component with error not rendering anything (returning null)

## API

### Types

```ts
type Props = {
  children: ReactNode;
  onError?: (error: unknown) => unknown;
  renderError?: (error: unknown, resetError: ResetError) => ReactNode;
  shouldIgnoreError?: (error: unknown) => boolean;
};
```

### Examples

1. Using `ErrorBoundary` component.

```tsx
import {ErrorBoundary} from "@unlockre/components-library/dist/error-boundary";

const OtherComponentWithError = () => {
  throw new Error("Something happened");

  return null;
};

/**
 * It will render a div containing "Something happened", which is the message
 * of the error thrown inside OtherComponentWithError
 */
const  MyComponent = () => (
  <ErrorBoundary renderError={error => <div>{(error as Error).message}</div>}>
    <OtherComponentWithError />
  </ErrorBoundary>
);
```

2. Using `withErrorBoundary` [HOC](https://legacy.reactjs.org/docs/higher-order-components.html) to hide the component with error.

```tsx
import {withErrorBoundary} from "@unlockre/components-library/dist/error-boundary";

const OtherComponentWithError = () => {
  throw new Error("Something happened");

  return null;
};

// It will hide the component if there is an error
const  MyComponent = withErrorBoundary(OtherComponentWithError);
```

3. Using `withErrorBoundary` [HOC](https://legacy.reactjs.org/docs/higher-order-components.html) to render something if there is an error.

```tsx
import {withErrorBoundary} from "@unlockre/components-library/dist/error-boundary";

const OtherComponentWithError = () => {
  throw new Error("Something happened");

  return null;
};

/**
 * It will render a div containing "Something happened", which is the message
 * of the error thrown inside OtherComponentWithError
 */
const  MyComponent = withErrorBoundary(OtherComponentWithError, {
  renderError: error => (
    <div>{(error as Error).message}</div>
  )
});
```
