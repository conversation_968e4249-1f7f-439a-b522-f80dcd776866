import {Component} from "react";
import type {ReactNode} from "react";

type State = {
  error: unknown;
  hasError: boolean;
};

type ResetError = () => void;

type Props = {
  children: ReactNode;
  onError?: (error: unknown) => unknown;
  renderError?: (error: unknown, resetError: ResetError) => ReactNode;
  shouldIgnoreError?: (error: unknown) => boolean;
};

const initialState: State = {
  hasError: false,
  error: undefined
};

class ErrorBoundary extends Component<Props, State> {
  static getDerivedStateFromError = (error: Error) => ({
    hasError: true,
    error
  });

  state = initialState;

  componentDidCatch(error: unknown) {
    const {onError} = this.props;

    onError?.(error);
  }

  resetError = () => {
    this.setState(initialState);
  };

  render() {
    const {children, renderError, shouldIgnoreError} = this.props;

    const {error, hasError} = this.state;

    if (hasError && shouldIgnoreError?.(error)) {
      throw error;
    }

    return hasError ? renderError?.(error, this.resetError) ?? null : children;
  }
}

export {ErrorBoundary};
