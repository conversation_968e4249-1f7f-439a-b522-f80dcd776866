import MuiButtonWithoutTheme from "@mui/material/Button";
import {forwardRef} from "react";
import type {ComponentPropsWithoutRef, ReactNode} from "react";
import {useTheme} from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider";
import {ThemeProvider} from "@/theme-provider";
import type {AnyTheme} from "@/theme-provider/theme";

type Props = ComponentPropsWithoutRef<typeof MuiButtonWithoutTheme>;

type MuiButtonRefElement = HTMLButtonElement;

const renderIcon = (theme: AnyTheme, icon?: ReactNode) =>
  icon && <ThemeProvider {...{theme}}>{icon}</ThemeProvider>;

const MuiButton = forwardRef<MuiButtonRefElement, Props>(
  ({endIcon, startIcon, ...rest}, ref) => {
    const theme = useTheme();

    return (
      <MuiThemeProvider>
        <MuiButtonWithoutTheme
          ref={ref}
          {...rest}
          endIcon={renderIcon(theme, endIcon)}
          startIcon={renderIcon(theme, startIcon)}
        />
      </MuiThemeProvider>
    );
  }
);

export {MuiButton};
