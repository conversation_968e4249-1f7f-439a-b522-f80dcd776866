import {forwardRef} from "react";
import type {ComponentPropsWithoutRef} from "react";
import styled, {css} from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import {buttonSizes} from "./button-size";
import type {ButtonSize} from "./button-size";
import type {ButtonVariant} from "./button-variant";
import {MuiButton} from "./mui-button";

type MuiButtonProps = ComponentPropsWithoutRef<typeof MuiButton>;

type ExposedMuiButtonProps = Omit<MuiButtonProps, "size" | "variant">;

type StyledMuiButtonStyledProps = {
  $isDanger?: boolean;
  $size: ButtonSize;
  $variant: ButtonVariant;
};

type StyledMuiButtonProps = ExposedMuiButtonProps & StyledMuiButtonStyledProps;

type Props = ExposedMuiButtonProps & {
  isDanger?: boolean;
  size: ButtonSize;
  testId?: string;
  variant: ButtonVariant;
};

const transparentEnabledCss = css`
  background: transparent;
  color: ${getColorByAlias("accentSecondary")};

  &:hover {
    background: transparent;
    color: ${getColor("blue", "900")};
  }
`;

const transparentDangerCss = css`
  background: transparent;
  color: ${getColorByAlias("feedbackError")};

  &:hover {
    background: transparent;
    color: ${getColor("red", "800")};
  }
`;

const secondaryEnabledCss = css`
  background: ${getColor("blue", "150")};
  color: ${getColorByAlias("accentSecondary")};

  &:hover {
    background: ${getColor("blue", "400")};
    color: ${getColorByAlias("accentTertiary")};
  }
`;

const secondaryDangerCss = css`
  background: ${getColor("red", "100")};
  color: ${getColorByAlias("feedbackError")};

  &:hover {
    background: ${getColor("red", "200")};
    color: ${getColor("red", "600")};
  }
`;

const primaryEnabledCss = css`
  background: ${getColorByAlias("accentSecondary")};
  color: ${getColor("gray", "000")};

  &:hover {
    background: ${getColorByAlias("accentTertiary")};
  }
`;

const primaryDangerCss = css`
  background: ${getColorByAlias("feedbackError")};
  color: ${getColor("gray", "000")};

  &:hover {
    background: ${getColor("red", "600")};
  }
`;

const cssByVariant = {
  primary: {
    danger: primaryDangerCss,
    enabled: primaryEnabledCss
  },
  secondary: {
    danger: secondaryDangerCss,
    enabled: secondaryEnabledCss
  },
  transparent: {
    danger: transparentDangerCss,
    enabled: transparentEnabledCss
  }
};

const transparentDisabledCss = css`
  color: ${getColorByAlias("textDisabled")};
`;

const defaultDisabledCss = css`
  background: ${getColor("gray", "100")};
  color: ${getColorByAlias("textDisabled")};
`;

const disabledCss = {
  primary: defaultDisabledCss,
  secondary: defaultDisabledCss,
  transparent: transparentDisabledCss
};

const getVariantCss = (props: StyledMuiButtonProps) =>
  props.disabled
    ? disabledCss[props.$variant]
    : cssByVariant[props.$variant][props.$isDanger ? "danger" : "enabled"];

const xlargeCss = css`
  ${getTypography("body", "s", 600)}

  height: 48px;
  padding: 0 24px;
  border-radius: 6px;
`;

const largeCss = css`
  ${getTypography("body", "s", 600)}

  height: 40px;
  padding: 0 20px;
  border-radius: 6px;
`;

const mediumCss = css`
  ${getTypography("body", "xs", 600)}

  height: 32px;
  padding: 0 12px;
  border-radius: 5px;
`;

const smallCss = css`
  ${getTypography("body", "xxs", 600)}

  height: 24px;
  padding: 0 8px;
  border-radius: 5px;
`;

const cssBySize = {
  [buttonSizes.small]: smallCss,
  [buttonSizes.medium]: mediumCss,
  [buttonSizes.large]: largeCss,
  [buttonSizes.xlarge]: xlargeCss
};

const getSizeCss = (props: StyledMuiButtonProps) => cssBySize[props.$size];

const StyledMuiButton = styled(MuiButton)<StyledMuiButtonStyledProps>`
  ${getSizeCss}

  ${getVariantCss}

  border: 0;
  text-transform: none;
`;

/**
 * @deprecated Use Button from /buttons instead
 */
const Button = forwardRef<HTMLButtonElement, Props>(
  ({isDanger, size, testId, variant, ...rest}, ref) => (
    <StyledMuiButton
      {...{ref}}
      {...rest}
      $isDanger={isDanger}
      $size={size}
      $variant={variant}
      data-testid={testId}
    />
  )
);

export default Button;
