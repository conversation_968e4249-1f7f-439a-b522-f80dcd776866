import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import Button from "./button";
import {buttonSizes} from "./button-size";
import {buttonVariants} from "./button-variant";

type ButtonProps = ComponentProps<typeof Button>;

type Args = Pick<ButtonProps, "children" | "isDanger" | "size" | "variant">;

const Default: StoryFn<Args> = args => <Button {...args} />;

const meta: Meta<Args> = {
  title: "button/button",
  argTypes: {
    children: {
      control: "text"
    },
    isDanger: {
      control: "boolean"
    },
    variant: {
      control: "radio",
      options: Object.values(buttonVariants)
    },
    size: {
      control: "radio",
      options: Object.values(buttonSizes)
    }
  },
  args: {
    children: "Label",
    isDanger: false,
    variant: buttonVariants.primary,
    size: buttonSizes.large
  }
};

export {meta as default, Default};
