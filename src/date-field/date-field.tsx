import {DatePicker} from "@mui/x-date-pickers";
import {AdapterDateFns} from "@mui/x-date-pickers/AdapterDateFns";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";
import {forwardRef} from "react";
import type {ComponentProps, ComponentRef, Ref} from "react";
import styled, {useTheme} from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider";
import TextField from "@/text-field";
import {ThemeProvider} from "@/theme-provider";

type TextFieldProps = ComponentProps<typeof TextField>;

type ExposedTextFieldProps = Pick<
  TextFieldProps,
  | "className"
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "isRequired"
  | "label"
  | "placeholder"
>;

type DatePickerElement = ComponentRef<typeof DatePicker>;

type DatePickerProps = ComponentProps<typeof DatePicker>;

type ExposedDatePicker = Pick<
  DatePickerProps,
  | "allowSameDateSelection"
  | "disableFuture"
  | "disablePast"
  | "maxDate"
  | "minDate"
>;

// prettier-ignore
type Props =
  & ExposedDatePicker
  & ExposedTextFieldProps
  & {
      calendarRef?: Ref<DatePickerElement>;
      onChange: (value: Date | null) => unknown;
      value: Date | null;
    };

const dateFieldInputHeight = TextField.textInputHeight;

const StyledTextField = styled(TextField)`
  min-width: 150px;
`;

const DateField = forwardRef<HTMLInputElement, Props>(
  (
    {
      calendarRef,
      className,
      errorMessage,
      hasError,
      infoMessage,
      isDisabled,
      isRequired,
      label,
      onChange,
      placeholder,
      ...rest
    },
    ref
  ) => {
    const theme = useTheme();

    return (
      <MuiThemeProvider>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            {...rest}
            disabled={isDisabled}
            inputFormat="MM/dd/yyyy"
            inputRef={ref}
            onChange={date => onChange(date as Date | null)}
            ref={calendarRef}
            renderInput={({inputProps, InputProps, inputRef}) => (
              <ThemeProvider {...{theme}}>
                <StyledTextField
                  {...inputProps}
                  {...{
                    className,
                    hasError,
                    errorMessage,
                    infoMessage,
                    isDisabled,
                    isRequired,
                    label,
                    placeholder
                  }}
                  boxRef={inputRef}
                  defaultValue={
                    inputProps?.defaultValue as TextFieldProps["defaultValue"]
                  }
                  right={InputProps?.endAdornment as TextFieldProps["right"]}
                  // We need the following logic as in the first render DatePicker
                  // passes the value but not the onChange handler and doing this
                  // throws a warning in React
                  value={
                    inputProps?.onChange &&
                    (inputProps?.value as TextFieldProps["value"])
                  }
                />
              </ThemeProvider>
            )}
          />
        </LocalizationProvider>
      </MuiThemeProvider>
    );
  }
);

export {DateField, dateFieldInputHeight};
export type {DatePickerElement as DateFieldCalendarElement};
