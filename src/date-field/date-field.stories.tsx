import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import type {ComponentProps} from "react";

import {DateField} from "./date-field";

type DateFieldProps = ComponentProps<typeof DateField>;

type Args = Pick<
  DateFieldProps,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "isRequired"
  | "label"
  | "placeholder"
>;

const Default: StoryFn<Args> = args => {
  const [date, setDate] = useState<Date | null>(() => new Date());

  return <DateField {...args} onChange={date => setDate(date)} value={date} />;
};

const meta: Meta<Args> = {
  title: "date-field",
  argTypes: {
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    }
  }
};

export {meta as default, Default};
