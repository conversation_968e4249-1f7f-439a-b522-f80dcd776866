# Link

## About

This component is an anchor styled with the text underlined and a mandatory `variant` (`dark` or `light`) prop to determine the text color (`accentPrimary` or `blue 300`, respectively).

The component doesn't include any typography style as most of the times it will be embedded inside a bigger piece of text and it is desirable for the Link to inherit the already used typography.


## Types

```ts
type LinkVariant = "dark" | "light"
```

## Examples

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import * as withTheme from "@unlockre/components-library/dist/theme-provider/theme";
import {Link} from "@unlockre/components-library/dist/link";

const StyledLink = styled(Link)`
  ${withTheme.getTypography("body", "s", 400)};
`;

const MyComponent = () => (
  <ThemeProvider>
    <StyledLink variant="dark">My Link</StyledLink>
  </ThemeProvider>
);

export default MyComponent;
```
