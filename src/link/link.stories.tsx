import type {Meta, StoryFn} from "@storybook/react";
import styled from "styled-components";

import * as withTheme from "@/theme-provider/theme";

import {Link} from "./link";
import {linkVariants} from "./link-variant";
import type {LinkVariant} from "./link-variant";

type Args = {
  text: string;
  variant: LinkVariant;
};

const StyledLink = styled(Link)`
  ${withTheme.getTypography("body", "s", 400)};
`;

const Default: StoryFn<Args> = args => (
  <StyledLink variant={args.variant}>{args.text}</StyledLink>
);

const meta: Meta<Args> = {
  title: "link",
  argTypes: {
    text: {
      control: "text"
    },
    variant: {
      control: "select",
      options: Object.values(linkVariants)
    }
  },
  args: {
    text: "My Link",
    variant: linkVariants.dark
  }
};

export {meta as default, Default};
