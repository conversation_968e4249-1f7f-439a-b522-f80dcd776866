import type {ComponentProps} from "react";
import styled from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";
import {UnstyledAnchor} from "@/unstyled";

import {linkVariants} from "./link-variant";
import type {LinkVariant} from "./link-variant";

type ExposedUnstyledAnchorProps = ComponentProps<typeof UnstyledAnchor>;

type Props = ExposedUnstyledAnchorProps & {
  variant: LinkVariant;
};

type ContainerStyledProps = {
  $variant: LinkVariant;
};

const containerColorGetters = {
  [linkVariants.dark]: getColorByAlias("accentPrimary"),
  [linkVariants.light]: getColor("blue", "300")
};

const Container = styled(UnstyledAnchor)<ContainerStyledProps>`
  color: ${props => containerColorGetters[props.$variant]};

  text-decoration-line: underline;
  text-underline-offset: 3px;
`;

const Link = ({variant, ...props}: Props) => (
  <Container $variant={variant} {...props} />
);

export {Link};
