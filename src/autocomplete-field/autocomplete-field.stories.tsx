import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import AutocompleteField from "./autocomplete-field";
import type {AutocompleteFieldProps} from "./autocomplete-field";

type Option = {
  label: string;
  value: number;
};

type Args = Pick<
  AutocompleteFieldProps<Option>,
  "errorMessage" | "hasError" | "label" | "options" | "placeholder"
>;

const getOptionLabel = (option: Option) => `${option.label} (${option.value})`;

const Default: StoryFn<Args> = args => {
  const [value, setValue] = useState<Option | null>(null);

  return (
    <AutocompleteField
      {...{getOptionLabel, value}}
      {...args}
      onChange={setValue}
    />
  );
};

const meta: Meta<Args> = {
  title: "autocomplete-field",
  argTypes: {
    hasError: {
      control: "boolean"
    },
    errorMessage: {
      control: "text"
    },
    label: {
      control: "text"
    },
    options: {
      control: "object"
    },
    placeholder: {
      control: "text"
    }
  },
  args: {
    options: [
      {label: "first", value: 1},
      {label: "second", value: 2},
      {label: "third", value: 3}
    ],
    placeholder: "Select an option"
  }
};

export {meta as default, Default};
