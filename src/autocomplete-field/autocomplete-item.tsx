import type {HTMLProps} from "react";
import styled from "styled-components";

import AutocompleteText from "./autocomplete-text";
import CheckIcon from "./check-icon";

type ContainerProps = HTMLProps<HTMLLIElement>;

type ExposedContainerProps = Omit<ContainerProps, "as" | "ref">;

type Props = ExposedContainerProps & {
  isSelected?: boolean;
};

const Container = styled.li`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;

  &:not(:last-child) {
    border-bottom: 1px solid rgb(237, 237, 237);
  }

  &:hover,
  &.Mui-focused {
    background-color: rgb(245, 245, 245);
  }
`;

const StyledCheckIcon = styled(CheckIcon)`
  margin-left: 8px;
`;

const AutocompleteItem = ({children, isSelected, ...rest}: Props) => (
  <Container {...rest}>
    <AutocompleteText>{children}</AutocompleteText>
    {isSelected && <StyledCheckIcon />}
  </Container>
);

export default AutocompleteItem;
