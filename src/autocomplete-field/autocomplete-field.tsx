import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon
} from "@mui/icons-material";
import {Autocomplete} from "@mui/material";
import type {
  AutocompleteProps,
  AutocompleteRenderInputParams
} from "@mui/material";
import type {ComponentProps, ReactChild, SyntheticEvent} from "react";
import styled, {useTheme} from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider/mui-theme-provider";
import TextField from "@/text-field";
import {ThemeProvider} from "@/theme-provider";

import AutocompleteItem from "./autocomplete-item";
import AutocompleteText from "./autocomplete-text";

type OnInputChangeParams = {
  hasSelectedOption: boolean;
  inputValue: string;
};

type OnInputChange = (params: OnInputChangeParams) => unknown;

type TextFieldProps = ComponentProps<typeof TextField>;

type ExposedTextFieldProps = Omit<
  TextFieldProps,
  | "boxRef"
  | "defaultValue"
  | "isDisabled"
  | "onBlur"
  | "onChange"
  | "onFocus"
  | "ref"
  | "value"
>;

type ConcreteAutocompleteProps<TValue> = AutocompleteProps<
  TValue,
  false,
  false,
  false
>;

type ExposedAutocompleteProps<TValue> = Pick<
  ConcreteAutocompleteProps<TValue>,
  "disablePortal" | "filterOptions" | "getOptionLabel" | "options"
>;

// prettier-ignore
type Props<TValue> =
  & ExposedAutocompleteProps<TValue>
  & ExposedTextFieldProps
  & {
      isLoading?: boolean;
      left?: ReactChild;
      onChange: (value: TValue | null) => unknown;
      onInputChange?: OnInputChange;
      value: TValue | null;
    };

const hasSelectedOption = (event: SyntheticEvent) =>
  event.type === "click" ||
  // @ts-expect-error
  (event.type === "keydown" && event.key === "Enter");

const getTextFieldPropsFromParams = (
  params: AutocompleteRenderInputParams
) => ({
  boxRef: params.InputProps.ref,
  isDisabled: params.disabled,
  onBlur: params.inputProps.onBlur,
  onChange: params.inputProps.onChange,
  onFocus: params.inputProps.onFocus,
  // @ts-expect-error "ref" should be in HTMLAttributes<HTMLInputElement>
  ref: params.inputProps.ref,
  right: params.InputProps.endAdornment as ReactChild,
  // @ts-expect-error "value" should be in HTMLAttributes<HTMLInputElement>
  value: params.inputProps.value
});

const ToggleDropdownIcon = styled(ExpandMoreIcon)`
  && {
    color: rgb(13, 67, 165);
    height: 20px;
    width: 20px;
  }
`;

const ClearIcon = styled(CloseIcon)`
  && {
    color: rgba(0, 0, 0, 0.9);
    height: 15px;
    width: 15px;
  }
`;

const defaultGetOptionLabel = <TValue,>(option: TValue) => {
  if (typeof option !== "string") {
    throw new Error(
      "AutocompleteField: option is not a string. Please provide a getOptionLabel function."
    );
  }

  return option;
};

const StyledAutocomplete: typeof Autocomplete = styled(Autocomplete)`
  && .MuiAutocomplete-endAdornment {
    position: static;
    right: auto;
    top: auto;
  }
`;

const AutocompleteField = <TValue,>({
  className,
  disablePortal,
  filterOptions,
  getOptionLabel = defaultGetOptionLabel,
  isLoading,
  onChange,
  onInputChange,
  options,
  value,
  ...textFieldProps
}: Props<TValue>) => {
  const theme = useTheme();

  return (
    <MuiThemeProvider>
      <StyledAutocomplete
        {...{
          className,
          disablePortal,
          filterOptions,
          getOptionLabel,
          options,
          value
        }}
        clearIcon={<ClearIcon />}
        loading={isLoading}
        loadingText={
          <ThemeProvider {...{theme}}>
            <AutocompleteText>Loading...</AutocompleteText>
          </ThemeProvider>
        }
        noOptionsText={
          <ThemeProvider {...{theme}}>
            <AutocompleteText>No results</AutocompleteText>
          </ThemeProvider>
        }
        onChange={(event, value) => onChange(value || null)}
        onInputChange={(event, inputValue) => {
          // There is a flow in where MUI passes a null event despite the fact
          // that the type doesn't says so
          if (event === null) {
            return;
          }

          onInputChange?.({
            hasSelectedOption: hasSelectedOption(event),
            inputValue
          });
        }}
        popupIcon={<ToggleDropdownIcon />}
        renderInput={params => (
          <ThemeProvider {...{theme}}>
            <TextField
              {...textFieldProps}
              {...getTextFieldPropsFromParams(params)}
            />
          </ThemeProvider>
        )}
        // @ts-expect-error
        renderOption={({className, key, style, ...rest}, option, state) => (
          <ThemeProvider {...{theme}} key={key}>
            <AutocompleteItem {...rest} isSelected={state.selected}>
              {getOptionLabel(option)}
            </AutocompleteItem>
          </ThemeProvider>
        )}
      />
    </MuiThemeProvider>
  );
};

export default AutocompleteField;
export type {Props as AutocompleteFieldProps};
