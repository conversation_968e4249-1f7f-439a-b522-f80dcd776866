import styled, {css} from "styled-components";

type Direction = "down" | "up";

type Props = {
  direction: Direction;
};

type ContainerStyledProps = {
  $direction: Direction;
};

const containerDownCss = css`
  transform: rotate(180deg);
`;

const Container = styled.svg<ContainerStyledProps>`
  ${props => props.$direction === "down" && containerDownCss}
`;

const VerticalChevronIcon = ({direction}: Props) => (
  <Container
    $direction={direction}
    fill="none"
    height="8"
    viewBox="0 0 13 8"
    width="13"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.6541 6.5L6.65405 1.5L1.65405 6.5"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
    />
  </Container>
);

export default VerticalChevronIcon;
