# Message

## About

This component provides a message with a title, description and an icon on its left side and optional action buttons.

Its style is based on a variant property that could take three different values: "success", "warning" and "error".

## Examples

### Example without action buttons

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Message from "@unlockre/components-library/dist/message";

const WithoutActionButtons = () => (
  <ThemeProvider>
    <Message title="It is a title" description="It is a description" variant="success" />
  </ThemeProvider>
);

export default WithoutActionButtons;
```

### Example with action buttons

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Message, {MessageActionButton} from "@unlockre/components-library/dist/message";

const WithActionButtons = () => (
  <ThemeProvider>
    <Message
      title="It is a title"
      description="It is a description"
      variant="success"
      primaryActionButton={
        <MessageActionButton
          label="Save"
          onClick={() => console.log("save")}
          type="primary"
        />
      }
      secondaryActionButton={
        <MessageActionButton
          label="Close"
          onClick={() => console.log("close")}
          type="secondary"
        />
      }
    />
  </ThemeProvider>
);

export default WithActionButtons;
```