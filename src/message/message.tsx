import type {ReactElement} from "react";
import styled from "styled-components";

import {getColor, getTypography} from "@/theme-provider/theme";

import Icon from "./message-icon/message-icon";
import type {MessageVariant} from "./types";

type Props = {
  className?: string;
  description?: string;
  primaryActionButton?: ReactElement;
  secondaryActionButton?: ReactElement;
  title?: string;
  variant: MessageVariant;
};

type ContainerStylesProps = {
  $variant: MessageVariant;
};

type SecondaryActionButtonContainerStyledProps = {
  $hasMarginLeft?: boolean;
};

const getInfoBackgroundColor = getColor("blue", "100");
const getErrorBackgroundColor = getColor("red", "100");
const getWarningBackgroundColor = getColor("orange", "100");
const getSuccessBackgroundColor = getColor("green", "100");

const Container = styled.div<ContainerStylesProps>`
  border-radius: 8px;
  padding: 18px;
  display: flex;

  background: ${props => {
    switch (props.$variant) {
      case "error":
        return getErrorBackgroundColor;

      case "warning":
        return getWarningBackgroundColor;

      case "success":
        return getSuccessBackgroundColor;

      case "information":
        return getInfoBackgroundColor;
    }
  }};
`;

const ContainerInfo = styled.div`
  margin-left: 18px;
`;

const Title = styled.h3`
  ${getTypography("body", "s", 600)};
  color: ${getColor("gray", "900")};
  margin: 0 0 4px;
`;

const Description = styled.p`
  ${getTypography("body", "xs", 400)};
  color: ${getColor("gray", "550")};
  margin-bottom: 0px;
`;

const StyledIcon = styled(Icon)`
  margin-top: 2px;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  margin-top: 16px;
`;

const SecondaryActionButtonContainer = styled.div<SecondaryActionButtonContainerStyledProps>`
  margin-left: ${props => (props.$hasMarginLeft ? 16 : 0)}px;
`;

const renderActionButtons = (
  primaryActionButton?: ReactElement,
  secondaryActionButton?: ReactElement
) => (
  <ActionButtonsContainer>
    {primaryActionButton}
    {secondaryActionButton && (
      <SecondaryActionButtonContainer
        $hasMarginLeft={primaryActionButton !== undefined}
      >
        {secondaryActionButton}
      </SecondaryActionButtonContainer>
    )}
  </ActionButtonsContainer>
);

const Message = ({
  description,
  primaryActionButton,
  secondaryActionButton,
  title,
  variant,
  ...rest
}: Props) => (
  <Container $variant={variant} {...rest}>
    <StyledIcon messageVariant={variant} />
    <ContainerInfo>
      <Title>{title}</Title>
      <Description>{description}</Description>
      {(primaryActionButton || secondaryActionButton) &&
        renderActionButtons(primaryActionButton, secondaryActionButton)}
    </ContainerInfo>
  </Container>
);

export default Message;
