type Props = {
  className?: string;
};

const SuccessIcon = (props: Props) => (
  <svg
    fill="none"
    height={16}
    viewBox="0 0 48 48"
    width={16}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      clipRule="evenodd"
      d="M22.9996 45.4001C35.3708 45.4001 45.3996 35.3713 45.3996 23.0001C45.3996 10.6289 35.3708 0.600098 22.9996 0.600098C10.6284 0.600098 0.599609 10.6289 0.599609 23.0001C0.599609 35.3713 10.6284 45.4001 22.9996 45.4001ZM35.2008 19.5213C36.3724 18.3498 36.3724 16.4503 35.2008 15.2787C34.0293 14.1071 32.1298 14.1071 30.9582 15.2787L20.7595 25.4774L17.2808 21.9987C16.1093 20.8271 14.2098 20.8271 13.0382 21.9987C11.8666 23.1703 11.8666 25.0698 13.0382 26.2413L18.6382 31.8413C19.2008 32.404 19.9639 32.72 20.7595 32.72C21.5552 32.72 22.3182 32.404 22.8808 31.8413L35.2008 19.5213Z"
      fill="#00A650"
      fillRule="evenodd"
    />
  </svg>
);

export default SuccessIcon;
