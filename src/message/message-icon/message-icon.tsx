import type {MessageVariant} from "../types";

import SuccessIcon from "./success-icon";
import WarningIcon from "./warning-icon";

type Props = {
  className?: string;
  messageVariant: MessageVariant;
};

const MessageIcon = ({messageVariant, ...rest}: Props) => {
  switch (messageVariant) {
    case "success":
      return <SuccessIcon {...rest} />;

    default:
      return <WarningIcon messageVariant={messageVariant} {...rest} />;
  }
};

export default MessageIcon;
