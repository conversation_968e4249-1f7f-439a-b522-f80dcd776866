import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import type {MessageVariant} from "../types";

type SupportedMessageVariant = Exclude<MessageVariant, "success">;

type Props = {
  className?: string;
  messageVariant: SupportedMessageVariant;
};

type StyledPathStyledProps = {
  $messageVariant: SupportedMessageVariant;
};

const getInfoFill = getColorByAlias("accentSecondary");
const getErrorFill = getColorByAlias("feedbackError");
const getWarningFill = getColorByAlias("feedbackWarning");

const StyledPath = styled.path<StyledPathStyledProps>`
  fill: ${props => {
    switch (props.$messageVariant) {
      case "error":
        return getErrorFill;

      case "information":
        return getInfoFill;

      case "warning":
        return getWarningFill;
    }
  }};
`;

const WarningIcon = (props: Props) => (
  <svg
    fill="none"
    height="16"
    viewBox="0 0 16 16"
    width="16"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <StyledPath
      $messageVariant={props.messageVariant}
      d="M8.78873 5.59624V3.98122H7.21127V5.59624H8.78873ZM8.78873 12.0188V7.21127H7.21127V12.0188H8.78873ZM2.32864 2.3662C3.9061 0.788732 5.79656 0 8 0C10.2034 0 12.0814 0.788732 13.6338 2.3662C15.2113 3.91862 16 5.79656 16 8C16 10.2034 15.2113 12.0939 13.6338 13.6714C12.0814 15.2238 10.2034 16 8 16C5.79656 16 3.9061 15.2238 2.32864 13.6714C0.776213 12.0939 0 10.2034 0 8C0 5.79656 0.776213 3.91862 2.32864 2.3662Z"
      fill="#F23D4F"
    />
  </svg>
);

export default WarningIcon;
