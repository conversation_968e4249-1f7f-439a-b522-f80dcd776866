import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import Message from "./message";
import MessageActionButton from "./message-action-button";

type MessageProps = ComponentProps<typeof Message>;

type Args = Pick<MessageProps, "description" | "title" | "variant">;

const storyParameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=1590%3A43119"
  }
};

const WithoutActionButtons: StoryFn<Args> = args => <Message {...args} />;

WithoutActionButtons.parameters = storyParameters;

const WithPrimaryActionButton: StoryFn<Args> = args => (
  <Message
    {...args}
    primaryActionButton={
      <MessageActionButton
        label="Save"
        onClick={() => console.log("save")}
        type="primary"
      />
    }
  />
);

WithPrimaryActionButton.parameters = storyParameters;

const WithSecondaryActionButton: StoryFn<Args> = args => (
  <Message
    {...args}
    secondaryActionButton={
      <MessageActionButton
        label="Close"
        onClick={() => console.log("close")}
        type="secondary"
      />
    }
  />
);

WithSecondaryActionButton.parameters = storyParameters;

const WithActionButtons: StoryFn<Args> = args => (
  <Message
    {...args}
    primaryActionButton={
      <MessageActionButton
        label="Save"
        onClick={() => console.log("save")}
        type="primary"
      />
    }
    secondaryActionButton={
      <MessageActionButton
        label="Close"
        onClick={() => console.log("close")}
        type="secondary"
      />
    }
  />
);

WithActionButtons.parameters = storyParameters;

const meta: Meta<Args> = {
  title: "message",
  argTypes: {
    description: {
      control: "text"
    },
    title: {
      control: "text"
    },
    variant: {
      control: "select",
      options: ["success", "error", "warning", "information"]
    }
  },
  args: {
    description: "This is a description!",
    title: "This is a title!",
    variant: "error"
  }
};

export {
  meta as default,
  WithoutActionButtons,
  WithPrimaryActionButton,
  WithSecondaryActionButton,
  WithActionButtons
};
