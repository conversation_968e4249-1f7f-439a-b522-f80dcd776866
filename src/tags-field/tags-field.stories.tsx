import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import type {TagVariant} from "./tag";
import TagsField from "./tags-field";
import type {TagsFieldProps} from "./tags-field";

type TagItem = {
  label: string;
  value: number;
  variant: TagVariant;
};

type Args = Pick<
  TagsFieldProps<TagItem>,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "isRequired"
  | "items"
  | "label"
  | "placeholder"
>;

const getItemLabel = (item: TagItem) => item.label;

const getItemVariant = (item: TagItem) => item.variant;

const Default: StoryFn<Args> = args => {
  const [value, setValue] = useState<TagItem[]>([]);

  return (
    <TagsField
      {...{value}}
      {...args}
      getItemLabel={getItemLabel}
      getItemVariant={getItemVariant}
      onChange={setValue}
    />
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/C3waJMAyXf5Lt1pqOkHZq0/Admin?node-id=3792%3A146394"
  }
};

const meta: Meta<Args> = {
  title: "tags-field",
  argTypes: {
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    items: {
      control: "object"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    }
  },
  args: {
    items: [
      {label: "Legal Conforming", value: 1, variant: "success"},
      {label: "Not Conforming", value: 2, variant: "error"},
      {label: "Review", value: 3, variant: "success"},
      {label: "On Hold", value: 4, variant: "error"}
    ],
    placeholder: "Select a tag to add"
  }
};

export {meta as default, Default};
