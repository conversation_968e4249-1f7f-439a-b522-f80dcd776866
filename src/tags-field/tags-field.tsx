import styled from "styled-components";

import MultiSelectField from "@/select-field/multi-select-field";
import type {MultiSelectFieldProps} from "@/select-field/multi-select-field";

import type {TagVariant} from "./tag";
import TagsList from "./tags-list";

type Props<TItem> = MultiSelectFieldProps<TItem> & {
  getItemVariant: (item: TItem) => TagVariant;
};

const Container = styled.div``;

const TagsField = <TItem,>({
  className,
  getItemLabel,
  getItemVariant,
  isDisabled,
  onChange,
  value,
  ...rest
}: Props<TItem>) => (
  <Container className={className}>
    <MultiSelectField
      {...{getItemLabel}}
      {...rest}
      isDisabled={isDisabled}
      onChange={onChange}
      value={value}
    />

    <TagsList
      getItemLabel={getItemLabel}
      getItemVariant={getItemVariant}
      isDisabled={isDisabled}
      items={value}
      onChange={onChange}
    />
  </Container>
);

export default TagsField;

export type {Props as TagsFieldProps};
