import styled, {css} from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import CloseIcon from "./close-icon";

type Props<TItem> = {
  getItemLabel: (item: TItem) => string;
  getItemVariant: (item: TItem) => Variant;
  isDisabled?: boolean;
  item: TItem;
  onRemove: (item: TItem) => unknown;
};

type Variant = "error" | "gray" | "information" | "success";

type ContainerStyledProps = {
  $isDisabled?: boolean;
  variant: Variant;
};

type RemoveItemButtonStyledProps = {
  variant: Variant;
};

const getGrayNotDisabledColor = getColor("gray", "550");

const notDisabledGrayContainerCss = css`
  background: ${getColor("gray", "100")};
  color: ${getGrayNotDisabledColor};
`;

const getInformationNotDisabledColor = getColorByAlias("accentPrimary");

const notDisabledInformationContainerCss = css`
  background: ${getColor("blue", "150")};
  color: ${getInformationNotDisabledColor};
`;

const getErrorNotDisabledColor = getColorByAlias("feedbackError");

const notDisabledErrorContainerCss = css`
  background: ${getColor("red", "100")};
  color: ${getErrorNotDisabledColor};
`;

const getSuccessNotDisabledColor = getColorByAlias("feedbackSuccess");

const notDisabledSuccessContainerCss = css`
  background: ${getColor("green", "150")};
  color: ${getSuccessNotDisabledColor};
`;

const allNotDisabledContainerCss = {
  success: notDisabledSuccessContainerCss,
  error: notDisabledErrorContainerCss,
  information: notDisabledInformationContainerCss,
  gray: notDisabledGrayContainerCss
};

const allNotDisabledColors = {
  success: getSuccessNotDisabledColor,
  error: getErrorNotDisabledColor,
  information: getInformationNotDisabledColor,
  gray: getGrayNotDisabledColor
};

const getDisabledColor = getColorByAlias("textDisabled");

const disabledContainerCss = css`
  background: ${getColor("gray", "070")};
  color: ${getDisabledColor};
`;

const Container = styled.div<ContainerStyledProps>`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px 5px 10px;
  border-radius: 12px;

  ${props =>
    props.$isDisabled
      ? disabledContainerCss
      : allNotDisabledContainerCss[props.variant]}
`;

const Label = styled.span`
  ${getTypography("body", "xs", 600)};
  margin-right: 6px;
`;

const RemoveItemButton = styled(UnstyledButton)<RemoveItemButtonStyledProps>`
  ${getTypography("body", "xxs", 600)};
  color: ${props =>
    props.disabled ? getDisabledColor : allNotDisabledColors[props.variant]};
  height: 11px;
`;

const Tag = <TItem,>({
  getItemLabel,
  getItemVariant,
  isDisabled,
  item,
  onRemove
}: Props<TItem>) => (
  <Container $isDisabled={isDisabled} variant={getItemVariant(item)}>
    <Label>{getItemLabel(item)}</Label>
    <RemoveItemButton
      disabled={isDisabled}
      onClick={() => onRemove(item)}
      type="button"
      variant={getItemVariant(item)}
    >
      <CloseIcon {...{isDisabled}} variant={getItemVariant(item)} />
    </RemoveItemButton>
  </Container>
);

export default Tag;

export type {Variant as TagVariant};
