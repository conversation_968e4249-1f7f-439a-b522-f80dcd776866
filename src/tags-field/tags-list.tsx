import * as withArray from "@unlockre/utils-array/dist";
import styled from "styled-components";

import {UnstyledUl} from "@/unstyled";

import Tag from "./tag";
import type {TagVariant} from "./tag";

type Props<TItem> = {
  getItemLabel: (item: TItem) => string;
  getItemVariant: (item: TItem) => TagVariant;
  isDisabled?: boolean;
  items: TItem[];
  onChange: (value: TItem[]) => unknown;
};

const Container = styled(UnstyledUl)`
  display: flex;
  flex-wrap: wrap;
`;

const TagContainer = styled.li`
  margin: 8px 8px 0px 0;
`;

const TagsList = <TItem,>({
  getItemLabel,
  getItemVariant,
  isDisabled,
  items,
  onChange
}: Props<TItem>) => (
  <Container>
    {items.map((item: TItem) => (
      <TagContainer key={getItemLabel(item)}>
        <Tag
          getItemLabel={getItemLabel}
          getItemVariant={getItemVariant}
          isDisabled={isDisabled}
          item={item}
          onRemove={item =>
            onChange(withArray.remove(items, items.indexOf(item), 1))
          }
        />
      </TagContainer>
    ))}
  </Container>
);

export default TagsList;
