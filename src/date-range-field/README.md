# Date Range Field

## About

This component allows users to pick a date between two of them (From, To).

## Example

```tsx
import {useState} from "react";
import styled from "styled-components";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

import type {DateRange} from "@unlockre/components-library/dist/date-range-field";

import {DateRangeField} from "@unlockre/components-library/dist/date-range-field";

const MyApp = () => {
  const [dates, setDates] = useState<DateRange | null>(null);

  return (
    <ThemeProvider>
      <DateRangeField
        {...args}
        onChange={dates => setDates(dates)}
        placeholderFrom="From"
        placeholderTo="To"
        value={dates}
      />
    </ThemeProvider>
  );
};

export default MyApp;
```
