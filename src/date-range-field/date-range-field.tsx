import {useState} from "react";
import type {Ref} from "react";
import styled from "styled-components";

import {DateField, dateFieldInputHeight} from "@/date-field";
import type {DateFieldCalendarElement} from "@/date-field";
import {RangeFieldContainer} from "@/field";
import type {RangeFieldLayout} from "@/field";

type CalendarElement = DateFieldCalendarElement;

type CalendarRef = Ref<CalendarElement>;

type DateRange = {
  dateFrom: Date | null;
  dateTo: Date | null;
};

type Props = {
  className?: string;
  dateFromCalendarRef?: CalendarRef;
  dateFromLabel?: string;
  dateToCalendarRef?: CalendarRef;
  dateToLabel?: string;
  isDisabled?: boolean;
  isRequired?: boolean;
  layout?: RangeFieldLayout;
  maxDateTo?: Date;
  minDateFrom?: Date;
  onChange: (value: DateRange | null) => unknown;
  placeholderFrom?: string;
  placeholderTo?: string;
  value: DateRange | null;
};

const StyledDateField = styled(DateField)`
  flex: 1;
`;

// TODO: Move this one to a utils-date package
const isValidDate = (date: Date) => !isNaN(Number(date));

const DateRangeField = ({
  className,
  dateFromCalendarRef,
  dateFromLabel,
  dateToCalendarRef,
  dateToLabel,
  isDisabled,
  isRequired,
  layout,
  maxDateTo,
  minDateFrom,
  onChange,
  placeholderFrom,
  placeholderTo,
  value
}: Props) => {
  const [invalidDateFrom, setInvalidDateFrom] = useState(false);
  const [invalidDateTo, setInvalidDateTo] = useState(false);

  const saveDateFrom = (date: Date | null) => {
    if (!date || isValidDate(date)) {
      setInvalidDateFrom(false);
      onChange({
        dateFrom: date,
        dateTo: value?.dateTo || null
      });
    } else {
      setInvalidDateFrom(true);
    }
  };

  const saveDateTo = (date: Date | null) => {
    if (!date || isValidDate(date)) {
      setInvalidDateTo(false);
      onChange({
        dateFrom: value?.dateFrom || null,
        dateTo: date
      });
    } else {
      setInvalidDateTo(true);
    }
  };

  return (
    <RangeFieldContainer
      className={className}
      fieldHeight={dateFieldInputHeight}
      fromField={
        <StyledDateField
          calendarRef={dateFromCalendarRef}
          errorMessage="Invalid Date From"
          hasError={invalidDateFrom}
          isDisabled={isDisabled}
          isRequired={isRequired}
          label={dateFromLabel}
          maxDate={value?.dateTo}
          minDate={minDateFrom}
          onChange={saveDateFrom}
          placeholder={placeholderFrom}
          value={value?.dateFrom || null}
        />
      }
      layout={layout}
      toField={
        <StyledDateField
          calendarRef={dateToCalendarRef}
          errorMessage="Invalid Date To"
          hasError={invalidDateTo}
          isDisabled={isDisabled}
          isRequired={isRequired}
          label={dateToLabel}
          maxDate={maxDateTo}
          minDate={value?.dateFrom}
          onChange={saveDateTo}
          placeholder={placeholderTo}
          value={value?.dateTo || null}
        />
      }
    />
  );
};

export {DateRangeField};
export type {DateRange, CalendarElement as DateRangeFieldCalendarElement};
