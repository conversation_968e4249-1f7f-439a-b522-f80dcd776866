import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import type {ComponentProps} from "react";

import {rangeFieldLayouts} from "@/field/range-field-layout";

import {DateRangeField} from "./date-range-field";
import type {DateRange} from "./date-range-field";

type DateRangeFieldProps = ComponentProps<typeof DateRangeField>;

type StoryArgs = Pick<
  DateRangeFieldProps,
  "dateFromLabel" | "dateToLabel" | "isDisabled" | "isRequired" | "layout"
>;

const Default: StoryFn<StoryArgs> = args => {
  const [dates, setDates] = useState<DateRange | null>(null);

  return (
    <DateRangeField
      {...args}
      onChange={dates => setDates(dates)}
      placeholderFrom="From"
      placeholderTo="To"
      value={dates}
    />
  );
};

const meta: Meta<StoryArgs> = {
  title: "date-range-field",
  argTypes: {
    dateFromLabel: {
      control: "text"
    },
    dateToLabel: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    layout: {
      control: "radio",
      options: Object.values(rangeFieldLayouts)
    },
    isRequired: {
      control: "boolean"
    }
  }
};

export {meta as default, Default};
