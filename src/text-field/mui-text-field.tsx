// TODO
// Remove this module once we have migrated DateField and AutocompleteField to
// use the new TextField component

import {TextField} from "@mui/material";
import type {ComponentProps} from "react";
import styled from "styled-components";

import MuiThemeProvider from "@/mui-theme-provider";

type TextFieldProps = ComponentProps<typeof TextField>;

type Props = Omit<TextFieldProps, "fullWidth" | "variant"> & {
  errorText?: string;
};

const StyledTextField = styled(TextField)`
  && .MuiInputBase-root {
    background-color: rgb(255, 255, 255);
    border-radius: 10px;
    font-family: "Inter Variable";
    font-size: 14px;
    padding: 10px 12px;

    &:hover .MuiOutlinedInput-notchedOutline,
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border: 1px solid rgb(115, 115, 115);
    }
  }

  .MuiOutlinedInput-notchedOutline {
    border: 1px solid rgb(191, 191, 191);
  }

  .MuiInputLabel-root {
    font-family: "Inter Variable";
    font-size: 14px;

    &[data-shrink="false"] {
      transform: translate(14px, 12px) scale(1);
    }
  }

  &&& .MuiInputBase-input {
    padding: 0;
  }
`;

/**
 * @deprecated use TextField instead
 */
const MuiTextField = ({
  error,
  errorText,
  helperText,
  inputProps,
  ...rest
}: Props) => (
  <MuiThemeProvider>
    <StyledTextField
      {...{error}}
      {...rest}
      fullWidth
      helperText={error ? errorText : helperText}
      inputProps={{
        ...inputProps,
        autoComplete: "off"
      }}
    />
  </MuiThemeProvider>
);

export default MuiTextField;
