import {forwardRef} from "react";
import type {ComponentRef, ReactChild} from "react";
import styled, {css} from "styled-components";
import type {StyledComponentProps} from "styled-components";

import {FieldBoxContainer} from "@/field";
import {fieldBoxTextCss, fieldPlaceholderCss} from "@/field/field-styles";
import {UnstyledInput} from "@/unstyled";

import {textFieldClassNames} from "./class-names";

type FieldBoxContainerRef = ComponentRef<typeof FieldBoxContainer>;

type InputStyledProps = {
  $hasLeft?: boolean;
  $hasRight?: boolean;
};

type InputProps = StyledComponentProps<
  "input",
  object,
  InputStyledProps,
  never
>;

type ExposedInputProps = Pick<
  InputProps,
  "onBlur" | "onChange" | "onFocus" | "placeholder"
>;

type Props = ExposedInputProps & {
  boxRef?: FieldBoxContainerRef;
  className?: string;
  defaultValue?: string;
  hasError?: boolean;
  isDisabled?: boolean;
  label?: string;
  left?: ReactChild;
  right?: ReactChild;
  type?: "password" | "text";
  value?: string;
  withoutPaddingLeft?: boolean;
  withoutPaddingRight?: boolean;
};

const inputHorizontalMargin = 4;

const inputWithRightCss = css`
  margin-right: ${inputHorizontalMargin}px;
`;

const getInputWithRightCssIfNeeded = (props: InputProps) =>
  props.$hasRight && inputWithRightCss;

const inputWithLeftCss = css`
  margin-left: ${inputHorizontalMargin}px;
`;

const getInputWithLeftCssIfNeeded = (props: InputProps) =>
  props.$hasLeft && inputWithLeftCss;

const Input = styled(UnstyledInput)<InputStyledProps>`
  ${fieldBoxTextCss}
  ${getInputWithLeftCssIfNeeded}
  ${getInputWithRightCssIfNeeded}

  flex: 1;
  min-width: 0;

  &::placeholder {
    ${fieldPlaceholderCss};
  }
`;

const containerHeight = 48;

const Container = styled(FieldBoxContainer)`
  align-items: center;
  height: ${containerHeight}px;
`;

const TextInput = forwardRef<HTMLInputElement, Props>(
  (
    {
      boxRef,
      className,
      hasError,
      isDisabled,
      label,
      left,
      right,
      withoutPaddingLeft,
      withoutPaddingRight,
      ...rest
    },
    ref
  ) => (
    <Container
      {...{className}}
      $hasError={hasError}
      $isDisabled={isDisabled}
      $withoutPaddingLeft={withoutPaddingLeft}
      $withoutPaddingRight={withoutPaddingRight}
      ref={boxRef}
    >
      {left}
      <Input
        {...{ref}}
        {...rest}
        $hasLeft={left !== undefined}
        $hasRight={right !== undefined}
        aria-label={label}
        className={textFieldClassNames.input}
        disabled={isDisabled}
      />
      {right}
    </Container>
  )
);

export default Object.assign(TextInput, {height: containerHeight});
