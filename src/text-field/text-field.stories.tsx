import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import TextField from "./text-field";

type TextFieldProps = ComponentProps<typeof TextField>;

type Args = Pick<
  TextFieldProps,
  | "errorMessage"
  | "hasError"
  | "infoMessage"
  | "isDisabled"
  | "isRequired"
  | "label"
  | "placeholder"
  | "value"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return (
    <TextField
      {...args}
      onChange={event => updateArgs({value: event.target.value})}
    />
  );
};

const meta: Meta<Args> = {
  title: "text-field",
  argTypes: {
    hasError: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    isDisabled: {
      control: "boolean"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    },
    value: {
      control: "text"
    }
  }
};

export {meta as default, Default};
