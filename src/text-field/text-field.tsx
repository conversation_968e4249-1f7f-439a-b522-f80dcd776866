import {forwardRef} from "react";
import type {ComponentProps, ComponentRef} from "react";

import {FieldContainer} from "@/field";

import {textFieldClassNames} from "./class-names";
import TextInput from "./text-input";

type TextInputRef = ComponentRef<typeof TextInput>;

type TextInputProps = ComponentProps<typeof TextInput>;

type ExposedTextInputProps = Omit<TextInputProps, "type">;

type FieldContainerProps = ComponentProps<typeof FieldContainer>;

type ExposedFieldContainerProps = Omit<FieldContainerProps, "children">;

type Props = ExposedFieldContainerProps & ExposedTextInputProps;

const TextField = forwardRef<TextInputRef, Props>(
  (
    {
      boxRef,
      defaultValue,
      hasError,
      isDisabled,
      label,
      left,
      onBlur,
      onChange,
      onFocus,
      placeholder,
      right,
      value,
      withoutPaddingLeft,
      withoutPaddingRight,
      ...containerProps
    },
    ref
  ) => (
    <FieldContainer {...{hasError, label, isDisabled}} {...containerProps}>
      <TextInput
        {...{
          defaultValue,
          hasError,
          boxRef,
          isDisabled,
          label,
          left,
          onBlur,
          onChange,
          onFocus,
          placeholder,
          ref,
          right,
          value,
          withoutPaddingLeft,
          withoutPaddingRight
        }}
        className={textFieldClassNames.inputContainer}
      />
    </FieldContainer>
  )
);

export default Object.assign(TextField, {
  textInputHeight: TextInput.height
});
