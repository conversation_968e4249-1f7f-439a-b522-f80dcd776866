import colorAlpha from "color-alpha";
import {forwardRef} from "react";
import type {CSSProperties, ForwardedRef, ReactNode, RefObject} from "react";
import styled, {css} from "styled-components";

import Portal from "@/portal";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import {TooltipArrow} from "./tooltip-arrow";
import type {TooltipVariant} from "./tooltip-variant";

type ContainerElement = HTMLDivElement;

type TooltipArrowElement = HTMLDivElement;

type Size = "large" | "small";

type Props = {
  arrowRef: ForwardedRef<TooltipArrowElement>;
  arrowStyle: CSSProperties;
  children: ReactNode;
  className?: string;
  onMouseEnter?: () => unknown;
  onMouseLeave?: () => unknown;
  parent?: RefObject<HTMLElement>;
  size: Size;
  style?: CSSProperties;
  variant: TooltipVariant;
};

type ContainerStyledProps = {
  $size: Size;
  $variant: TooltipVariant;
};

const containerWhiteCss = css`
  background: ${getColorByAlias("backgroundWhite")};
  color: ${getColorByAlias("textPrimary")};
`;

const containerDarkCss = css`
  background: ${getColor("gray", "900")};
  color: ${getColorByAlias("textInverted")};
`;

const containerLargeCss = css`
  ${getTypography("body", "s")}

  padding: 16px;
`;

const containerSmallCss = css`
  ${getTypography("body", "xs")}

  padding: 8px;
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => (props.$size === "small" ? containerSmallCss : containerLargeCss)}
  ${props => (props.$variant === "dark" ? containerDarkCss : containerWhiteCss)}

  border-radius: 6px;
  box-shadow: 0px 4px 58px
    ${props => colorAlpha(getColor("blue", "900")(props), 0.1)};
`;

const Tooltip = forwardRef<ContainerElement, Props>(
  ({arrowRef, arrowStyle, children, parent, size, variant, ...rest}, ref) => (
    <Portal parent={parent?.current ?? undefined}>
      <Container {...rest} {...{ref}} $size={size} $variant={variant}>
        {children}
        <TooltipArrow $variant={variant} ref={arrowRef} style={arrowStyle} />
      </Container>
    </Portal>
  )
);

export default Tooltip;
export type {TooltipArrowElement, Size as TooltipSize};
