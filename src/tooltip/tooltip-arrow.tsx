import styled, {css} from "styled-components";

import {getColor} from "@/theme-provider/theme";

import type {TooltipVariant} from "./tooltip-variant";

type StyledProps = {
  $variant: TooltipVariant;
};

const height = 8;

const tooltipArrowWhiteCss = css`
  border-top: ${height}px solid ${getColor("gray", "000")};
`;

const tooltipArrowDarkCss = css`
  border-top: ${height}px solid ${getColor("gray", "900")};
`;

const TooltipArrow = styled.div<StyledProps>`
  ${props =>
    props.$variant === "dark" ? tooltipArrowDarkCss : tooltipArrowWhiteCss}

  border-left: ${height}px solid transparent;
  border-right: ${height}px solid transparent;

  height: 0px;
  position: absolute;
  width: 0px;
`;

export {TooltipArrow, height as tooltipArrowHeight};
