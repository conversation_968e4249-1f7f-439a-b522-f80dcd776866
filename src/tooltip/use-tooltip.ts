import {
  arrow,
  autoUpdate,
  flip,
  offset,
  useFloating
} from "@floating-ui/react-dom";
import type {Placement} from "@floating-ui/react-dom";
import {useCallback, useRef, useState} from "react";

import type {TooltipArrowElement} from "./tooltip";
import {tooltipArrowHeight} from "./tooltip-arrow";
import type {TooltipPlacement} from "./tooltip-placement";

type TooltipOptions = {
  fallbackAxisSideDirection?: "end" | "start";
  offset?: number;
};

// eslint-disable-next-line max-statements
const useTooltip = (
  tooltipPlacement: TooltipPlacement,
  {fallbackAxisSideDirection, offset: tooltipOffset = 4}: TooltipOptions = {}
) => {
  const [isTooltipOpened, setIsTooltipOpened] = useState(false);

  const tooltipArrowRef = useRef<TooltipArrowElement | null>(null);

  const showTooltip = useCallback(
    () => setIsTooltipOpened(true),
    [setIsTooltipOpened]
  );

  const hideTooltip = useCallback(
    () => setIsTooltipOpened(false),
    [setIsTooltipOpened]
  );

  const toggleTooltip = useCallback(
    () => setIsTooltipOpened(currentState => !currentState),
    [setIsTooltipOpened]
  );

  const offsetValue = tooltipArrowHeight + tooltipOffset;

  const {
    floating,
    middlewareData: {arrow: {x: arrowX, y: arrowY} = {}},
    placement,
    reference,
    strategy,
    update,
    x,
    y
  } = useFloating({
    placement: tooltipPlacement,
    middleware: [
      flip({
        fallbackAxisSideDirection
      }),
      offset(offsetValue),
      arrow({
        element: tooltipArrowRef
      })
    ],
    strategy: "fixed",
    whileElementsMounted: autoUpdate
  });

  const arrowCallback = useCallback(
    (tooltipArrowElement: TooltipArrowElement | null) => {
      tooltipArrowRef.current = tooltipArrowElement;

      update();
    },
    [update]
  );

  const getArrowStyle = (
    placement: Placement,
    arrowX: number | undefined,
    arrowY: number | undefined
  ) => {
    switch (placement) {
      case "bottom":
        return {
          top: `-${tooltipArrowHeight}px`,
          left: arrowX,
          transform: "rotate(180deg)"
        };

      case "left":
        return {
          top: arrowY,
          right: `-${tooltipArrowHeight + 4}px`,
          transform: "rotate(270deg)"
        };

      case "right":
        return {
          top: arrowY,
          left: `-${tooltipArrowHeight + 4}px`,
          transform: "rotate(90deg)"
        };

      case "top":
        return {
          bottom: `-${tooltipArrowHeight}px`,
          left: arrowX
        };

      default:
        throw new Error(`Unknown placement: ${placement}`);
    }
  };

  const tooltipProps = {
    arrowRef: arrowCallback,
    arrowStyle: getArrowStyle(placement, arrowX, arrowY),
    ref: floating,
    style: {
      position: strategy,
      top: y ?? "",
      left: x ?? ""
    }
  };

  return {
    hideTooltip,
    isTooltipOpened,
    tooltipProps,
    reference,
    showTooltip,
    toggleTooltip
  };
};

export default useTooltip;
