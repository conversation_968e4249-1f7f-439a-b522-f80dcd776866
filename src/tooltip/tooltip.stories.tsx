import type {<PERSON><PERSON>, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import Button from "@/button";

import Tooltip from "./tooltip";
import {tooltipPlacements} from "./tooltip-placement";
import type {TooltipPlacement} from "./tooltip-placement";
import {tooltipVariants} from "./tooltip-variant";
import useTooltip from "./use-tooltip";

type TooltipProps = ComponentProps<typeof Tooltip>;

type ExposedTooltipProps = Pick<TooltipProps, "children" | "size" | "variant">;

type Args = ExposedTooltipProps & {
  tooltipPlacement: TooltipPlacement;
};

const StyledButton = styled(Button)`
  display: block;
  margin: 30px auto;
`;

const StyledTooltip = styled(Tooltip)`
  min-width: 140px;
  text-align: left;
`;

const Default: StoryFn<Args> = args => {
  const {isTooltipOpened, reference, toggleTooltip, tooltipProps} = useTooltip(
    args.tooltipPlacement
  );

  return (
    <>
      <StyledButton
        onClick={toggleTooltip}
        ref={reference}
        size="small"
        variant="primary"
      >
        Show!
      </StyledButton>
      {isTooltipOpened && <StyledTooltip {...tooltipProps} {...args} />}
    </>
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/T0jvDtEMM7maBpAYKJ5GUG/Keyway---Main-library?node-id=348%3A1407"
  }
};

const meta: Meta<Args> = {
  title: "tooltip",
  argTypes: {
    children: {
      control: "text"
    },
    size: {
      control: "select",
      options: ["small", "large"]
    },
    tooltipPlacement: {
      control: "select",
      options: Object.values(tooltipPlacements)
    },
    variant: {
      control: "select",
      options: Object.values(tooltipVariants)
    }
  },
  args: {
    children: "This is a tooltip",
    size: "small",
    tooltipPlacement: "bottom",
    variant: "dark"
  }
};

export {meta as default, Default};
