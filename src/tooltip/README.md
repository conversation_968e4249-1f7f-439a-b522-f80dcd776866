# Tooltip

## About

This component renders a tooltip that will be placed on top of the component chosen as container.

Its style is based on a size property that could take two different values: "small" and "large".

A reference to the container and the tooltip state and handlers to change it are provided through the `useTooltip` hook.

This hook receives a placement param ("bottom" | "left" | "right" | "top") to display the tooltip.

## State handlers

Provided handlers are:

- `toggleTooltip`: use this handler when the state change is triggered by a single event.
E.g. toggle state on click.

- `showTooltip`/`hideTooltip`: use these handlers when the state change is triggered by more than one event as asynchrony in event processing could cause unexpected behavior on state change in case of using `toggleTooltip`.
E.g. show on mouse enter and hide on mouse leave.

## Examples

### Example using toggleTooltip

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Tooltip, {useTooltip} from "@unlockre/components-library/dist/tooltip";
import {tooltipPlacements} from "@unlockre/components-library/dist/tooltip-placement";

const StyledButton = styled.button`
  display: block;
  margin: 0 auto;
`;

const StyledTooltip = styled(Tooltip)`
  min-width: 140px;
  text-align: left;
`;

const ToggleTooltipOnClick = () => {
  const {isTooltipOpened, reference, toggleTooltip, tooltipProps} =
    useTooltip(tooltipPlacements.bottom);

  return (
    <ThemeProvider>
      <StyledButton onClick={toggleTooltip} ref={reference}>
        Show on click!
      </StyledButton>
      {isTooltipOpened && (
        <StyledTooltip size="small" variant="white" {...tooltipProps}>
          This is a tooltip
        </StyledTooltip>
      )}
    </ThemeProvider>
  )
};

export default ToggleTooltipOnClick;
```

### Example using showTooltip/hideTooltip

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import Tooltip, {useTooltip} from "@unlockre/components-library/dist/tooltip";
import {tooltipPlacements} from "@unlockre/components-library/dist/tooltip-placement";

const StyledButton = styled.button`
  display: block;
  margin: 0 auto;
`;

const StyledTooltip = styled(Tooltip)`
  min-width: 140px;
  text-align: left;
`;

const ToggleTooltipOnHover = () => {
  const {hideTooltip, isTooltipOpened, reference, showTooltip, tooltipProps} =
    useTooltip(tooltipPlacements.bottom);

  return (
    <ThemeProvider>
      <StyledButton
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        ref={reference}
      >
        Show on hover!
      </StyledButton>
      {isTooltipOpened && (
        <StyledTooltip size="small" variant="dark" {...tooltipProps}>
          This is a tooltip
        </StyledTooltip>
      )}
    </ThemeProvider>
  )
};

export default ToggleTooltipOnHover;
```