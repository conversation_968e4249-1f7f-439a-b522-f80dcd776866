import styled from "styled-components";

import Checkbox from "@/checkbox";
import type {CheckboxSize} from "@/checkbox/checkbox-size";
import type {GetColor} from "@/theme-provider/theme";
import {UnstyledUl} from "@/unstyled";

type Direction = "horizontal" | "vertical";

type ContainerStyledProps = {
  $direction: Direction;
};

type Props<TItem> = {
  className?: string;
  direction: Direction;
  getItemColor?: (item: TItem) => GetColor;
  getItemLabel: (item: TItem) => string;
  isDisabled?: boolean;
  items: TItem[];
  onChange: (items: TItem[]) => unknown;
  selectedItems: TItem[];
  size: CheckboxSize;
};

const Container = styled(UnstyledUl)<ContainerStyledProps>`
  display: flex;
  flex-direction: ${props =>
    props.$direction === "horizontal" ? "row" : "column"};
  gap: 18px;
`;

const CheckboxGroup = <TItem,>({
  direction,
  getItemColor,
  getItemLabel,
  isDisabled,
  items,
  onChange,
  selectedItems,
  size,
  ...rest
}: Props<TItem>) => {
  const handleItemChange = (item: TItem) => {
    const selected = selectedItems.includes(item)
      ? selectedItems.filter(selectedItem => selectedItem !== item)
      : [...selectedItems, item];

    onChange(selected);
  };

  return (
    <Container $direction={direction} {...rest}>
      {items.map(item => (
        <li key={getItemLabel(item)}>
          <Checkbox
            {...{isDisabled, size}}
            getColor={getItemColor?.(item)}
            isChecked={selectedItems.includes(item)}
            label={getItemLabel(item)}
            onChange={() => handleItemChange(item)}
          />
        </li>
      ))}
    </Container>
  );
};

export default CheckboxGroup;

export type {Props as CheckboxGroupProps, Direction as CheckboxGroupDirection};
