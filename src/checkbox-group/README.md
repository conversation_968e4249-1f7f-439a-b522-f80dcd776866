# CheckboxGroup

## About

This component renders a group of checkboxes from a list of items returning the ones that have been selected (checking the corresponding checkbox).

## Examples

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import CheckboxGroup from "@unlockre/components-library/dist/checkbox-group";

type MyItem = {
  label: string;
}

const items = [
  {
    label: "Item 1"
  },
  {
    label: "Item 2"
  }
];

const MyComponent = () => {
  const [selectedItems, setSelectedItems] = useState<MyItem[]>(items);

  const getItemLabel = (item: MyItem) => item.label;

  return(

    <ThemeProvider>
      <CheckboxGroup
        {...{getItemLabel, items, selectedItems}}
        direction="vertical"
        onChange={setSelectedItems}
        size="medium"
      />
    </ThemeProvider>
  )
};

export default MyComponent;
```
