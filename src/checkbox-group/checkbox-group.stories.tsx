import type {Meta, StoryFn} from "@storybook/react";
import {useEffect, useState} from "react";

import CheckboxGroup from "./checkbox-group";
import type {CheckboxGroupProps} from "./checkbox-group";

type CheckboxItem = {
  label: string;
};

type Args = Omit<CheckboxGroupProps<CheckboxItem>, "getItemLabel" | "onChange">;

const getItemLabel = (item: CheckboxItem) => item.label;

const Default: StoryFn<Args> = args => {
  const [selectedItems, setSelectedItems] = useState<CheckboxItem[]>([]);

  useEffect(() => {
    setSelectedItems([]);
  }, [args.items]);

  return (
    <CheckboxGroup
      {...{getItemLabel, selectedItems}}
      {...args}
      onChange={setSelectedItems}
    />
  );
};

const meta: Meta<Args> = {
  title: "checkbox-group",
  argTypes: {
    direction: {
      control: "radio",
      options: ["horizontal", "vertical"]
    },
    isDisabled: {
      control: "boolean"
    },
    items: {
      control: "object"
    },
    size: {
      control: "radio",
      options: ["small", "medium"]
    }
  },
  args: {
    direction: "vertical",
    items: [{label: "one"}, {label: "two"}, {label: "three"}],
    size: "medium"
  }
};

export {meta as default, Default};
