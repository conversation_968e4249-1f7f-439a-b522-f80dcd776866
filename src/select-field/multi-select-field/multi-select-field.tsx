import {MultiItemList} from "@/item-list";
import type {MultiItemListProps} from "@/item-list";

import MultiSelectFieldContainer from "./multi-select-field-container";
import type {
  MultiSelectFieldContainerProps,
  RenderItemListParams
} from "./multi-select-field-container";

type RenderItemList<TItem> =
  MultiSelectFieldContainerProps<TItem>["renderItemList"];

type ExposedMultiItemListProps<TItem> = Omit<
  MultiItemListProps<TItem>,
  keyof RenderItemListParams<TItem>["itemListProps"] | "footer"
>;

type ExposedSelectFieldContainerProps<TItem> = Omit<
  MultiSelectFieldContainerProps<TItem>,
  "onClear" | "onItemSelect" | "renderItemList" | "valueLabel"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedMultiItemListProps<TItem>
  & ExposedSelectFieldContainerProps<TItem>;

const MultiSelectField = <TItem,>({
  getItemLabel,
  isRequired = false,
  onChange,
  value,
  ...selectFieldContainerProps
}: Props<TItem>) => {
  const renderMultiItemList: RenderItemList<TItem> = ({itemListProps}) => (
    <MultiItemList {...{getItemLabel, value, onChange}} {...itemListProps} />
  );

  const handleItemSelect = (item: TItem, event: KeyboardEvent) => {
    event.preventDefault();

    if (MultiItemList.canChangeValue(value, item, isRequired)) {
      onChange(MultiItemList.changeValue(value, item));
    }
  };

  return (
    <MultiSelectFieldContainer
      {...{getItemLabel}}
      {...selectFieldContainerProps}
      isRequired={isRequired}
      onItemSelect={handleItemSelect}
      renderItemList={renderMultiItemList}
    />
  );
};

export default MultiSelectField;

export type {Props as MultiSelectFieldProps};
