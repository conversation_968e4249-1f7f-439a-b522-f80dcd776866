import type {ComponentProps, ReactElement} from "react";

import type {UseMultiItemListParams, UseMultiItemListResult} from "@/item-list";
import {useMultiItemList} from "@/item-list/multi-item-list";

import SelectFieldContainer from "../select-field-container";

type RenderItemListParams<TItem> = Pick<
  UseMultiItemListResult<TItem>,
  "itemListProps" | "toggleItemList"
>;

type RenderItemList<TItem> = (
  params: RenderItemListParams<TItem>
) => ReactElement;

type SelectFieldContainerProps = ComponentProps<typeof SelectFieldContainer>;

type ExposedSelectFieldContainerProps = Omit<
  SelectFieldContainerProps,
  | "children"
  | "isItemListOpened"
  | "onItemListToggle"
  | "selectBoxRef"
  | "selectBoxTextRef"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedSelectFieldContainerProps
  & UseMultiItemListParams<TItem>
  & {
      renderItemList: RenderItemList<TItem>
    }

const MultiSelectFieldContainer = <TItem,>({
  getItemLabel,
  itemGroups,
  items,
  onItemSelect,
  renderFooter,
  renderItemList,
  ...rest
}: Props<TItem>) => {
  const {
    isItemListOpened,
    itemListProps,
    setAnchorageElement,
    setControlElement,
    toggleItemList
  } = useMultiItemList({
    getItemLabel,
    itemGroups,
    items,
    onItemSelect,
    renderFooter
  });

  return (
    <SelectFieldContainer
      isItemListOpened={isItemListOpened}
      onItemListToggle={toggleItemList}
      selectBoxRef={setAnchorageElement}
      selectBoxTextRef={setControlElement}
      {...rest}
    >
      {isItemListOpened && renderItemList({itemListProps, toggleItemList})}
    </SelectFieldContainer>
  );
};

export default MultiSelectFieldContainer;

export type {Props as MultiSelectFieldContainerProps, RenderItemListParams};
