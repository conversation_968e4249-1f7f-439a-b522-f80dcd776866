import type {ComponentProps, ComponentRef, ReactNode, Ref} from "react";
import styled from "styled-components";

import {FieldContainer} from "@/field";

import SelectBox from "./select-box";

type SelectBoxRef = Ref<ComponentRef<typeof SelectBox>>;

type SelectBoxProps = ComponentProps<typeof SelectBox>;

type ExposedSelectBoxProps = Pick<
  SelectBoxProps,
  "onClear" | "placeholder" | "valueLabel"
>;

type FieldContainerProps = ComponentProps<typeof FieldContainer>;

type ExposedFieldContainerProps = Omit<
  FieldContainerProps,
  "children" | "hasError" | "isDisabled" | "isRequired"
>;

// prettier-ignore
type Props =
  & ExposedFieldContainerProps
  & ExposedSelectBoxProps
  & {
      children: ReactNode;
      hasError?: boolean;
      isDisabled?: boolean;
      isItemListOpened?: boolean;
      isRequired?: boolean;
      onItemListToggle: () => unknown;
      selectBoxRef: SelectBoxRef;
      selectBoxTextRef: SelectBoxProps["textRef"];
      toggleButtonTestId?: string;
    }

const Container = styled(FieldContainer)`
  position: relative;
`;

const SelectFieldContainer = ({
  children,
  hasError,
  isDisabled,
  isItemListOpened,
  isRequired,
  onClear,
  onItemListToggle,
  placeholder,
  selectBoxRef,
  selectBoxTextRef,
  toggleButtonTestId,
  valueLabel,
  ...containerProps
}: Props) => {
  const handleClear = () => {
    onClear?.();

    if (isItemListOpened) {
      onItemListToggle();
    }
  };

  const renderSelectBox = () => (
    <SelectBox
      {...{
        hasError,
        isDisabled,
        isRequired,
        isItemListOpened,
        onItemListToggle,
        placeholder,
        toggleButtonTestId,
        valueLabel
      }}
      onClear={handleClear}
      ref={selectBoxRef}
      textRef={selectBoxTextRef}
    />
  );

  return (
    <Container {...{hasError, isDisabled, isRequired}} {...containerProps}>
      {renderSelectBox()}
      {children}
    </Container>
  );
};

export default SelectFieldContainer;
