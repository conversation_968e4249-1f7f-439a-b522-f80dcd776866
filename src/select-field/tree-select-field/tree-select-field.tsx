import {TreeItemList} from "@/item-list";
import type {TreeItemListProps} from "@/item-list";
import type {TreeListItem} from "@/item-list/tree-item-list/types";

import TreeSelectFieldContainer from "./tree-select-field-container";
import type {
  RenderItemListParams,
  SelectFieldContainerProps
} from "./tree-select-field-container";

type RenderItemList<TItem> = SelectFieldContainerProps<TItem>["renderItemList"];

type ExposedTreeItemListProps<TItem> = Omit<
  TreeItemListProps<TItem>,
  | keyof RenderItemListParams<TItem>["itemListProps"]
  | "childrens"
  | "groupedItems"
  | "onParentChange"
  | "onToggle"
  | "toggledItemIds"
>;

type ExposedSelectFieldContainerProps<TItem> = Omit<
  SelectFieldContainerProps<TItem>,
  | "getItemLabel"
  | "itemListProps"
  | "items"
  | "onClear"
  | "onItemSelect"
  | "renderItemList"
  | "valueLabel"
>;

// prettier-ignore
type Props<TItem> =
	  & ExposedSelectFieldContainerProps<TItem>
	  & ExposedTreeItemListProps<TItem>
	  & {items: TreeListItem<TItem>[]};

const TreeSelectField = <TItem,>({
  getItemLabel,
  isRequired = false,
  items,
  onChange,
  value,
  ...selectFieldContainerProps
}: Props<TItem>) => {
  const renderTreeItemList: RenderItemList<TItem> = ({
    itemListProps,
    itemsGroupedByParent,
    onParentChange,
    toggledItems,
    toggleItem,
    visibleItems
  }) => (
    <TreeItemList
      {...{getItemLabel, value, onChange}}
      {...itemListProps}
      focusedItem={itemListProps.focusedItem}
      groupedItems={itemsGroupedByParent}
      items={visibleItems}
      onItemHover={itemListProps.onItemHover}
      onParentChange={onParentChange(onChange, value)}
      onToggle={toggleItem}
      toggledItemIds={toggledItems}
    />
  );

  const handleItemSelect = (
    item: TreeListItem<TItem>,
    event: KeyboardEvent
  ) => {
    event.preventDefault();
    if (TreeItemList.canChangeValue(value, item, isRequired)) {
      onChange(TreeItemList.changeValue(value, item));
    }
  };

  return (
    <TreeSelectFieldContainer
      {...selectFieldContainerProps}
      getItemLabel={getItemLabel}
      items={items}
      onItemSelect={handleItemSelect}
      renderItemList={renderTreeItemList}
    />
  );
};

export default TreeSelectField;

export type {Props as TreeSelectFieldProps};
