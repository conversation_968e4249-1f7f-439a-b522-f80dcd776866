import type {ComponentProps, ReactElement} from "react";

import type {FieldContainer} from "@/field";
import type {UseTreeItemListResult} from "@/item-list";
import {useTreeItemList} from "@/item-list/tree-item-list";
import type {TreeListItem} from "@/item-list/tree-item-list/types";

import type SelectBox from "../select-box";
import SelectFieldContainer from "../select-field-container";

type RenderItemListParams<TItem> = Pick<
  UseTreeItemListResult<TItem>,
  | "itemListProps"
  | "itemsGroupedByParent"
  | "onParentChange"
  | "setToggledItems"
  | "toggledItems"
  | "toggleItem"
  | "toggleItemList"
  | "visibleItems"
>;

type RenderItemList<TItem> = (
  params: RenderItemListParams<TItem>
) => ReactElement;

type SelectBoxProps = ComponentProps<typeof SelectBox>;

type ExposedSelectBoxProps = Pick<
  SelectBoxProps,
  "onClear" | "placeholder" | "valueLabel"
>;

type FieldContainerProps = ComponentProps<typeof FieldContainer>;

type ExposedFieldContainerProps = Omit<
  FieldContainerProps,
  "children" | "hasError" | "isDisabled" | "isItemListOpened" | "isRequired"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedFieldContainerProps
  & ExposedSelectBoxProps
  & {
      getItemLabel: (item: TreeListItem<TItem>) => string;
      hasError?: boolean;
      isDisabled?: boolean;
      isRequired?: boolean;
      items: TreeListItem<TItem>[];
      onItemSelect: (item: TreeListItem<TItem>, event: KeyboardEvent) => unknown;
      renderItemList: RenderItemList<TItem>;
    }

const TreeSelectFieldContainer = <TItem,>({
  getItemLabel,
  items,
  onItemSelect,
  renderItemList,
  ...rest
}: Props<TItem>) => {
  const {
    isItemListOpened,
    itemListProps,
    itemsGroupedByParent,
    onParentChange,
    setAnchorageElement,
    setControlElement,
    setToggledItems,
    toggledItems,
    toggleItem,
    toggleItemList,
    visibleItems
  } = useTreeItemList({
    getItemLabel,
    items,
    onItemSelect
  });

  return (
    <SelectFieldContainer
      isItemListOpened={isItemListOpened}
      onItemListToggle={toggleItemList}
      selectBoxRef={setAnchorageElement}
      selectBoxTextRef={setControlElement}
      {...rest}
    >
      {isItemListOpened
        ? renderItemList({
            itemListProps,
            itemsGroupedByParent,
            onParentChange,
            setToggledItems,
            toggleItemList,
            toggleItem,
            toggledItems,
            visibleItems
          })
        : null}
    </SelectFieldContainer>
  );
};

export default TreeSelectFieldContainer;

export type {Props as SelectFieldContainerProps, RenderItemListParams};
