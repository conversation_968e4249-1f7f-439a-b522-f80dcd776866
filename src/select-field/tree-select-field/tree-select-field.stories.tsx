import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import type {TreeListItem} from "@/item-list/tree-item-list/types";

import TreeSelectField from "./tree-select-field";
import type {TreeSelectFieldProps} from "./tree-select-field";

type TreeSelectFieldItem = {
  label: string;
  value: number;
};

type StoryArgs = Omit<
  TreeSelectFieldProps<TreeSelectFieldItem>,
  "getItemLabel" | "onItemSelect" | "value"
>;

const getTreeSelectFieldItemLabel = (item: TreeListItem<TreeSelectFieldItem>) =>
  item.value.label;

const Default: StoryFn<StoryArgs> = args => {
  const [value, setValue] = useState<TreeListItem<TreeSelectFieldItem>[]>([]);

  return (
    <TreeSelectField
      {...{value}}
      {...args}
      getItemLabel={getTreeSelectFieldItemLabel}
      onChange={setValue}
    />
  );
};

Default.parameters = {
  design: {
    control: "figma",
    url: "https://www.figma.com/file/T0jvDtEMM7maBpAYKJ5GUG/Keyway---Main-library?node-id=427%3A1613"
  }
};

const meta: Meta<StoryArgs> = {
  title: "tree-select-field",
  argTypes: {
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    items: {
      control: "object"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    }
  },
  args: {
    items: [
      {value: {label: "one", value: 1}, id: "1", hasChildren: true},
      {
        value: {label: "eleven", value: 11},
        id: "11",
        hasChildren: false,
        ancestorIds: ["1"]
      },
      {
        value: {label: "twelve", value: 12},
        id: "12",
        hasChildren: true,
        ancestorIds: ["1"]
      },
      {
        value: {label: "one hundred and twenty one", value: 121},
        id: "121",
        hasChildren: false,
        ancestorIds: ["1", "12"]
      },
      {value: {label: "two", value: 2}, id: "1", hasChildren: false}
    ]
  }
};

export {meta as default, Default};
