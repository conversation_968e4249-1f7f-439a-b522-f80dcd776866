import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import SingleSelectField from "./single-select-field";
import type {SingleSelectFieldProps} from "./single-select-field";

const inputTypes = {
  itemGroups: "itemGroups",
  items: "items"
} as const;

type InputType = (typeof inputTypes)[keyof typeof inputTypes];

type SingleSelectFieldItem = {
  label: string;
  value: number;
};

type SingleSelectFieldArgs = Omit<
  SingleSelectFieldProps<SingleSelectFieldItem>,
  "getItemLabel" | "onItemSelect" | "value"
>;

type StoryArgs = SingleSelectFieldArgs & {
  inputType: InputType;
};

const getSingleSelectFieldItemLabel = ({label}: SingleSelectFieldItem) => label;

const Default: StoryFn<StoryArgs> = ({
  inputType,
  itemGroups,
  items,
  ...rest
}) => {
  const [value, setValue] = useState<SingleSelectFieldItem | null>(null);

  const inputArgs =
    inputType === inputTypes.itemGroups ? {itemGroups} : {items};

  return (
    <SingleSelectField
      {...{value}}
      {...inputArgs}
      {...rest}
      getItemLabel={getSingleSelectFieldItemLabel}
      onChange={setValue}
      toggleButtonTestId="test-dropdown-id"
    />
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/T0jvDtEMM7maBpAYKJ5GUG/Keyway---Main-library?node-id=427%3A1613"
  }
};

const meta: Meta<StoryArgs> = {
  title: "single-select-field",
  argTypes: {
    errorMessage: {
      control: "text"
    },
    infoMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    },
    isRequired: {
      control: "boolean"
    },
    inputType: {
      control: {control: "radio"},
      options: Object.values(inputTypes)
    },
    itemGroups: {
      control: "object"
    },
    items: {
      control: "object"
    },
    label: {
      control: "text"
    },
    placeholder: {
      control: "text"
    }
  },
  args: {
    inputType: inputTypes.items,
    itemGroups: [
      {
        name: "group1",
        label: "Group 1",
        items: [
          {label: "one", value: 1},
          {label: "two", value: 2},
          {label: "three", value: 3}
        ]
      },
      {
        name: "group2",
        label: "Group 2",
        items: [
          {label: "four", value: 4},
          {label: "five", value: 5},
          {label: "six", value: 6}
        ]
      },
      {
        name: "group3",
        label: "Group 3",
        items: [
          {label: "seven", value: 7},
          {label: "eight", value: 8},
          {label: "nine", value: 9}
        ]
      }
    ],
    items: [
      {label: "one", value: 1},
      {label: "two", value: 2},
      {label: "three", value: 3},
      {label: "four", value: 4},
      {label: "five", value: 5},
      {label: "six", value: 6},
      {label: "seven", value: 7},
      {label: "eight", value: 8},
      {label: "nine", value: 9},
      {label: "ten", value: 10},
      {label: "eleven", value: 11},
      {label: "twelve", value: 12},
      {label: "thirteen", value: 13},
      {label: "fourteen", value: 14},
      {label: "fifteen", value: 15},
      {label: "sixteen", value: 16},
      {label: "seventeen", value: 17},
      {label: "eighteen", value: 18},
      {label: "nineteen", value: 19},
      {label: "twenty", value: 20}
    ]
  }
};

export {meta as default, Default};
