import type {ComponentProps, ReactElement} from "react";

import {useSingleItemList} from "@/item-list";
import type {
  UseSingleItemListParams,
  UseSingleItemListResult
} from "@/item-list";

import SelectFieldContainer from "../select-field-container";

type RenderItemListParams<TItem> = Pick<
  UseSingleItemListResult<TItem>,
  "itemListProps" | "toggleItemList"
>;

type RenderItemList<TItem> = (
  params: RenderItemListParams<TItem>
) => ReactElement;

type SelectFieldContainerProps = ComponentProps<typeof SelectFieldContainer>;

type ExposedSelectFieldContainerProps = Omit<
  SelectFieldContainerProps,
  | "children"
  | "isItemListOpened"
  | "onItemListToggle"
  | "selectBoxRef"
  | "selectBoxTextRef"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedSelectFieldContainerProps
  & UseSingleItemListParams<TItem>
  & {
      renderItemList: RenderItemList<TItem>;
    }

const SingleSelectFieldContainer = <TItem,>({
  getItemLabel,
  itemGroups,
  items,
  onItemSelect,
  renderFooter,
  renderItemList,
  ...rest
}: Props<TItem>) => {
  const {
    isItemListOpened,
    itemListProps,
    setAnchorageElement,
    setControlElement,
    toggleItemList
  } = useSingleItemList({
    getItemLabel,
    items,
    itemGroups,
    onItemSelect,
    renderFooter
  });

  return (
    <SelectFieldContainer
      isItemListOpened={isItemListOpened}
      onItemListToggle={toggleItemList}
      selectBoxRef={setAnchorageElement}
      selectBoxTextRef={setControlElement}
      {...rest}
    >
      {isItemListOpened && renderItemList({itemListProps, toggleItemList})}
    </SelectFieldContainer>
  );
};

export default SingleSelectFieldContainer;

export type {Props as SingleSelectFieldContainerProps, RenderItemListParams};
