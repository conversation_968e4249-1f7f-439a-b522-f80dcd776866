import {SingleItemList} from "@/item-list";
import type {SingleItemListProps} from "@/item-list";

import SingleSelectFieldContainer from "./single-select-field-container";
import type {
  RenderItemListParams,
  SingleSelectFieldContainerProps
} from "./single-select-field-container";

type RenderItemList<TItem> =
  SingleSelectFieldContainerProps<TItem>["renderItemList"];

type ExposedSingleItemListProps<TItem> = Omit<
  SingleItemListProps<TItem>,
  keyof RenderItemListParams<TItem>["itemListProps"] | "footer"
>;

type ExposedSelectFieldContainerProps<TItem> = Omit<
  SingleSelectFieldContainerProps<TItem>,
  "onClear" | "onItemSelect" | "renderItemList" | "valueLabel"
>;

// prettier-ignore
type Props<TItem> =
  & ExposedSelectFieldContainerProps<TItem>
  & ExposedSingleItemListProps<TItem>;

const SingleSelectField = <TItem,>({
  getItemLabel,
  isRequired = false,
  onChange,
  value,
  ...selectFieldContainerProps
}: Props<TItem>) => {
  const renderSingleItemList: RenderItemList<TItem> = ({
    itemListProps,
    toggleItemList
  }) => (
    <SingleItemList
      {...{getItemLabel, isRequired, value}}
      {...itemListProps}
      onChange={value => {
        toggleItemList();

        onChange(value);
      }}
    />
  );

  const handleItemSelect = (item: TItem) => {
    if (SingleItemList.canChangeValue(value, item, isRequired)) {
      onChange(SingleItemList.changeValue(value, item));
    }
  };

  const handleClear = () => onChange(null);

  return (
    <SingleSelectFieldContainer
      {...{getItemLabel, isRequired}}
      {...selectFieldContainerProps}
      onClear={handleClear}
      onItemSelect={handleItemSelect}
      renderItemList={renderSingleItemList}
      valueLabel={value ? getItemLabel(value) : undefined}
    />
  );
};

export default SingleSelectField;

export type {Props as SingleSelectFieldProps};
