import {forwardRef} from "react";
import type {Ref} from "react";
import styled from "styled-components";

import {FieldBoxButton, FieldBoxContainer} from "@/field";
import type {FieldBoxContainerRefElement} from "@/field";
import {fieldBoxTextCss, fieldPlaceholderCss} from "@/field/field-styles";
import {UnstyledButton} from "@/unstyled";

import CloseIcon from "./close-icon";
import VerticalChevronIcon from "./vertical-chevron-icon";

type RefElement = FieldBoxContainerRefElement;

type TextRefElement = HTMLButtonElement;

type TextRef = Ref<TextRefElement>;

type Props = {
  hasError?: boolean;
  isDisabled?: boolean;
  isItemListOpened?: boolean;
  isRequired?: boolean;
  onClear?: () => unknown;
  onItemListToggle: () => unknown;
  placeholder?: string;
  textRef?: TextRef;
  toggleButtonTestId?: string;
  valueLabel?: string;
};

type TextContainerStyledProps = {
  $hasValueLabel?: boolean;
};

const renderToggleItemListButton = ({
  isDisabled,
  isItemListOpened,
  onItemListToggle,
  toggleButtonTestId
}: Props) => (
  <FieldBoxButton
    data-testid={toggleButtonTestId}
    disabled={isDisabled}
    onClick={onItemListToggle}
    tabIndex={-1}
    type="button"
  >
    <VerticalChevronIcon direction={isItemListOpened ? "up" : "down"} />
  </FieldBoxButton>
);

const renderClearButton = ({isDisabled, onClear}: Props) => (
  <FieldBoxButton
    disabled={isDisabled}
    onClick={onClear}
    tabIndex={-1}
    type="button"
  >
    <CloseIcon />
  </FieldBoxButton>
);

const hasValueLabel = (props: Props) => props.valueLabel !== undefined;

const renderClearButtonIfNeeded = (props: Props) =>
  !props.isRequired && hasValueLabel(props) && renderClearButton(props);

const TextContainer = styled(UnstyledButton)<TextContainerStyledProps>`
  ${props => (props.$hasValueLabel ? fieldBoxTextCss : fieldPlaceholderCss)}

  flex: 1;
  height: 100%;
  text-align: left;
`;

const renderText = (props: Props) => (
  <TextContainer
    $hasValueLabel={hasValueLabel(props)}
    disabled={props.isDisabled}
    onClick={props.onItemListToggle}
    ref={props.textRef}
    type="button"
  >
    {props.valueLabel ?? props.placeholder}
  </TextContainer>
);

const Container = styled(FieldBoxContainer)`
  align-items: center;
  justify-content: space-between;
  height: 48px;
`;

const SelectBox = forwardRef<RefElement, Props>((props, ref) => (
  <Container
    {...{ref}}
    $hasError={props.hasError}
    $isDisabled={props.isDisabled}
    $withoutPaddingRight
  >
    {renderText(props)}
    {renderClearButtonIfNeeded(props)}
    {renderToggleItemListButton(props)}
  </Container>
));

export default SelectBox;
