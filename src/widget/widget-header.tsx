import type {ReactElement} from "react";
import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledHeading} from "@/unstyled";

import {widgetClassNames} from "./class-names";

type Props = {
  description?: string;
  right?: ReactElement;
  title: ReactElement | string;
};

const Description = styled.span`
  ${getTypography("body", "xs")}

  color: ${getColorByAlias("textSecondary")};
  margin-top: 4px;
`;

const Title = styled(UnstyledHeading)`
  ${getTypography("title", "s", 400)}

  color: ${getColorByAlias("textPrimary")};
`;

const Container = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const WidgetHeader = ({description, right, title}: Props) => (
  <Container>
    <div>
      <Title className={widgetClassNames.headerTitle} level={2}>
        {title}
      </Title>
      {description && (
        <Description className={widgetClassNames.headerDescription}>
          {description}
        </Description>
      )}
    </div>
    {right}
  </Container>
);

export {WidgetHeader};
