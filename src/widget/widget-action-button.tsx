import {ArrowUpRight} from "@phosphor-icons/react";
import {useState} from "react";
import styled, {css} from "styled-components";

import Button from "@/button";
import {IconButton} from "@/icons/icon-button";
import {IconWithColor} from "@/icons/icon-with-color";
import {getColor} from "@/theme-provider/theme";

import {WidgetContainer} from "./widget-container";

type Props = {
  className?: string;
  label?: string;
  onClick: () => unknown;
};

type IconButtonProps = Omit<Props, "label">;

const IconButtonWithProps = (props: IconButtonProps) => (
  <IconButton
    {...props}
    renderIcon={params => (
      <IconWithColor {...params} icon={ArrowUpRight} weight="regular" />
    )}
    size={38}
    variant="primary"
  />
);

const widgetActionButtonCss = css`
  opacity: 0;
  translate: 0 -8px;
  transition:
    opacity 100ms ease-in-out,
    translate 100ms ease-in-out;

  ${WidgetContainer}:hover & {
    opacity: 1;
    translate: 0 0;
  }
`;

const StyledIconButtonWithProps = styled(IconButtonWithProps)`
  ${widgetActionButtonCss}
`;

const StyledButton = styled(Button)`
  ${widgetActionButtonCss}
`;

const WidgetActionButton = ({className, label, onClick}: Props) => {
  const [isHover, setIsHover] = useState(false);

  if (!label) {
    return <StyledIconButtonWithProps {...{className, onClick}} />;
  }

  return (
    <StyledButton
      endIcon={
        <IconWithColor
          getColor={isHover ? getColor("blue", "900") : getColor("blue", "700")}
          icon={ArrowUpRight}
          size={28}
          weight="regular"
        />
      }
      onClick={onClick}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      size="large"
      variant="transparent"
    >
      {label}
    </StyledButton>
  );
};

export {WidgetActionButton};
