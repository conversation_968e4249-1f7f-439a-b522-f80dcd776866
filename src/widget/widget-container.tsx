import colorAlpha from "color-alpha";
import styled, {css} from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

type StyledProps = {
  $noHoverEffect?: boolean;
};

const hoverEffectCss = css`
  &:hover {
    border-color: ${getColor("gray", "250")};
    box-shadow: 0 0 20px
      ${props => colorAlpha(getColor("blue", "900")(props), 0.1)};
  }
`;

const WidgetContainer = styled.div<StyledProps>`
  background: ${getColorByAlias("backgroundWhite")};
  border: 1px solid ${getColor("gray", "070")};
  border-radius: 16px;
  transition: all 0.2s ease-in-out;

  ${props => !props.$noHoverEffect && hoverEffectCss}
`;

export {WidgetContainer};
