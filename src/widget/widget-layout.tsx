import type {ComponentProps, ReactNode} from "react";
import styled from "styled-components";

import {WidgetContainer} from "./widget-container";
import {WidgetHeader} from "./widget-header";

type WidgetHeaderProps = ComponentProps<typeof WidgetHeader>;

type ExposedWidgetHeaderProps = Omit<WidgetHeaderProps, "right">;

type Props = ExposedWidgetHeaderProps & {
  children: ReactNode;
  className?: string;
  headerRight?: WidgetHeaderProps["right"];
  noHoverEffect?: boolean;
};

const Container = styled(WidgetContainer)`
  display: flex;
  flex-direction: column;
  padding: 24px;
`;

const WidgetLayout = ({
  children,
  className,
  headerRight,
  noHoverEffect,
  ...rest
}: Props) => (
  <Container {...{className}} $noHoverEffect={noHoverEffect}>
    <WidgetHeader {...rest} right={headerRight} />
    {children}
  </Container>
);

export {WidgetLayout};
