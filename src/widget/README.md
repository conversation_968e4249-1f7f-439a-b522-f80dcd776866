# Widget

Here you will find a set of components to help you creating a widget component or render widgets on a page.

## Components

### WidgetActionButton

#### About

#### Props

```ts
type Props = {
  className?: string;
  onClick: () => unknown;
};
```

### WidgetsColumn

#### About

This component is just a simple div that can be used when multiple widgets need to be rendered horizontally.

### WidgetContainer

#### About

This component can be used for widgets that just need a container matching the widget styles.

### WidgetsGrid

#### About

This component is just a simple div that can be used when multiple widgets need to be rendered in a grid.

### WidgetLayout

#### About

This component can be used for widgets that have a header with a title, a description and optionally something at the right, and with the content below (children).

#### Props

```ts
import {ReactElement, Node} from "react";

type Props = {
  children: ReactNode;
  className?: string;
  description?: string;
  headerRight?: ReactElement;
  noHoverEffect?: boolean;
  title: ReactElement | string;
}
```

### WidgetsRow

#### About

This component is just a simple div that can be used when multiple widgets need to be rendered vertically.

### WidgetSpinner

#### About

This component renders a `CenteredSpinner` with a proper size to match the rest of the widget style.

> To avoid having different heights when the widget is loading and showing the spinner and when it finishes and renders its content, a fixed height could be set to the WidgetLayout.

#### Props

```ts
type Props = {
  className?: string;
};
```
