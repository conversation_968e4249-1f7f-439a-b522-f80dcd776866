import {useMemo} from "react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {UnstyledUl} from "@/unstyled";

import * as withMultiCellEntry from "./item-entry";
import type {ItemEntry} from "./item-entry";
import {ItemEntryRow} from "./item-entry-row";

type MultiCellRowProps = ComponentProps<typeof ItemEntryRow>;

type ExposedMultiCellRowProps = Pick<
  MultiCellRowProps,
  | "getNestedIconColor"
  | "horizontalAlign"
  | "isFirstColumn"
  | "renderStringItemEntry"
>;

type Props = ExposedMultiCellRowProps & {
  isCollapsed?: boolean;
  itemEntries: ItemEntry[];
};

const valueRowsGap = 4;

const Container = styled(UnstyledUl)`
  display: flex;
  flex-direction: column;
  gap: ${valueRowsGap}px;
`;

const ItemEntryList = ({isCollapsed, itemEntries, ...rest}: Props) => {
  const nonEmptyItemEntries = useMemo(
    () => itemEntries.filter(withMultiCellEntry.isNonEmpty),
    [itemEntries]
  );

  return (
    <Container>
      {isCollapsed && nonEmptyItemEntries.length !== 0 ? (
        <ItemEntryRow
          {...rest}
          index={0}
          itemEntry={nonEmptyItemEntries[0]}
          key={0}
        />
      ) : (
        nonEmptyItemEntries.map((nonEmptyItemEntry, index) => (
          <ItemEntryRow
            {...{index}}
            {...rest}
            itemEntry={nonEmptyItemEntry}
            key={index}
          />
        ))
      )}
    </Container>
  );
};

export {ItemEntryList};
