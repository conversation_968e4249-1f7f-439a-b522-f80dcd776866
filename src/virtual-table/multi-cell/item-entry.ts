import type {ReactElement} from "react";

type EmptyItemEntry = false | null | undefined;

type NonEmptyItemEntry = ReactElement | string;

type ItemEntry = EmptyItemEntry | NonEmptyItemEntry;

const emptyItemEntries: ItemEntry[] = [false, null, undefined];

const isNonEmpty = (itemEntry: ItemEntry): itemEntry is NonEmptyItemEntry =>
  !emptyItemEntries.includes(itemEntry);

export {isNonEmpty};
export type {ItemEntry, NonEmptyItemEntry};
