import {ArrowElbowDownRight} from "@phosphor-icons/react";
import type {ReactElement} from "react";
import styled, {css} from "styled-components";

import {IconWithColor} from "@/icons";
import type {GetColor} from "@/theme-provider/theme";

import type {CellHorizontalAlignType} from "../cell-horizontal-align-type";

import type {NonEmptyItemEntry} from "./item-entry";

type Props = {
  getNestedIconColor: GetColor;
  horizontalAlign?: CellHorizontalAlignType;
  index: number;
  isFirstColumn?: boolean;
  itemEntry: NonEmptyItemEntry;
  renderStringItemEntry: (stringItemEntry: string) => ReactElement;
};

// TODO: Use RequiredOnly type from @unlockre/utils-object
type PropsWithDefaults = {
  getNestedIconColor: GetColor;
  horizontalAlign: CellHorizontalAlignType;
  index: number;
  isFirstColumn?: boolean;
  itemEntry: NonEmptyItemEntry;
  renderStringItemEntry: (stringItemEntry: string) => ReactElement;
};

type ContainerStyledProps = {
  $horizontalAlign: CellHorizontalAlignType;
  $index: number;
  $isFirstColumn?: boolean;
};

const nestedContainerCss = css`
  padding-left: 18px;
`;

const Container = styled.li<ContainerStyledProps>`
  ${props => props.$isFirstColumn && props.$index !== 0 && nestedContainerCss}

  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: ${props => props.$horizontalAlign};
`;

const ensureProps = ({horizontalAlign, isFirstColumn}: PropsWithDefaults) => {
  if (isFirstColumn && horizontalAlign !== "start") {
    throw new Error(
      'Expected horizontalAlign to be "start" when isFirstColumn is true'
    );
  }
};

const ItemEntryRow = ({
  getNestedIconColor,
  horizontalAlign = "start",
  index,
  isFirstColumn,
  itemEntry,
  renderStringItemEntry
}: Props) => {
  ensureProps({
    itemEntry,
    horizontalAlign,
    getNestedIconColor,
    index,
    isFirstColumn,
    renderStringItemEntry
  });

  return (
    <Container
      $horizontalAlign={horizontalAlign}
      $index={index}
      $isFirstColumn={isFirstColumn}
    >
      {isFirstColumn && index !== 0 && (
        <IconWithColor
          getColor={getNestedIconColor}
          icon={ArrowElbowDownRight}
          size={16}
          weight="regular"
        />
      )}
      {typeof itemEntry === "string"
        ? renderStringItemEntry(itemEntry)
        : itemEntry}
    </Container>
  );
};

export {ItemEntryRow};
