import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps} from "react";

import {ItemsSummaryCell} from "../items-summary-cell";
import type {ItemsSummaryCellProps} from "../items-summary-cell";
import {
  ItemsSummaryText,
  getItemsSummaryTextColor
} from "../items-summary-text";

import {ItemEntryList} from "./item-entry-list";

type MultiCellProps = ComponentProps<typeof ItemEntryList>;

type ExposedMultiCellProps = Omit<
  MultiCellProps,
  "getNestedIconColor" | "horizontalAlign" | "renderStringItemEntry"
>;

type ExposedItemsSummaryCellProps<TItem extends AnyObject> = Omit<
  ItemsSummaryCellProps<TItem>,
  "children" | "className"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedItemsSummaryCellProps<TItem>
  & ExposedMultiCellProps;

const ItemsSummaryMultiCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <ItemsSummaryCell {...{tableColumn, variant}}>
    <ItemEntryList
      {...rest}
      getNestedIconColor={getItemsSummaryTextColor}
      horizontalAlign={tableColumn.horizontalAlign}
      renderStringItemEntry={stringItemEntry => (
        <ItemsSummaryText>{stringItemEntry}</ItemsSummaryText>
      )}
    />
  </ItemsSummaryCell>
);

export {ItemsSummaryMultiCell};
export type {Props as ItemsSummaryMultiCellProps};
