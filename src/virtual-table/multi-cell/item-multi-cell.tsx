import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps} from "react";

import {ItemCell} from "../item-cell";
import type {ItemCellProps} from "../item-cell";
import {ItemText, getItemTextColor} from "../item-text";

import {ItemEntryList} from "./item-entry-list";

type MultiCellProps = ComponentProps<typeof ItemEntryList>;

type ExposedMultiCellProps = Omit<
  MultiCellProps,
  "getNestedIconColor" | "horizontalAlign" | "renderStringItemEntry"
>;

type ExposedItemCellProps<TItem extends AnyObject> = Omit<
  ItemCellProps<TItem>,
  "children" | "className"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedItemCellProps<TItem>
  & ExposedMultiCellProps;

const ItemMultiCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <ItemCell {...{tableColumn, variant}}>
    <ItemEntryList
      {...rest}
      getNestedIconColor={getItemTextColor}
      horizontalAlign={tableColumn.horizontalAlign}
      renderStringItemEntry={stringItemEntry => (
        <ItemText>{stringItemEntry}</ItemText>
      )}
    />
  </ItemCell>
);

export {ItemMultiCell};
export type {Props as ItemMultiCellProps};
