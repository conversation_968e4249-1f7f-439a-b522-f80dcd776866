import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps} from "react";

import {GroupSummaryCell} from "../group-summary-cell";
import type {GroupSummaryCellProps} from "../group-summary-cell";
import {
  GroupSummaryText,
  getGroupSummaryTextColor
} from "../group-summary-text";

import {ItemEntryList} from "./item-entry-list";

type MultiCellProps = ComponentProps<typeof ItemEntryList>;

type ExposedMultiCellProps = Omit<
  MultiCellProps,
  "getNestedIconColor" | "horizontalAlign" | "renderStringItemEntry"
>;

type ExposedGroupSummaryCellProps<TItem extends AnyObject> = Omit<
  GroupSummaryCellProps<TItem>,
  "children" | "className"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedGroupSummaryCellProps<TItem>
  & ExposedMultiCellProps;

const GroupSummaryMultiCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <GroupSummaryCell {...{tableColumn, variant}}>
    <ItemEntryList
      {...rest}
      getNestedIconColor={getGroupSummaryTextColor}
      horizontalAlign={tableColumn.horizontalAlign}
      renderStringItemEntry={stringItemEntry => (
        <GroupSummaryText>{stringItemEntry}</GroupSummaryText>
      )}
    />
  </GroupSummaryCell>
);

export {GroupSummaryMultiCell};
export type {Props as GroupSummaryMultiCellProps};
