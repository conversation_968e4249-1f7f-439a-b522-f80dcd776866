import mergeRefs from "merge-refs";
import {forwardRef, useMemo, useState} from "react";
import type {ForwardedRef, ReactNode} from "react";
import styled from "styled-components";

import {useFixedElement} from "./use-fixed-element";

type TableCellElement = HTMLDivElement;

type GetFixedElement = (
  tableCellElement: TableCellElement | null
) => HTMLElement | null;

type Props = {
  children?: ReactNode;
  className?: string;
  getFixedElement: GetFixedElement;
  isFixed?: boolean;
};

const Container = styled.div`
  align-items: center;
  display: flex;
`;

const TableCell = forwardRef(
  (
    {getFixedElement, isFixed, ...rest}: Props,
    forwardedRef: ForwardedRef<TableCellElement>
  ) => {
    const [container, setContainer] = useState<TableCellElement | null>(null);

    const fixedElement = useMemo(
      () => (isFixed ? getFixedElement(container) : null),
      [container, getFixedElement, isFixed]
    );

    useFixedElement(fixedElement);

    return (
      <Container
        {...rest}
        ref={mergeRefs(forwardedRef, el => setContainer(el))}
      />
    );
  }
);

export {TableCell};
