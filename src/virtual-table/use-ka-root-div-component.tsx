import type {ITableProps} from "ka-table";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import {useTheme} from "styled-components";

type Params = {
  height?: number | string;
  withBorder?: boolean;
};

const useKaRootDivComponent = ({
  height,
  withBorder
}: Params): ChildComponent<ITableProps> => {
  const theme = useTheme();

  const maxHeightStyle = height === undefined ? undefined : {maxHeight: height};

  const borderStyle = !withBorder
    ? undefined
    : {
        border: `1px solid ${theme.colors.palette.gray["070"]}`,
        borderRadius: "16px"
      };

  return {
    elementAttributes: () => ({
      style: {
        ...borderStyle,
        ...maxHeightStyle,
        overflow: "hidden"
      }
    })
  };
};

export {useKaRootDivComponent};
