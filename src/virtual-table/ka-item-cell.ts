import type {AnyObject} from "@unlockre/utils-object/dist";
import type {CSSProperties} from "react";

import type {GetColor, Theme} from "@/theme-provider/theme";

import * as withKaBodyCell from "./ka-body-cell";
import type {KaBodyCellGetStyleParams} from "./ka-body-cell";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";

type GetStyleParams<TItem extends AnyObject> =
  KaBodyCellGetStyleParams<TItem> & {
    highlightedBorderColor?: GetColor;
    isFirstRow?: boolean;
    isLastRow?: boolean;
    tableColumn: TableColumn<TItem>;
    tableColumns: TableColumn<TItem>[];
    theme: Theme;
    withoutRowBorders?: boolean;
  };

type GetHighlightedBorderStyleParams<TItem extends AnyObject> = Omit<
  GetStyleParams<TItem>,
  "highlightedBorderColor"
> & {
  highlightedBorderColor: GetColor;
};

const getNonHighlightedBorderStyle = <TItem extends AnyObject>({
  theme,
  withoutRowBorders
}: GetStyleParams<TItem>): CSSProperties => ({
  borderBottom: withoutRowBorders
    ? "unset"
    : `1px solid ${theme.colors.palette.gray[100]}`
});

const getHighlightedBorderCss = (
  width: number,
  highlightedBorderColor: GetColor,
  theme: Theme
) => `${width}px solid ${highlightedBorderColor({theme})}`;

const getHighlightedBorderStyle = <TItem extends AnyObject>({
  highlightedBorderColor,
  isFirstRow,
  tableColumn,
  tableColumns,
  theme
}: GetHighlightedBorderStyleParams<TItem>): CSSProperties => ({
  borderTop: getHighlightedBorderCss(
    isFirstRow ? 3 : 2,
    highlightedBorderColor,
    theme
  ),
  borderBottom: getHighlightedBorderCss(2, highlightedBorderColor, theme),
  ...(!withTableColumn.isFirst(tableColumns, tableColumn)
    ? undefined
    : {
        borderLeft: getHighlightedBorderCss(2, highlightedBorderColor, theme)
      }),
  ...(!withTableColumn.isLast(tableColumns, tableColumn)
    ? undefined
    : {
        borderRight: getHighlightedBorderCss(2, highlightedBorderColor, theme)
      })
});

const getBorderStyle = <TItem extends AnyObject>(
  params: GetStyleParams<TItem>
): CSSProperties => {
  const {highlightedBorderColor} = params;

  return highlightedBorderColor
    ? getHighlightedBorderStyle({...params, highlightedBorderColor})
    : getNonHighlightedBorderStyle(params);
};

const getLastRowStyle = <TItem extends AnyObject>({
  isLastRow,
  tableColumn,
  tableColumns
}: GetStyleParams<TItem>): CSSProperties =>
  isLastRow
    ? {
        borderBottomLeftRadius: withTableColumn.isFirst(
          tableColumns,
          tableColumn
        )
          ? "16px"
          : "0",
        borderBottomRightRadius: withTableColumn.isLast(
          tableColumns,
          tableColumn
        )
          ? "16px"
          : "0"
      }
    : {};

const getStyle = <TItem extends AnyObject>(params: GetStyleParams<TItem>) => ({
  ...withKaBodyCell.getStyle(params),
  ...getBorderStyle(params),
  ...getLastRowStyle(params)
});

export {getStyle};
