import type {AnyObject} from "@unlockre/utils-object/dist";
import {DataType} from "ka-table";
import type {ReactNode} from "react";

import type {GetColor} from "@/theme-provider/theme";

import type {CellHorizontalAlignType} from "./cell-horizontal-align-type";
import type {CellVerticalAlignType} from "./cell-vertical-align-type";
import type {KaTableColumn} from "./ka-table-column";
import {specialTableColumnKeys} from "./special-table-column-key";
import * as withTableSortDirection from "./table-sort-direction";
import type {TableSortDirection} from "./table-sort-direction";
import type {VirtualTableVariant} from "./virtual-table-variant";

type ItemsSorter<TItem extends AnyObject> = (
  firstItem: TItem,
  secondItem: TItem,
  sortDirection?: TableSortDirection
) => -1 | 0 | 1;

type RenderItemsSummaryCellParams<TItem extends AnyObject> = {
  items: TItem[];
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

type RenderItemsSummaryCell<TItem extends AnyObject> = (
  params: RenderItemsSummaryCellParams<TItem>
) => ReactNode;

type RenderGroupSummaryCellParams<TItem extends AnyObject> = {
  groupItems: TItem[];
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

type RenderGroupSummaryCell<TItem extends AnyObject> = (
  params: RenderGroupSummaryCellParams<TItem>
) => ReactNode;

type RenderTableHeaderCell<TItem extends AnyObject> = (
  tableColumn: TableColumn<TItem>
) => ReactNode;

type RenderTableCell<TItem extends AnyObject> = (
  item: TItem,
  tableColumn: TableColumn<TItem>
) => ReactNode;

type RenderHeaderCellParams<TItem extends AnyObject> = {
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

type RenderHeaderCell<TItem extends AnyObject> = (
  params: RenderHeaderCellParams<TItem>
) => ReactNode;

type RenderItemCellParams<TItem extends AnyObject> = {
  item: TItem;
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

type RenderItemCell<TItem extends AnyObject> = (
  params: RenderItemCellParams<TItem>
) => ReactNode;

type SummaryGetter<TItem extends AnyObject> = (
  itemsToSummary: TItem[],
  items: TItem[]
) => number | string | undefined;

type TableColumn<TItem extends AnyObject> = Omit<
  KaTableColumn<TItem>,
  | "colGroup"
  | "dataType"
  | "filterRowOperator"
  | "filterRowValue"
  | "headerFilterPopupPosition"
  | "headerFilterValues"
  | "isEditable"
  | "isFilterable"
  | "isHeaderFilterPopupShown"
  | "isResizable"
  | "sortDirection"
  | "sortIndex"
  | "style"
  | "visible"
> & {
  description?: string;
  format?: (item: TItem) => string;
  getBackgroundColor?: GetColor;
  getSummary?: SummaryGetter<TItem>;
  groupKey?: string;
  horizontalAlign?: CellHorizontalAlignType;
  initialSortDirection?: TableSortDirection;
  isFixed?: boolean;
  renderGroupSummaryCell?: RenderGroupSummaryCell<TItem>;
  renderHeaderCell?: RenderHeaderCell<TItem>;
  renderItemCell?: RenderItemCell<TItem>;
  renderItemsSummaryCell?: RenderItemsSummaryCell<TItem>;
  /**
   * @deprecated Use renderItemCell instead
   */
  renderTableCell?: RenderTableCell<TItem>;
  /**
   * @deprecated Use renderHeaderCell instead
   */
  renderTableHeaderCell?: RenderTableHeaderCell<TItem>;
  sortFunction?: ItemsSorter<TItem>;
  verticalAlign?: CellVerticalAlignType;
};

const getKeyFrom = (kaTableColumn: KaTableColumn) =>
  kaTableColumn.key.split(".").slice(1).join(".");

const allSpecialTableColumnKeys = Object.values(specialTableColumnKeys);

const isSpecial = <TItem extends AnyObject>(tableColumn: TableColumn<TItem>) =>
  (allSpecialTableColumnKeys as string[]).includes(tableColumn.key);

const isLastFixed = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[],
  tableColumnKey: string
) => {
  const tableColumn = ensure(tableColumns, tableColumnKey);

  const tableColumnIndex = tableColumns.findIndex(
    column => tableColumn.key === column.key
  );

  return (
    tableColumn.isFixed &&
    tableColumnIndex < tableColumns.length - 1 &&
    !tableColumns[tableColumnIndex + 1]?.isFixed
  );
};

// TODO: Remove this one once we have added hasIndex in @unlockre/utils-array
const isFirst = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[],
  tableColumn: TableColumn<TItem>
) => tableColumns.indexOf(tableColumn) === 0;

// TODO: Remove this one once we have added hasIndex in @unlockre/utils-array
const isLast = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[],
  tableColumn: TableColumn<TItem>
) => tableColumns.indexOf(tableColumn) === tableColumns.length - 1;

const getKaTableColumnKey = (tableColumnKey: string) =>
  "item." + tableColumnKey;

const toKaTableColumn = <TItem extends AnyObject>({
  initialSortDirection,
  isSortable,
  key,
  title,
  width
}: TableColumn<TItem>): KaTableColumn<TItem> => ({
  dataType: DataType.Object,
  key: getKaTableColumnKey(key),
  isSortable: Boolean(isSortable),
  sortDirection: withTableSortDirection.toKaSortDirection(initialSortDirection),
  title,
  width
});

const find = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[],
  tableColumnKey: string
) => tableColumns.find(curTableColumn => curTableColumn.key === tableColumnKey);

const ensure = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[],
  tableColumnKey: string
) => {
  const tableColumn = find(tableColumns, tableColumnKey);

  // We need this to complain TS.
  if (!tableColumn) {
    throw new Error("No column");
  }

  return tableColumn;
};

const findSortedTableColumn = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[],
  kaTableColumns: KaTableColumn<TItem>[]
) => {
  const sortedKaTableColumn = kaTableColumns.find(
    kaTableColumn => kaTableColumn.sortDirection
  );

  if (!sortedKaTableColumn) {
    return;
  }

  const tableColumn = ensure(tableColumns, getKeyFrom(sortedKaTableColumn));
  const tableSortDirection = withTableSortDirection.fromKaSortDirection(
    sortedKaTableColumn.sortDirection
  );

  return {tableColumn, tableSortDirection};
};

const hasAnySummaryRow = <TItem extends AnyObject>(
  tableColumn: TableColumn<TItem>
) => tableColumn.getSummary !== undefined;

const hasItemsSummaryRow = <TItem extends AnyObject>(
  tableColumn: TableColumn<TItem>
) =>
  hasAnySummaryRow(tableColumn) ||
  tableColumn.renderItemsSummaryCell !== undefined;

const hasGroupSummaryRow = <TItem extends AnyObject>(
  tableColumn: TableColumn<TItem>
) =>
  tableColumn.getSummary !== undefined ||
  tableColumn.renderGroupSummaryCell !== undefined;

const areFixedConsecutive = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[]
) =>
  tableColumns.every(
    (column, index) =>
      !column.isFixed || index === 0 || tableColumns[index - 1]?.isFixed
  );

const ensureFixedConsecutive = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[]
) => {
  if (!areFixedConsecutive(tableColumns)) {
    throw new Error("Fixed columns must be consecutive");
  }

  return tableColumns;
};

const ensureOneInitialSortDirection = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[]
) => {
  const tableColumnsWithInitialSortDirection = tableColumns.filter(
    tableColumn => tableColumn.initialSortDirection
  );

  if (tableColumnsWithInitialSortDirection.length > 1) {
    throw new Error("More than one column with initialSortDirection");
  }

  return tableColumns;
};

export {
  ensureFixedConsecutive,
  ensureOneInitialSortDirection,
  ensure,
  find,
  findSortedTableColumn,
  getKaTableColumnKey,
  getKeyFrom,
  hasGroupSummaryRow,
  hasItemsSummaryRow,
  isFirst,
  isLast,
  isLastFixed,
  isSpecial,
  toKaTableColumn
};
export type {TableColumn};
