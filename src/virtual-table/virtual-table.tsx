import type {AnyObject} from "@unlockre/utils-object/dist";
import colorAlpha from "color-alpha";
import {Table as KaTable} from "ka-table";
import styled, {css} from "styled-components";

import {getColor} from "@/theme-provider/theme";

import {kaDataRowCss} from "./ka-data-row";
import {useVirtualTable} from "./use-virtual-table";
import type {UseVirtualTableParams} from "./use-virtual-table";
import {virtualTableClassNames} from "./virtual-table-class-names";
import type {VirtualTableVariant} from "./virtual-table-variant";

type ContainerStyledProps = {
  $areRowsClickable?: boolean;
  $isGroupingEnabled?: boolean;
  $isPagingEnabled?: boolean;
};

type ExposedUseVirtualTableParams<TItem extends AnyObject> = Omit<
  UseVirtualTableParams<TItem>,
  "variant"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedUseVirtualTableParams<TItem>
  & {
      className?: string;
      variant?: VirtualTableVariant;
    };

const fixedCellCss = css`
  .${virtualTableClassNames.fixedCell} {
    ::after {
      box-shadow: inset 8px 0px 4px -4px ${props => colorAlpha(getColor("blue", "900")(props), 0.07)};
      content: "";
      height: 100%;
      position: absolute;
      right: -10px;
      top: 0;
      width: 10px;
    }
  }
`;

const pagingEnabledCss = css`
  padding-bottom: 36px;
`;

// This css is used to hide the first empty column added by ka-table when
// grouping is enabled
const groupingEnabledCss = css`
  *:is(colgroup, .ka-thead-row, .ka-row, .ka-summary-row, .ka-group-summary-row)
    > :first-child {
    display: none;
  }
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => props.$isGroupingEnabled && groupingEnabledCss}

  ${props => props.$isPagingEnabled && pagingEnabledCss}

  position: relative;

  ${fixedCellCss}

  ${kaDataRowCss}
`;

const VirtualTable = <TItem extends AnyObject>({
  className,
  groupBy,
  isRowClickable,
  items,
  pagingOptions,
  variant = "dense",
  ...rest
}: Props<TItem>) => {
  const {kaTableProps} = useVirtualTable({
    groupBy,
    isRowClickable,
    items,
    pagingOptions,
    variant,
    ...rest
  });

  return (
    <Container
      {...{className}}
      $areRowsClickable={Boolean(isRowClickable)}
      $isGroupingEnabled={groupBy !== undefined}
      $isPagingEnabled={pagingOptions?.isEnabled}
    >
      <KaTable {...kaTableProps} />
    </Container>
  );
};

export {VirtualTable};
export type {Props as VirtualTableProps};
