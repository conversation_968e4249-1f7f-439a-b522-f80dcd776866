import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {ISummaryRowProps} from "ka-table/props";
import {useTheme} from "styled-components";

import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";

type Params<TItem extends AnyObject> = {
  tableColumns: TableColumn<TItem>[];
  withoutRowBorders?: boolean;
};

const useKaSummaryRowComponent = <TItem extends AnyObject>({
  tableColumns,
  withoutRowBorders
}: Params<TItem>): ChildComponent<ISummaryRowProps> => {
  const hideSummary = !tableColumns.some(withTableColumn.hasItemsSummaryRow);

  const theme = useTheme();

  return {
    elementAttributes: props => ({
      style: {
        backgroundColor: theme.colors.aliases.backgroundWhite,
        borderTop: withoutRowBorders
          ? undefined
          : "1px solid " + theme.colors.palette.gray[100],
        // We need to use ka-table's loading prop instead of VirtualTable's prop
        // because ka-table's prop is updated later, so using the other prop
        // would cause the summary row to appear while ka-table is still in
        // loading state
        display: props.loading?.enabled || hideSummary ? "none" : undefined
      }
    })
  };
};

export {useKaSummaryRowComponent};
