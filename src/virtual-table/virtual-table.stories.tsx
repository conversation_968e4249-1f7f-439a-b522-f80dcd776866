import {CurrencyDollar} from "@phosphor-icons/react";
import type {Meta, StoryFn} from "@storybook/react";
import {formatCurrency} from "@unlockre/utils-formatting/dist";
import {getRange} from "@unlockre/utils-number/dist";
import {useAsync} from "@unlockre/utils-react/dist";
import {useCallback, useEffect, useMemo, useState} from "react";
import styled from "styled-components";

import {HeaderCell} from "./header-cell";
import {HeaderText} from "./header-text";
import {highlightSelectedRow} from "./highlight-selected-row";
import {isEvenRowZebraStriped} from "./is-even-row-zebra-striped";
import {isOddRowZebraStriped} from "./is-odd-row-zebra-striped";
import type {GetItemId} from "./item";
import {ItemCell} from "./item-cell";
import {ItemText} from "./item-text";
import {ItemMultiCell} from "./multi-cell";
import type {PagingOptions} from "./paging-options";
import type {TableColumn} from "./table-column";
import {timeout} from "./timeout";
import {useMultipleRowSelectionTableProps} from "./use-multiple-row-selection-table-props";
import {VirtualTable} from "./virtual-table";
import type {VirtualTableProps} from "./virtual-table";
import {virtualTableVariants} from "./virtual-table-variant";
import type {VirtualTableVariant} from "./virtual-table-variant";
import {zebraStripingTypes} from "./zebra-striping-type";
import type {ZebraStripingType} from "./zebra-striping-type";

type Movie = {
  id: number;
  revenue: number;
  title: string;
  year: string;
};

type MovieTableColumn = TableColumn<Movie>;

type ExposedPagingOptions = Omit<PagingOptions, "onPageChange">;

type ExposedVirtualTableProps = Pick<
  VirtualTableProps<Movie>,
  | "areRowsExpansible"
  | "groupBy"
  | "height"
  | "isRowHighlighted"
  | "selectedItems"
  | "withNoHeader"
  | "withoutRowBorders"
>;

type Args = ExposedVirtualTableProps & {
  areRowsClickable: boolean;
  areRowsDraggable: boolean;
  areRowsSelectable: boolean;
  hasInfiniteScroll: boolean;
  hasMultipleRowSelection: boolean;
  hasTitleAndRevenueColumn: boolean;
  pagingOptions: ExposedPagingOptions;
  variant: VirtualTableVariant;
  withPagination: boolean;
  zebraStripingType?: ZebraStripingType;
};

type RevenueHeaderCellProps = {
  tableColumn: MovieTableColumn;
  variant: VirtualTableVariant;
};

type TitleAndYearCellProps = {
  movie: Movie;
  tableColumn: MovieTableColumn;
  variant: VirtualTableVariant;
};

const createMockMovie = (id: number): Movie => ({
  id,
  revenue: Math.floor(Math.random() * 500_000_000),
  title: `Movie ${id}`,
  year: String(1980 + Math.floor(Math.random() * 40))
});

const createMockMovies = (minId: number, maxId: number): Movie[] =>
  getRange(minId, maxId).map(id => createMockMovie(id));

const getMoviesTotalRevenue = (movies: Movie[]) =>
  movies.reduce((totalRevenue, movie) => totalRevenue + movie.revenue, 0);

const RevenueHeaderCellContainer: typeof HeaderCell = styled(HeaderCell)`
  align-items: center;
  display: flex;
  gap: 4px;
`;

const RevenueHeaderCell = ({tableColumn, ...rest}: RevenueHeaderCellProps) => (
  <RevenueHeaderCellContainer {...{tableColumn}} {...rest}>
    <HeaderText>{tableColumn.title}</HeaderText>
    <CurrencyDollar size={12} />
  </RevenueHeaderCellContainer>
);

const TitleAndYearCell = ({
  movie,
  tableColumn,
  variant
}: TitleAndYearCellProps) => (
  <ItemCell {...{tableColumn, variant}}>
    <ItemText>{movie.title + ": " + movie.year}</ItemText>
  </ItemCell>
);

const titleAndRevenueColumn: TableColumn<Movie> = {
  title: "Title and revenue",
  key: "titleAndRevenue",
  groupKey: "group3",
  width: 260,
  isSortable: true,
  verticalAlign: "top",
  renderItemCell: ({item, ...rest}) => (
    <ItemMultiCell
      {...rest}
      itemEntries={[item.title, formatCurrency(item.revenue)]}
    />
  ),
  renderItemsSummaryCell: ({items, ...rest}) => (
    <ItemMultiCell
      {...rest}
      itemEntries={[
        "Total Revenue",
        formatCurrency(getMoviesTotalRevenue(items))
      ]}
    />
  )
};

const columns: TableColumn<Movie>[] = [
  {
    title: "Id",
    key: "id",
    groupKey: "group1",
    width: 150,
    isFixed: true,
    isSortable: true,
    verticalAlign: "top",
    getSummary: (itemsToSummary, items) =>
      itemsToSummary === items ? "Total" : "Group Total"
  },
  {
    title: "Title",
    key: "title",
    groupKey: "group1",
    width: 220,
    verticalAlign: "top",
    isFixed: true,
    isSortable: true
  },
  {
    title: "Year",
    key: "year",
    groupKey: "group2",
    width: 200,
    verticalAlign: "top",
    initialSortDirection: "ASC",
    isSortable: true
  },
  {
    title: "Revenue",
    key: "revenue",
    width: 140,
    isSortable: true,
    horizontalAlign: "end",
    verticalAlign: "top",
    getSummary: getMoviesTotalRevenue,
    renderHeaderCell: params => <RevenueHeaderCell {...params} />
  },
  {
    title: "Title and year",
    key: "titleAndYear",
    width: 260,
    verticalAlign: "top",
    renderItemCell: ({item, ...rest}) => (
      <TitleAndYearCell {...rest} movie={item} />
    )
  }
];

const MovieDetails = ({title, year}: Movie) => (
  <div>
    <h3>{title}</h3>
    <p>year: {year}</p>
  </div>
);

const isRowClickable = () => true;

const isRowDraggable = (item: Movie) => item.id !== 1;

const isRowHighlighted = (item: Movie) => item.id === 2;

const isRowSelectable = (item: Movie) => item.id !== 3;

const getItemId = (movie: Movie) => String(movie.id);

const getYearGroupDescription = (year: string) => "Year: " + year;

const isRowZebraStripedFrom = (zebraStripingType: ZebraStripingType) =>
  zebraStripingType === zebraStripingTypes.even
    ? isOddRowZebraStriped
    : isEvenRowZebraStriped;

const hasScrollToBottom = (targetElement: HTMLElement, bottomOffset = 0) =>
  targetElement.offsetHeight + targetElement.scrollTop >=
  targetElement.scrollHeight - bottomOffset;

const pagingOptions: PagingOptions = {
  isEnabled: true,
  onPageChange: pageNumber => console.log({pageNumber}),
  pageNumber: 1,
  pageSize: 10,
  totalRows: 95
};

// eslint-disable-next-line complexity, max-statements
const Default: StoryFn<Args> = ({
  areRowsClickable,
  areRowsDraggable,
  areRowsSelectable,
  groupBy,
  hasInfiniteScroll,
  hasMultipleRowSelection,
  hasTitleAndRevenueColumn,
  height,
  variant,
  withPagination,
  ...rest
}) => {
  const tableColumns = useMemo(
    () =>
      hasTitleAndRevenueColumn ? [...columns, titleAndRevenueColumn] : columns,
    [hasTitleAndRevenueColumn]
  );

  const [items, setItems] = useState<Movie[]>([]);

  const [selectedItems, setSelectedItems] = useState<Movie[]>([]);

  const [expandedItems, setExpandedItems] = useState<Movie[]>([]);

  const addMockMovies = useCallback(() => {
    setItems(items => [
      ...items,
      ...createMockMovies(items.length + 1, items.length + 10)
    ]);
  }, []);

  useEffect(() => {
    timeout(1200).promise.then(addMockMovies);
  }, []);

  const [loadMoreItemsResult, loadMoreItems] = useAsync(
    useCallback(() => timeout(300).promise.then(addMockMovies), [])
  );

  const handleScroll = (_event: Event, targetElement: HTMLElement) => {
    const shouldLoadMoreItems =
      hasInfiniteScroll &&
      hasScrollToBottom(targetElement, 20) &&
      loadMoreItemsResult.status !== "pending";

    if (shouldLoadMoreItems) {
      console.log("scroll to bottom");

      loadMoreItems();
    }
  };

  const {isRowSelected, ...multipleRowSelectionTableProps} =
    useMultipleRowSelectionTableProps({
      getItemId,
      isRowSelectable,
      items
    });

  const isMovieSelected = useCallback(
    (item: Movie, getItemId: GetItemId<Movie>, selectedItems?: Movie[]) =>
      highlightSelectedRow(item, selectedItems) || isRowSelected(item),
    [isRowSelected]
  );

  const onSelectedItemsChange = useCallback(
    ({selectedItems: newSelectedItems}: {selectedItems: Movie[]}) =>
      setSelectedItems([
        ...items.filter(item =>
          newSelectedItems.some(
            selectedItem => getItemId(selectedItem) === getItemId(item)
          )
        )
      ]),
    [items]
  );

  const hasMultipleRowSelectionProps = hasMultipleRowSelection
    ? {
        ...multipleRowSelectionTableProps,
        isRowHighlighted: isMovieSelected
      }
    : {};

  return (
    <VirtualTable
      {...{
        expandedItems,
        getItemId,
        groupBy,
        isRowHighlighted,
        items,
        onSelectedItemsChange,
        selectedItems,
        variant
      }}
      {...rest}
      {...hasMultipleRowSelectionProps}
      columns={tableColumns}
      getGroupDescription={
        groupBy === "year" ? getYearGroupDescription : undefined
      }
      height={height || undefined}
      isLoading={items.length === 0}
      isRowClickable={areRowsClickable ? isRowClickable : undefined}
      isRowDraggable={areRowsDraggable ? isRowDraggable : undefined}
      isRowSelectable={areRowsSelectable ? isRowSelectable : undefined}
      isRowZebraStriped={
        rest.zebraStripingType && isRowZebraStripedFrom(rest.zebraStripingType)
      }
      onExpandedItemsChange={setExpandedItems}
      onItemsReorder={setItems}
      onScroll={handleScroll}
      pagingOptions={withPagination ? pagingOptions : undefined}
      renderExpandedItem={MovieDetails}
      withBorder
    />
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?type=design&node-id=1907-71197&mode=design&t=8ydZ9jJaTRvOZwdg-0"
  }
};

const meta: Meta<Args> = {
  title: "virtual-table",
  argTypes: {
    areRowsClickable: {
      control: "boolean"
    },
    areRowsDraggable: {
      control: "boolean"
    },
    areRowsExpansible: {
      control: "boolean"
    },
    areRowsSelectable: {
      control: "boolean"
    },
    groupBy: {
      control: "select",
      options: [undefined, "id", "revenue", "title", "year"]
    },
    hasInfiniteScroll: {
      control: "boolean"
    },
    hasMultipleRowSelection: {
      control: "boolean"
    },
    hasTitleAndRevenueColumn: {
      control: "boolean"
    },
    height: {
      control: "number"
    },
    variant: {
      control: "select",
      options: Object.values(virtualTableVariants)
    },
    withNoHeader: {
      control: "boolean"
    },
    withPagination: {
      control: "boolean"
    },
    withoutRowBorders: {
      control: "boolean"
    },
    zebraStripingType: {
      control: "select",
      options: [undefined, ...Object.values(zebraStripingTypes)]
    }
  },
  args: {
    areRowsClickable: true,
    areRowsDraggable: true,
    areRowsExpansible: true,
    areRowsSelectable: false,
    groupBy: undefined,
    hasInfiniteScroll: false,
    hasMultipleRowSelection: false,
    hasTitleAndRevenueColumn: false,
    height: 500,
    variant: virtualTableVariants.dense,
    withNoHeader: false,
    withPagination: true,
    withoutRowBorders: false,
    zebraStripingType: zebraStripingTypes.even
  }
};

export {meta as default, Default};
