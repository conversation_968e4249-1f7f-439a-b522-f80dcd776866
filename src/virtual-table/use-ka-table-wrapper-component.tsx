import type {ITableProps} from "ka-table";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {Ref} from "react";

type KaTableWrapperRef = Ref<HTMLDivElement>;

type Params = {
  height?: number | string;
  isLoading?: boolean;
  ref: KaTableWrapperRef;
  scrollToBottomOffset?: number;
};

const useKaTableWrapperComponent = ({
  height,
  isLoading,
  ref
}: Params): ChildComponent<ITableProps> => {
  const maxHeightStyle = height === undefined ? undefined : {maxHeight: height};

  return {
    elementAttributes: () => ({
      // TODO: Use this technique instead of the useTableScroll hook
      onScroll: (event, {baseFunc}) => {
        baseFunc(event);
      },
      ref,
      style: {
        ...maxHeightStyle,
        overflow: isLoading ? "none" : "auto"
      }
    })
  };
};

export {useKaTableWrapperComponent};
