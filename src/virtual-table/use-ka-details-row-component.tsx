import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IDataRowProps} from "ka-table/props";
import {useMemo} from "react";
import type {ReactNode} from "react";

const createDetailsRowContent =
  <TItem extends AnyObject>(renderExpandedItem?: (item: TItem) => ReactNode) =>
  (props: IDataRowProps) =>
    renderExpandedItem ? renderExpandedItem(props.rowData.item) : null;

const useKaDetailsRowComponent = <TItem extends AnyObject>(
  renderExpandedItem?: (item: TItem) => ReactNode
): ChildComponent<IDataRowProps> => {
  const DetailsRowContent = useMemo(
    () => createDetailsRowContent(renderExpandedItem),
    [renderExpandedItem]
  );

  return {
    content: DetailsRowContent
  };
};

export {useKaDetailsRowComponent};
