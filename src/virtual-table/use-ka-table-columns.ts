import type {AnyObject} from "@unlockre/utils-object/dist";
import {useArray} from "@unlockre/utils-react/dist";
import {ensureIsDefined} from "@unlockre/utils-validation/dist";
import {useCallback, useMemo} from "react";

import * as withKaTableColumn from "./ka-table-column";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";

const useKaTableColumns = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[]
) => {
  const [kaTableColumns, kaTableColumnsActions] = useArray(
    useMemo(
      () => tableColumns.map(withTableColumn.toKaTableColumn),
      // TODO: Remove this temporary fix once useAllTableColumns stabilizes tableColumns generation
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [JSON.stringify(tableColumns)]
    )
  );

  const updateKaTableColumnSortDirection = useCallback(
    (kaTableColumnKey: string) => {
      const kaTableColumnIndex = kaTableColumns.findIndex(
        kaTableColumn => kaTableColumn.key === kaTableColumnKey
      );

      const kaTableColumn = ensureIsDefined(
        kaTableColumns[kaTableColumnIndex],
        "No column found with key: " + kaTableColumnKey
      );

      const sortedKaTableColumnIndex = kaTableColumns.findIndex(
        kaTableColumn => kaTableColumn.sortDirection
      );

      if (sortedKaTableColumnIndex >= 0) {
        const sortedKaTableColumn = kaTableColumns[sortedKaTableColumnIndex];

        kaTableColumnsActions.update(
          sortedKaTableColumnIndex,
          withKaTableColumn.setSortDirection(sortedKaTableColumn, undefined)
        );
      }

      kaTableColumnsActions.update(
        kaTableColumnIndex,
        withKaTableColumn.setNextSortDirection(kaTableColumn)
      );
    },
    [kaTableColumns, kaTableColumnsActions]
  );

  return {kaTableColumns, updateKaTableColumnSortDirection};
};

export {useKaTableColumns};
