import type {AnyObject} from "@unlockre/utils-object/dist";
import type {Column as KaTableColumn} from "ka-table/models";

import type {Theme} from "@/theme-provider/theme";

import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import * as withTableColumnGroup from "./table-column-group";
import type {
  TableColumnGroup,
  TableColumnGroupRecord
} from "./table-column-group";
import {virtualTableClassNames} from "./virtual-table-class-names";

type GetFixedCellClassNameParams<TItem extends AnyObject> = {
  hasScrolledHorizontally?: boolean;
  kaTableColumn: KaTableColumn;
  tableColumns: TableColumn<TItem>[];
};

type GetGroupedColumnCellStyleParams<TItem extends AnyObject> = {
  tableColumn: TableColumn<TItem>;
  tableColumnGroupRecord: TableColumnGroupRecord<TItem>;
  tableColumnGroups: TableColumnGroup<TItem>[];
  tableColumns: TableColumn<TItem>[];
  theme: Theme;
};

const getFixedCellClassName = <TItem extends AnyObject>({
  hasScrolledHorizontally,
  kaTableColumn,
  tableColumns
}: GetFixedCellClassNameParams<TItem>) =>
  withTableColumn.isLastFixed(
    tableColumns,
    withTableColumn.getKeyFrom(kaTableColumn)
  ) && hasScrolledHorizontally
    ? virtualTableClassNames.fixedCell
    : undefined;

// eslint-disable-next-line complexity, max-statements
const getGroupedColumnCellStyle = <TItem extends AnyObject>({
  tableColumn,
  tableColumnGroupRecord,
  tableColumnGroups,
  tableColumns,
  theme
}: GetGroupedColumnCellStyleParams<TItem>) => {
  const isFirstTableColumn = withTableColumn.isFirst(tableColumns, tableColumn);

  const isLastTableColumn = withTableColumn.isLast(tableColumns, tableColumn);

  const isFirstTableColumnInGroup = withTableColumnGroup.isFirstTableColumn(
    tableColumnGroupRecord,
    tableColumn
  );

  const isLastTableColumnInGroup = withTableColumnGroup.isLastTableColumn(
    tableColumnGroupRecord,
    tableColumn
  );

  const currentTableColumnGroup = tableColumn.groupKey
    ? tableColumnGroupRecord[tableColumn.groupKey]
    : undefined;

  const nextTableColumnGroup =
    currentTableColumnGroup &&
    withTableColumnGroup.getNext(tableColumnGroups, currentTableColumnGroup);

  const areConsecutiveTableColumnGroups =
    currentTableColumnGroup &&
    nextTableColumnGroup &&
    withTableColumnGroup.areConsecutive(
      tableColumns,
      currentTableColumnGroup,
      nextTableColumnGroup
    );

  const needsLeftBorder = isFirstTableColumnInGroup && !isFirstTableColumn;

  const needsRightBorder =
    !areConsecutiveTableColumnGroups &&
    isLastTableColumnInGroup &&
    !isLastTableColumn;

  return {
    ...(needsLeftBorder && {
      borderLeft: `1px solid ${theme.colors.palette.gray[100]}`
    }),
    ...(needsRightBorder && {
      borderRight: `1px solid ${theme.colors.palette.gray[100]}`
    })
  };
};

export {getFixedCellClassName, getGroupedColumnCellStyle};
export type {GetGroupedColumnCellStyleParams};
