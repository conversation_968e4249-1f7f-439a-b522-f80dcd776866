import * as withArray from "@unlockre/utils-array/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";

import * as withItem from "./item";
import type {GetItemId} from "./item";
import type {ReorderRowsAction} from "./ka-table-action";

const reorderItems = <TItem extends AnyObject>(
  reorderRowsAction: ReorderRowsAction,
  getItemId: GetItemId<TItem>,
  items: TItem[]
) =>
  withArray.move(
    items,
    withItem.getIndex(items, getItemId, reorderRowsAction.rowKeyValue),
    withItem.getIndex(items, getItemId, reorderRowsAction.targetRowKeyValue)
  );

export {reorderItems};
