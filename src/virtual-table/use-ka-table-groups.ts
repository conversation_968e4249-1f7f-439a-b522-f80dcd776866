import type {AnyObject} from "@unlockre/utils-object/dist";
import {useMemo} from "react";

import * as withTableColumn from "./table-column";

type Params<TItem extends AnyObject> = {
  groupBy?: Extract<keyof TItem, string>;
};

const useKaTableGroups = <TItem extends AnyObject>({groupBy}: Params<TItem>) =>
  useMemo(
    () =>
      groupBy && [
        {
          columnKey: withTableColumn.getKaTableColumnKey(groupBy),
          enableSummary: true
        }
      ],
    [groupBy]
  );

export {useKaTableGroups};
export type {Params as UseKaTableGroupsParams};
