import type {
  KaTableActionType,
  kaTableActionTypes
} from "./ka-table-action-type";

type KaTableActionFrom<
  TKaTableActionType extends KaTableActionType,
  TPayload = unknown
> = TPayload & {
  type: TKaTableActionType;
};

type UpdateSortDirection = KaTableActionFrom<
  typeof kaTableActionTypes.updateSortDirection,
  {columnKey: string}
>;

type ReorderRowsAction = KaTableActionFrom<
  typeof kaTableActionTypes.reorderRows,
  // these are ids that's why they are string (in ka-table are typed as any)
  {
    rowKeyValue: string;
    targetRowKeyValue: string;
  }
>;

type KaTableAction = ReorderRowsAction | UpdateSortDirection;

type ExtractKaTableAction<
  TKaTableAction extends KaTableAction,
  TKaTableActionType extends KaTableActionType
> = Extract<TKaTableAction, {type: TKaTableActionType}>;

const hasTypeOf = <
  TKaTableAction extends KaTableAction,
  TKaTableActionType extends KaTableActionType
>(
  action: TKaTableAction,
  actionType: TKaTableActionType
): action is ExtractKaTableAction<TKaTableAction, TKaTableActionType> =>
  action.type === actionType;

export {hasTypeOf};
export type {KaTableAction, ReorderRowsAction, UpdateSortDirection};
