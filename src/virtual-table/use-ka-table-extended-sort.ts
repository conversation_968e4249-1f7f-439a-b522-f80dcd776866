import * as withObject from "@unlockre/utils-object/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";
import {useCallback} from "react";

import type {KaItem} from "./item";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import {tableSortDirections} from "./table-sort-direction";
import type {TableSortDirection} from "./table-sort-direction";
import type {KaTableExtendedSortFunc} from "./types";

type SortFunction<TItem extends AnyObject> = (
  firstItem: TItem,
  secondItem: TItem,
  sortDirection?: TableSortDirection
) => -1 | 0 | 1;

type Params<TItem extends AnyObject> = {
  tableColumns: TableColumn<TItem>[];
};

const handleTableColumnDefaultSort = <TItem extends AnyObject>(
  kaItems: KaItem<TItem>[],
  tableColumn: TableColumn<TItem>,
  tableSortDirection?: TableSortDirection
) => {
  const getValue = (kaItem: KaItem<TItem>) => {
    const value = kaItem.item[tableColumn.key as keyof TItem];

    if (typeof value !== "number" && typeof value !== "string") {
      throw new Error("Expected value to be a string or number");
    }

    return value as number | string;
  };

  const defaultSortFunction =
    tableSortDirection === tableSortDirections.ascendant
      ? withObject.compareAscBy(getValue)
      : withObject.compareDescBy(getValue);

  return tableSortDirection ? [...kaItems].sort(defaultSortFunction) : kaItems;
};

const handleTableColumnSort = <TItem extends AnyObject>(
  kaItems: KaItem<TItem>[],
  sortFunction: SortFunction<TItem>,
  tableSortDirection?: TableSortDirection
) =>
  [...kaItems].sort((firstKaItem, secondKaItem) =>
    sortFunction(firstKaItem.item, secondKaItem.item, tableSortDirection)
  );

const sortKaItems = <TItem extends AnyObject>(
  kaItems: KaItem<TItem>[],
  tableColumn: TableColumn<TItem>,
  tableSortDirection?: TableSortDirection
) => {
  const {sortFunction} = tableColumn;

  if (sortFunction) {
    return handleTableColumnSort(kaItems, sortFunction, tableSortDirection);
  }

  const firstKaItem = kaItems[0];

  if (firstKaItem && tableColumn.key in firstKaItem.item) {
    return handleTableColumnDefaultSort(
      kaItems,
      tableColumn,
      tableSortDirection
    );
  }

  return kaItems;
};

const useKaTableExtendedSort = <TItem extends AnyObject>({
  tableColumns
}: Params<TItem>): KaTableExtendedSortFunc<TItem> =>
  useCallback(
    (kaItems, kaTableColumns) => {
      const sortedTableColumn = withTableColumn.findSortedTableColumn(
        tableColumns,
        kaTableColumns
      );

      if (!sortedTableColumn) {
        return kaItems;
      }

      const sortedKaItems = sortKaItems(
        kaItems,
        sortedTableColumn.tableColumn,
        sortedTableColumn.tableSortDirection
      );

      return sortedKaItems;
    },
    [tableColumns]
  );

export {useKaTableExtendedSort};
export type {Params as UseKaTableExtendedSortParams};
