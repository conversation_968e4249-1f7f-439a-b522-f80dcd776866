import {useElementEvent} from "@unlockre/utils-react/dist";
import type {EventHandler} from "@unlockre/utils-react/dist";
import {useState} from "react";

type TableScrollingState = {
  scrollLeft: number;
  scrollTop: number;
};

type ScrollableElementState = HTMLDivElement | null;

const useTableScroll = (onScroll?: EventHandler<HTMLDivElement>) => {
  const [tableScroll, setTableScroll] = useState<TableScrollingState>({
    scrollLeft: 0,
    scrollTop: 0
  });

  const [scrollableElement, setScrollableElement] =
    useState<ScrollableElementState>(null);

  const handleTableScroll: EventHandler<HTMLDivElement> = (event, target) => {
    setTableScroll({
      scrollLeft: target.scrollLeft,
      scrollTop: target.scrollTop
    });

    onScroll?.(event, target);
  };

  useElementEvent(scrollableElement, "scroll", handleTableScroll);

  return {
    setScrollableElement,
    tableScrollLeft: tableScroll.scrollLeft,
    tableScrollTop: tableScroll.scrollTop
  };
};

export {useTableScroll};
export type {ScrollableElementState};
