import type {AnyObject} from "@unlockre/utils-object/dist";
import {useMemo} from "react";

import type {TableColumn} from "./table-column";
import * as withTableColumnGroup from "./table-column-group";

const useTableColumnGroups = <TItem extends AnyObject>(
  tableColumns: TableColumn<TItem>[]
) =>
  useMemo(
    () => withTableColumnGroup.createAllFrom(tableColumns),
    [tableColumns]
  );

export {useTableColumnGroups};
