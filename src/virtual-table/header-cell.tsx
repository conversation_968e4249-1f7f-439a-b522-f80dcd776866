import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ReactNode} from "react";
import styled from "styled-components";
import type {CSSProperties} from "styled-components";

import {useTooltip} from "@/tooltip";

import type {CellHorizontalAlignType} from "./cell-horizontal-align-type";
import {HeaderCellTooltip} from "./header-cell-tooltip";
import {TableCell} from "./table-cell";
import type {TableColumn} from "./table-column";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Props<TItem extends AnyObject> = {
  children: ReactNode;
  className?: string;
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

type ContainerStyledProps = {
  $horizontalAlign?: CellHorizontalAlignType;
  $isClickable?: boolean;
  $variant: VirtualTableVariant;
};

const getFixedElement = (tableCellElement: HTMLElement | null) =>
  (tableCellElement?.offsetParent as HTMLElement) ?? null;

const variants = {
  comfortable: {
    padding: "16px"
  },
  dense: {
    padding: "16px"
  }
} as const satisfies Record<VirtualTableVariant, CSSProperties>;

const Container = styled(TableCell)<ContainerStyledProps>`
  ${props => variants[props.$variant]}

  box-sizing: border-box;
  cursor: ${props => (props.$isClickable ? "pointer" : "default")};
  display: flex;
  justify-content: ${props => props.$horizontalAlign};
  text-align: ${props => props.$horizontalAlign};
  user-select: none;
  width: 100%;
`;

const HeaderCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => {
  const {hideTooltip, isTooltipOpened, reference, showTooltip, tooltipProps} =
    useTooltip("top");

  const containerTooltipProps = tableColumn.description
    ? {
        onMouseEnter: showTooltip,
        onMouseLeave: hideTooltip,
        ref: reference
      }
    : {};

  return (
    <>
      <Container
        {...{getFixedElement}}
        {...rest}
        $horizontalAlign={tableColumn.horizontalAlign}
        $isClickable={tableColumn.isSortable}
        $variant={variant}
        isFixed={tableColumn.isFixed}
        {...containerTooltipProps}
      />
      {tableColumn.description && isTooltipOpened ? (
        <HeaderCellTooltip {...tooltipProps}>
          {tableColumn.description}
        </HeaderCellTooltip>
      ) : null}
    </>
  );
};

export {HeaderCell};
