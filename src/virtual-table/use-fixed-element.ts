import {useElementStyle, useResizeObserver} from "@unlockre/utils-react/dist";
import {useCallback, useLayoutEffect, useMemo, useState} from "react";

const getFixedStyle = (offsetLeft: number): Partial<CSSStyleDeclaration> => ({
  background: "inherit",
  left: offsetLeft + "px",
  position: "sticky",
  zIndex: "1"
});

const getTotalWidth = (elements: HTMLElement[]) =>
  elements.reduce((totalWidth, element) => totalWidth + element.offsetWidth, 0);

const getPreviousElement = (element: HTMLElement) =>
  element.previousElementSibling as HTMLElement | null;

const getAllPreviousElements = (element: HTMLElement) => {
  let previousElements: HTMLElement[] = [];

  let previousElement = getPreviousElement(element);

  while (previousElement) {
    previousElements = [...previousElements, previousElement];

    previousElement = getPreviousElement(previousElement);
  }

  return previousElements;
};

const getLeft = (fixedElement: HTMLElement | null) =>
  fixedElement
    ? getTotalWidth(getAllPreviousElements(fixedElement))
    : undefined;

const useOffsetLeft = (fixedElement: HTMLElement | null) => {
  const [offsetLeft, setOffsetLeft] = useState<number | undefined>(undefined);

  const updateOffsetLeft = useCallback(() => {
    setOffsetLeft(getLeft(fixedElement));
  }, [fixedElement]);

  useLayoutEffect(() => {
    updateOffsetLeft();
  }, [updateOffsetLeft]);

  const previousElements = useMemo(
    () => (fixedElement ? getAllPreviousElements(fixedElement) : []),
    [fixedElement]
  );

  useResizeObserver(previousElements, updateOffsetLeft);

  return offsetLeft;
};

const useFixedElement = (fixedElement: HTMLElement | null) => {
  const offsetLeft = useOffsetLeft(fixedElement);

  const fixedStyle = useMemo(
    () => (offsetLeft !== undefined ? getFixedStyle(offsetLeft) : undefined),
    [offsetLeft]
  );

  useElementStyle({element: fixedElement, style: fixedStyle});
};

export {useFixedElement};
