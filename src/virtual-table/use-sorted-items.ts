import type {AnyObject} from "@unlockre/utils-object/dist";
import {useEffect, useMemo} from "react";

import * as withItem from "./item";
import type {KaItem} from "./item";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import type {TableSortDirection} from "./table-sort-direction";
import type {KaTableExtendedSortFunc} from "./types";

type SortHandlerParams<TItem extends AnyObject> = {
  sortedItems: TItem[];
  tableColumn: TableColumn<TItem>;
  tableSortDirection?: TableSortDirection;
};

type SortHandler<TItem extends AnyObject> = (
  params: SortHandlerParams<TItem>
) => unknown;

type Params<TItem extends AnyObject> = {
  items: TItem[];
  kaItems: KaItem<TItem>[];
  kaTableColumns: TableColumn<TItem>[];
  kaTableExtendedSort: KaTableExtendedSortFunc<TItem>;
  onSort?: SortHandler<TItem>;
  tableColumns: TableColumn<TItem>[];
};

const useSortedItems = <TItem extends AnyObject>({
  items,
  kaItems,
  kaTableColumns,
  kaTableExtendedSort,
  onSort,
  tableColumns
}: Params<TItem>) => {
  const sortedTableColumn = useMemo(
    () => withTableColumn.findSortedTableColumn(tableColumns, kaTableColumns),
    [kaTableColumns, tableColumns]
  );

  const sortedItems = useMemo(() => {
    if (!sortedTableColumn) {
      return items;
    }

    return kaTableExtendedSort(kaItems, kaTableColumns).map(
      withItem.fromKaItem
    );
  }, [kaItems, kaTableColumns, kaTableExtendedSort, items, sortedTableColumn]);

  useEffect(() => {
    if (!sortedTableColumn) {
      return;
    }

    const {tableColumn, tableSortDirection} = sortedTableColumn;

    onSort?.({sortedItems, tableColumn, tableSortDirection});
  }, [items, onSort, sortedItems, sortedTableColumn]);

  return sortedItems;
};

export {useSortedItems};
export type {Params as UseSortedItemsParams};
