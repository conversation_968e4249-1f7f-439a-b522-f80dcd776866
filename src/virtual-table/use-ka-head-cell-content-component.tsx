import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IHeadCellProps} from "ka-table/props";
import {useMemo} from "react";

import {DefaultHeaderCell} from "./default-header-cell";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Params<TItem extends AnyObject> = {
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
};

type CreateHeadCellContentParams<TItem extends AnyObject> = {
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
};

const createHeadCellContent =
  <TItem extends AnyObject>({
    tableColumns,
    variant
  }: CreateHeadCellContentParams<TItem>) =>
  ({column}: IHeadCellProps) => {
    const tableColumn = withTableColumn.find(
      tableColumns,
      withTableColumn.getKeyFrom(column)
    );

    if (!tableColumn) {
      return null;
    }

    if (tableColumn.renderHeaderCell) {
      return tableColumn.renderHeaderCell({tableColumn, variant});
    }

    return <DefaultHeaderCell {...{tableColumn, variant}} />;
  };

const useKaHeadCellContentComponent = <TItem extends AnyObject>({
  tableColumns,
  variant
}: Params<TItem>): ChildComponent<IHeadCellProps> => {
  const HeadCellContent = useMemo(
    () => createHeadCellContent({tableColumns, variant}),
    [tableColumns, variant]
  );

  return {
    content: HeadCellContent,
    elementAttributes: ({column: kaTableColumn}) => ({
      style: {
        alignItems: "center",
        display: "flex",
        gap: 8,
        minWidth: kaTableColumn.width
      }
    })
  };
};

export {useKaHeadCellContentComponent};
