import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IHeadCellProps} from "ka-table/props";
import {useMemo} from "react";
import {useTheme} from "styled-components";

import * as withKaCell from "./ka-cell";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import * as withTableColumnGroup from "./table-column-group";
import type {TableColumnGroup} from "./table-column-group";

type Params<TItem extends AnyObject> = {
  hasScrolledHorizontally?: boolean;
  tableColumnGroups: TableColumnGroup<TItem>[];
  tableColumns: TableColumn<TItem>[];
};

const useKaHeadCellComponent = <TItem extends AnyObject>({
  hasScrolledHorizontally,
  tableColumnGroups,
  tableColumns
}: Params<TItem>): ChildComponent<IHeadCellProps> => {
  const theme = useTheme();

  const tableColumnGroupRecord = useMemo(
    () => withTableColumnGroup.getTableColumnGroupRecord(tableColumnGroups),
    [tableColumnGroups]
  );

  return {
    elementAttributes: ({column}) => {
      const tableColumn = withTableColumn.find(
        tableColumns,
        withTableColumn.getKeyFrom(column)
      );

      if (!tableColumn) {
        return {style: {display: "none"}};
      }

      return {
        className: withKaCell.getFixedCellClassName({
          hasScrolledHorizontally,
          kaTableColumn: column,
          tableColumns
        }),
        style: {
          padding: 0,
          maxWidth: column.width,
          ...withKaCell.getGroupedColumnCellStyle({
            tableColumn,
            tableColumnGroupRecord,
            tableColumnGroups,
            tableColumns,
            theme
          }),
          ...(tableColumn.getBackgroundColor
            ? {backgroundColor: tableColumn.getBackgroundColor({theme})}
            : {})
        }
      };
    }
  };
};

export {useKaHeadCellComponent};
