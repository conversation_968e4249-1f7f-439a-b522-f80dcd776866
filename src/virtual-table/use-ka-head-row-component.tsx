import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IHeadRowProps} from "ka-table/props";
import {useTheme} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";

type Params = {
  getHeaderRowBackgroundColor?: GetColor;
  hasScrolledVertically?: boolean;
  withNoHeader?: boolean;
};

const getDefaultHeaderRowBackgroundColor = getColorByAlias("backgroundWhite");

const useKaHeadRowComponent = ({
  getHeaderRowBackgroundColor = getDefaultHeaderRowBackgroundColor,
  hasScrolledVertically,
  withNoHeader
}: Params): ChildComponent<IHeadRowProps> => {
  const theme = useTheme();

  return {
    elementAttributes: () => ({
      style: {
        ...(withNoHeader
          ? {display: "none"}
          : {
              outline: `1px solid ${theme.colors.palette.gray["070"]}`
            }),
        backgroundColor: getHeaderRowBackgroundColor({theme}),
        boxShadow: hasScrolledVertically
          ? "0px 2px 16px 4px rgba(7, 24, 84, 0.08)"
          : "none",
        position: "sticky",
        top: 0,
        zIndex: 2
      }
    })
  };
};

export {useKaHeadRowComponent};
