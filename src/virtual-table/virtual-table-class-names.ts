const virtualTableClassNames = {
  dragTableRowIcon: "virtual-table-drag-table-row-icon",
  fixedCell: "virtual-table-fixed-cell",
  /**
   * @description You can avoid using this class by passing a custom renderHeaderCell function to the table columns
   */
  headerCell: "virtual-table-header-cell",
  /**
   * @description You can avoid using this class by passing a custom renderItemCell function to the table columns
   */
  itemCell: "virtual-table-item-cell"
};

export {virtualTableClassNames};
