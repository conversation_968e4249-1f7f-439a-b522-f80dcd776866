import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {INoDataRowProps} from "ka-table/props";
import type {ReactNode} from "react";

const useKaNoDataRowComponent = (
  renderEmptyElement?: () => ReactNode
): ChildComponent<INoDataRowProps> => ({
  content: () => renderEmptyElement?.(),
  elementAttributes: props => ({
    style: {
      // We need to use ka-table's loading prop instead of VirtualTable's prop
      // because ka-table's prop is updated later, so using the other prop
      // would cause the no-data row to appear while ka-table is still in
      // loading state
      display: props.loading?.enabled ? "none" : undefined
    }
  })
});

export {useKaNoDataRowComponent};
