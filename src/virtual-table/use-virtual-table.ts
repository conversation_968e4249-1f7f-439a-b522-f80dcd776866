import type {AnyObject} from "@unlockre/utils-object/dist";

import type {TableColumn} from "./table-column";
import {useAllTableColumns} from "./use-all-table-columns";
import type {UseAllTableColumnsParams} from "./use-all-table-columns";
import {useKaTableProps} from "./use-ka-table-props";
import type {UseKaTablePropsParams} from "./use-ka-table-props";

type ExposedUseKaTablePropsParams<TItem extends AnyObject> = Omit<
  UseKaTablePropsParams<TItem>,
  "tableColumns"
>;

type ExposedUseAllTableColumnsParams<TItem extends AnyObject> = Omit<
  UseAllTableColumnsParams<TItem>,
  "tableColumns"
>;

// prettier-ignore
type Params<TItem extends AnyObject> =
  & ExposedUseAllTableColumnsParams<TItem>
  & ExposedUseKaTablePropsParams<TItem>
  & {
      columns: TableColumn<TItem>[];
    }

const useVirtualTable = <TItem extends AnyObject>({
  areRowsExpansible,
  columns,
  expandedItems,
  getItemId,
  isLoading,
  isRowDraggable,
  isRowSelectable,
  onExpandedItemsChange,
  onSelectedItemsChange,
  selectedItems,
  ...rest
}: Params<TItem>) => {
  const tableColumns = useAllTableColumns({
    areRowsExpansible,
    expandedItems,
    getItemId,
    isRowDraggable,
    isRowSelectable,
    onExpandedItemsChange,
    onSelectedItemsChange,
    selectedItems,
    tableColumns: columns
  });

  const kaTableProps = useKaTableProps<TItem>({
    getItemId,
    isLoading,
    isRowDraggable,
    selectedItems,
    tableColumns,
    ...rest
  });

  return {kaTableProps};
};

export {useVirtualTable};
export type {Params as UseVirtualTableParams};
