import type {AnyObject} from "@unlockre/utils-object/dist";
import {useMemo} from "react";

import * as withItem from "./item";
import type {GetItemId} from "./item";

type Params<TItem extends AnyObject> = {
  getItemId: GetItemId<TItem>;
  items: TItem[];
};

const useKaItems = <TItem extends AnyObject>({
  getItemId,
  items
}: Params<TItem>) =>
  useMemo(
    () => items.map(item => withItem.toKaItem(item, getItemId)),
    [getItemId, items]
  );

export {useKaItems};
export type {Params as UseKaItemsParams};
