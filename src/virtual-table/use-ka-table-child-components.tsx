import type {AnyObject} from "@unlockre/utils-object/dist";
import type {EventHandler} from "@unlockre/utils-react/dist";
import mergeRefs from "merge-refs";
import type {MouseEvent, ReactNode} from "react";

import type {GetColor} from "@/theme-provider/theme";

import type {GetItemId} from "./item";
import type {PagingOptions} from "./paging-options";
import type {TableColumn} from "./table-column";
import type {TableColumnGroup} from "./table-column-group";
import {useKaCellComponent} from "./use-ka-cell-component";
import {useKaDataRowComponent} from "./use-ka-data-row-component";
import {useKaDetailsRowComponent} from "./use-ka-details-row-component";
import {useKaGroupRowComponent} from "./use-ka-group-row-component";
import {useKaGroupSummaryCellComponent} from "./use-ka-group-summary-cell-component";
import {useKaGroupSummaryRowComponent} from "./use-ka-group-summary-row-component";
import {useKaHeadCellComponent} from "./use-ka-head-cell-component";
import {useKaHeadCellContentComponent} from "./use-ka-head-cell-content-component";
import {useKaHeadRowComponent} from "./use-ka-head-row-component";
import {useKaLoadingComponent} from "./use-ka-loading-component";
import {useKaNoDataRowComponent} from "./use-ka-no-data-row-component";
import {useKaPagingPagesComponent} from "./use-ka-paging-pages-component";
import {useKaRootDivComponent} from "./use-ka-root-div-component";
import {useKaSortIconComponent} from "./use-ka-sort-icon-component";
import {useKaSummaryCellComponent} from "./use-ka-summary-cell-component";
import {useKaSummaryRowComponent} from "./use-ka-summary-row-component";
import {useKaTableComponent} from "./use-ka-table-component";
import {useKaTableWrapperComponent} from "./use-ka-table-wrapper-component";
import {useTableScroll} from "./use-table-scroll";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Params<TItem extends AnyObject> = {
  getGroupDescription?: (groupValue: string) => string;
  getHeaderRowBackgroundColor?: GetColor;
  getItemId: GetItemId<TItem>;
  getRowHighlightedBorderColor?: (item: TItem) => GetColor | undefined;
  groupBy?: Extract<keyof TItem, string>;
  height?: number;
  isLoading?: boolean;
  isRowClickable?: (item: TItem) => boolean;
  isRowDraggable?: (item: TItem) => boolean;
  isRowHighlighted?: (item: TItem, selectedItems?: TItem[]) => boolean;
  isRowZebraStriped?: (item: TItem, items: TItem[]) => boolean;
  items: TItem[];
  onRowClick?: (item: TItem, event: MouseEvent<HTMLElement>) => unknown;
  onRowMouseEnter?: (item: TItem) => unknown;
  onRowMouseLeave?: (item: TItem) => unknown;
  onScroll?: EventHandler<HTMLDivElement>;
  pagingOptions?: PagingOptions;
  renderEmptyElement?: () => ReactNode;
  renderExpandedItem?: (item: TItem) => ReactNode;
  selectedItems?: TItem[];
  setTableWrapperRef?: (event: HTMLDivElement | null) => unknown;
  tableColumnGroups: TableColumnGroup<TItem>[];
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
  withBorder?: boolean;
  withNoHeader?: boolean;
  withoutRowBorders?: boolean;
};

// eslint-disable-next-line max-statements
const useKaTableChildComponents = <TItem extends AnyObject>({
  getGroupDescription,
  getHeaderRowBackgroundColor,
  getItemId,
  getRowHighlightedBorderColor,
  groupBy,
  height,
  isLoading,
  isRowClickable,
  isRowDraggable,
  isRowHighlighted,
  isRowZebraStriped,
  items,
  onRowClick,
  onRowMouseEnter,
  onRowMouseLeave,
  onScroll,
  pagingOptions,
  renderEmptyElement,
  renderExpandedItem,
  selectedItems,
  setTableWrapperRef,
  tableColumnGroups,
  tableColumns,
  variant,
  withBorder,
  withNoHeader,
  withoutRowBorders
}: Params<TItem>) => {
  const {setScrollableElement, tableScrollLeft, tableScrollTop} =
    useTableScroll(onScroll);

  const hasScrolledHorizontally = tableScrollLeft > 0;

  const hasScrolledVertically = tableScrollTop > 0;

  const kaRootDivComponent = useKaRootDivComponent({
    height,
    withBorder
  });

  const kaTableComponent = useKaTableComponent();

  const kaTableWrapperComponent = useKaTableWrapperComponent({
    height,
    isLoading,
    ref: mergeRefs(
      setTableWrapperRef,
      setScrollableElement as (event: HTMLDivElement | null) => unknown
    )
  });

  const kaSummaryRowComponent = useKaSummaryRowComponent({
    tableColumns,
    withoutRowBorders
  });

  const kaSummaryCellComponent = useKaSummaryCellComponent({
    hasScrolledHorizontally,
    isLoading,
    items,
    tableColumnGroups,
    tableColumns,
    variant
  });

  const kaSortIconComponent = useKaSortIconComponent();

  const kaPagingPagesComponent = useKaPagingPagesComponent({
    pagingOptions
  });

  const kaNoDataRowComponent = useKaNoDataRowComponent(renderEmptyElement);

  const kaLoadingComponent = useKaLoadingComponent();

  const kaHeadRowComponent = useKaHeadRowComponent({
    getHeaderRowBackgroundColor,
    hasScrolledVertically,
    withNoHeader
  });

  const kaHeadCellContentComponent = useKaHeadCellContentComponent({
    tableColumns,
    variant
  });

  const kaHeadCellComponent = useKaHeadCellComponent({
    hasScrolledHorizontally,
    tableColumns,
    tableColumnGroups
  });

  const kaGroupSummaryRowComponent = useKaGroupSummaryRowComponent();

  const kaGroupSummaryCellComponent = useKaGroupSummaryCellComponent({
    hasScrolledHorizontally,
    isLoading,
    items,
    tableColumnGroups,
    tableColumns,
    variant
  });

  const kaGroupRowComponent = useKaGroupRowComponent({getGroupDescription});

  const kaDetailsRowComponent = useKaDetailsRowComponent(renderExpandedItem);

  const kaDataRowComponent = useKaDataRowComponent({
    getItemId,
    groupBy,
    isRowClickable,
    isRowDraggable,
    isRowHighlighted,
    isRowZebraStriped,
    items,
    onRowClick,
    onRowMouseEnter,
    onRowMouseLeave,
    selectedItems
  });

  const kaCellComponent = useKaCellComponent({
    hasScrolledHorizontally,
    items,
    getItemId,
    getRowHighlightedBorderColor,
    tableColumnGroups,
    tableColumns,
    variant,
    withoutRowBorders
  });

  const childComponents = {
    cell: kaCellComponent,
    dataRow: kaDataRowComponent,
    detailsRow: kaDetailsRowComponent,
    groupRow: kaGroupRowComponent,
    groupSummaryCell: kaGroupSummaryCellComponent,
    groupSummaryRow: kaGroupSummaryRowComponent,
    headCell: kaHeadCellComponent,
    headCellContent: kaHeadCellContentComponent,
    headRow: kaHeadRowComponent,
    loading: kaLoadingComponent,
    noDataRow: kaNoDataRowComponent,
    pagingPages: kaPagingPagesComponent,
    sortIcon: kaSortIconComponent,
    summaryCell: kaSummaryCellComponent,
    summaryRow: kaSummaryRowComponent,
    table: kaTableComponent,
    tableWrapper: kaTableWrapperComponent,
    rootDiv: kaRootDivComponent
  };

  return childComponents;
};

export {useKaTableChildComponents};
export type {Params as UseKaTableChildComponentsParams};
