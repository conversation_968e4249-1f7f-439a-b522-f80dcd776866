import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IGroupSummaryRowProps} from "ka-table/props";
import {useTheme} from "styled-components";

const useKaGroupSummaryRowComponent =
  (): ChildComponent<IGroupSummaryRowProps> => {
    const theme = useTheme();

    return {
      elementAttributes: () => ({
        style: {
          backgroundColor: theme.colors.aliases.backgroundWhite
        }
      })
    };
  };

export {useKaGroupSummaryRowComponent};
