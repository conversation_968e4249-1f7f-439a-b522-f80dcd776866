import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IGroupSummaryCellProps} from "ka-table/props";
import {useMemo} from "react";
import {useTheme} from "styled-components";

import {DefaultGroupSummaryCell} from "./default-group-summary-cell";
import {GroupSummaryCell} from "./group-summary-cell";
import type {KaItem} from "./item";
import * as withKaBodyCell from "./ka-body-cell";
import * as withKaCell from "./ka-cell";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import * as withTableColumnGroup from "./table-column-group";
import type {TableColumnGroup} from "./table-column-group";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Params<TItem extends AnyObject> = {
  hasScrolledHorizontally?: boolean;
  isLoading?: boolean;
  items: TItem[];
  tableColumnGroups: TableColumnGroup<TItem>[];
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
};

type CreateGroupSummaryCellContentParams<TItem extends AnyObject> = Pick<
  Params<TItem>,
  "items" | "tableColumns" | "variant"
>;

const createGroupSummaryCellContent =
  <TItem extends AnyObject>({
    items,
    tableColumns,
    variant
  }: CreateGroupSummaryCellContentParams<TItem>) =>
  ({column, groupData}: IGroupSummaryCellProps) => {
    const tableColumn = withTableColumn.find(
      tableColumns,
      withTableColumn.getKeyFrom(column)
    );

    if (!tableColumn) {
      return null;
    }

    const groupKaItems = groupData as KaItem<TItem>[];

    const groupItems = useMemo(
      () => groupKaItems.map(kaItem => kaItem.item),
      [groupKaItems]
    );

    if (withTableColumn.isSpecial(tableColumn)) {
      return <GroupSummaryCell {...{items, tableColumn, variant}} />;
    }

    if (tableColumn.renderGroupSummaryCell) {
      return tableColumn.renderGroupSummaryCell({
        groupItems,
        tableColumn,
        variant
      });
    }

    return (
      <DefaultGroupSummaryCell {...{groupItems, items, tableColumn, variant}} />
    );
  };

const useKaGroupSummaryCellComponent = <TItem extends AnyObject>({
  hasScrolledHorizontally,
  isLoading,
  items,
  tableColumnGroups,
  tableColumns,
  variant
}: Params<TItem>): ChildComponent<IGroupSummaryCellProps> => {
  const theme = useTheme();

  const GroupSummaryCellContent = useMemo(
    () =>
      createGroupSummaryCellContent({
        items,
        tableColumns,
        variant
      }),
    [items, tableColumns, variant]
  );

  const tableHasGroupSummaryRow = tableColumns.some(
    withTableColumn.hasGroupSummaryRow
  );

  const tableColumnGroupRecord = useMemo(
    () => withTableColumnGroup.getTableColumnGroupRecord(tableColumnGroups),
    [tableColumnGroups]
  );

  return {
    content:
      !isLoading && tableHasGroupSummaryRow
        ? GroupSummaryCellContent
        : undefined,
    elementAttributes: ({column}) => {
      const tableColumn = withTableColumn.find(
        tableColumns,
        withTableColumn.getKeyFrom(column)
      );

      if (!tableColumn) {
        return {style: {display: "none"}};
      }

      return {
        className: withKaCell.getFixedCellClassName({
          hasScrolledHorizontally,
          kaTableColumn: column,
          tableColumns
        }),
        style: withKaBodyCell.getStyle({
          tableColumn,
          tableColumnGroupRecord,
          tableColumnGroups,
          tableColumns,
          theme
        })
      };
    }
  };
};

export {useKaGroupSummaryCellComponent};
