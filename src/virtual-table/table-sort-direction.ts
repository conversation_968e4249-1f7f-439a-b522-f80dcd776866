import {SortDirection as KaSortDirection} from "ka-table";

type KaSortDirectionType = `${KaSortDirection}`;

const tableSortDirections = {
  ascendant: "ASC",
  descendant: "DESC"
} as const;

type TableSortDirection =
  (typeof tableSortDirections)[keyof typeof tableSortDirections];

const toKaSortDirection = (tableSortDirection?: TableSortDirection) => {
  if (!tableSortDirection) {
    return undefined;
  }

  return tableSortDirection === tableSortDirections.ascendant
    ? KaSortDirection.Ascend
    : KaSortDirection.Descend;
};

const fromKaSortDirection = (kaSortDirection?: KaSortDirectionType) => {
  if (!kaSortDirection) {
    return undefined;
  }

  return kaSortDirection === KaSortDirection.Ascend
    ? tableSortDirections.ascendant
    : tableSortDirections.descendant;
};

export {fromKaSortDirection, tableSortDirections, toKaSortDirection};
export type {TableSortDirection};
