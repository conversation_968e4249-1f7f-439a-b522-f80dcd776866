import colorAlpha from "color-alpha";
import {forwardRef} from "react";
import type {ComponentProps, ComponentRef} from "react";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";
import Tooltip from "@/tooltip";

type Props = Omit<ComponentProps<typeof Tooltip>, "size" | "variant">;
type TooltipElement = ComponentRef<typeof Tooltip>;

const StyledTooltip = styled(Tooltip)`
  max-width: 250px;
  padding: 16px;
  box-shadow: 0px 4px 25px 0px
    ${props => colorAlpha(getColor("blue", "900")(props), 0.1)};
`;

const HeaderCellTooltip = forwardRef<TooltipElement, Props>((props, ref) => (
  <StyledTooltip {...props} ref={ref} size="small" variant="white" />
));

export {HeaderCellTooltip};
