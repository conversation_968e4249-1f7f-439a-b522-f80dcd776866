type Params = {
  currentPage: number;
  pageCount: number;
};

const range = (start: number, end: number, step = 1) => {
  const output = [];

  if (typeof end === "undefined") {
    end = start;
    start = 0;
  }

  for (let i = start; i <= end; i += step) {
    output.push(i);
  }

  return output;
};

const dots = "...";

// eslint-disable-next-line complexity, max-statements
const usePaginationItems = ({currentPage, pageCount}: Params) => {
  // First (or last) page + dots.
  const onePageAndDotsCount = 2;

  const currentPageCount = 1;

  const siblingPageCount = 2;

  // Left siblings + current page + right siblings.
  const middlePageCount = currentPageCount + siblingPageCount * 2;

  // First (or last) page + dots + middle pages.
  const oneSidePaginationItemCount = onePageAndDotsCount + middlePageCount;

  // First page + dots + middle pages + dots + last page.
  const maxPaginationItemCount = onePageAndDotsCount * 2 + middlePageCount;

  const showAllPages = pageCount < maxPaginationItemCount;

  if (showAllPages) {
    return range(1, pageCount);
  }

  const minSiblingPage = Math.max(currentPage - siblingPageCount, 1);

  const maxSiblingPage = Math.min(currentPage + siblingPageCount, pageCount);

  const minLeftPageWithoutDots = 3;

  const maxRightPageWithoutDots = pageCount - 2;

  const showLeftDots = minSiblingPage > minLeftPageWithoutDots;

  const showRightDots = maxSiblingPage < maxRightPageWithoutDots;

  if (showLeftDots && !showRightDots) {
    const minLeftPage = pageCount - oneSidePaginationItemCount + 1;
    const rightPages = range(minLeftPage, pageCount);

    return [1, dots, ...rightPages];
  }

  if (!showLeftDots && showRightDots) {
    const leftPages = range(1, oneSidePaginationItemCount);

    return [...leftPages, dots, pageCount];
  }

  const middlePages = range(minSiblingPage, maxSiblingPage);

  return [1, dots, ...middlePages, dots, pageCount];
};

export {usePaginationItems};
