import {DotsThree} from "@phosphor-icons/react";
import styled from "styled-components";

import ChevronButton from "@/chevron-button";
import {IconWithColor} from "@/icons/icon-with-color";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import {usePaginationItems} from "./use-pagination-items";

type Props = {
  currentPage: number;
  onPageChange: (page: number) => unknown;
  pageCount: number;
};

type PageNumberStyledProps = {
  $isCurrent?: boolean;
};

const PageNumber = styled(UnstyledButton)<PageNumberStyledProps>`
  ${getTypography("body", "xs")}

  border-radius: 4px;
  border-color: ${getColorByAlias("accentPrimary")};
  border-width: ${props => (props.$isCurrent ? "2px" : "0")};
  border-style: solid;
  font-variant-numeric: tabular-nums;
  height: 24px;
  min-width: 24px;
  padding: 0 4px;
`;

const DotsThreeIconItem = styled.li`
  align-items: center;
  display: flex;
`;

const TablePagination = ({currentPage, onPageChange, pageCount}: Props) => {
  const paginationItems = usePaginationItems({
    currentPage,
    pageCount
  });

  return (
    <>
      <li>
        <ChevronButton
          direction="left"
          isDisabled={currentPage === 1}
          onClick={() => onPageChange(currentPage - 1)}
          size={24}
        />
      </li>
      {paginationItems.map((paginationItem, index) =>
        typeof paginationItem === "string" ? (
          <DotsThreeIconItem key={index}>
            <IconWithColor
              getColor={getColor("gray", "400")}
              icon={DotsThree}
              size={16}
              weight="regular"
            />
          </DotsThreeIconItem>
        ) : (
          <li key={index}>
            <PageNumber
              $isCurrent={currentPage === paginationItem}
              onClick={() => onPageChange(paginationItem)}
            >
              {paginationItem}
            </PageNumber>
          </li>
        )
      )}
      <li>
        <ChevronButton
          direction="right"
          isDisabled={currentPage === paginationItems.at(-1)}
          onClick={() => onPageChange(currentPage + 1)}
          size={24}
        />
      </li>
    </>
  );
};

export {TablePagination};
