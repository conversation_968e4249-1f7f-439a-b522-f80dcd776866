import {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import {draggableTableRowCss} from "./drag-table-row-column/draggable-table-row";

type KaDataRowCssStyledProps = {
  $areRowsClickable?: boolean;
};

/**
 * We need to add this css here to avoid having to use a state inside
 * the useKaDataRowComponent hook to control the hovering background, as doing
 * this was causing issues when dragging rows because of the extra rerender.
 *
 * We also need to use !important because background colors set in the
 * useKaDataRowComponent hook are passed in the style attribute and we want this
 * one (hovering background) to win over them.
 */
const clickableKaDataRowCss = css`
  cursor: pointer;

  .ka-row:hover {
    background-color: ${getColorByAlias("bgAccentSecondaryHover")} !important;
  }
`;

const kaDataRowCss = css<KaDataRowCssStyledProps>`
  ${props => props.$areRowsClickable && clickableKaDataRowCss}

  ${draggableTableRowCss}
`;

export {kaDataRowCss};
