import type {AnyObject} from "@unlockre/utils-object/dist";
import {useTable} from "ka-table";

import type {GetItemId} from "./item";
import type {KaTableAction} from "./ka-table-action";
import {kaTableActionTypes} from "./ka-table-action-type";
import * as withReorderRowsAction from "./reorder-rows-action";

type Params<TItem extends AnyObject> = {
  getItemId: GetItemId<TItem>;
  items: TItem[];
  onItemsReorder?: (items: TItem[]) => unknown;
  onUpdateSortDirection?: (kaTableColumnKey: string) => unknown;
};

const useKaTable = <TItem extends AnyObject>({
  getItemId,
  items,
  onItemsReorder,
  onUpdateSortDirection
}: Params<TItem>) =>
  useTable({
    onDispatch: (kaTableAction: KaTableAction) => {
      switch (kaTableAction.type) {
        case kaTableActionTypes.reorderRows:
          onItemsReorder?.(
            withReorderRowsAction.reorderItems(kaTableAction, getItemId, items)
          );

          break;

        case kaTableActionTypes.updateSortDirection:
          onUpdateSortDirection?.(kaTableAction.columnKey);

          break;
      }
    }
  });

export {useKaTable};
export type {Params as UseKaTableParams};
