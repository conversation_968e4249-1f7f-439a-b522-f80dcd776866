import type {IKaTableProps} from "ka-table";

type KaPagingOptions = NonNullable<IKaTableProps["paging"]>;

type PagingOptions = {
  isEnabled: boolean;
  onPageChange: (page: number) => unknown;
  pageNumber: number;
  pageSize: number;
  totalRows: number;
};

const getPagesCount = (pagingOptions: PagingOptions) =>
  Math.ceil(pagingOptions.totalRows / pagingOptions.pageSize);

const toKaPagingOptions = (pagingOptions: PagingOptions): KaPagingOptions => ({
  enabled: pagingOptions.isEnabled,
  pagesCount: getPagesCount(pagingOptions)
});

export {toKaPagingOptions};
export type {PagingOptions};
