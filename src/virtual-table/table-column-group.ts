import type {AnyObject} from "@unlockre/utils-object/dist";
import {ensureIsDefined} from "@unlockre/utils-validation/dist";

import type {TableColumn} from "./table-column";

type TableColumnGroup<TItem extends AnyObject> = {
  groupKey: string;
  tableColumns: TableColumn<TItem>[];
};

type TableColumnGroupRecord<TItem extends AnyObject> = Record<
  string,
  TableColumnGroup<TItem>
>;

const areGroupKeysConsecutive = <TItem extends AnyObject>(
  tableColumnGroup: TableColumnGroup<TItem>,
  allTableColumns: TableColumn<TItem>[]
) =>
  tableColumnGroup.tableColumns.every((tableColumn, index) => {
    if (index === 0) {
      return true;
    }

    const currentIndex = allTableColumns.indexOf(tableColumn);

    const prevTableColumn = ensureIsDefined(
      tableColumnGroup.tableColumns[index - 1],
      "No previous table column found"
    );

    const prevIndex = allTableColumns.indexOf(prevTableColumn);

    return currentIndex === prevIndex + 1;
  });

const ensureConsecutiveGroupKeys = <TItem extends AnyObject>(
  tableColumnGroup: TableColumnGroup<TItem>,
  allTableColumns: TableColumn<TItem>[]
) => {
  if (!areGroupKeysConsecutive(tableColumnGroup, allTableColumns)) {
    throw new Error("Nonconsecutive group key: " + tableColumnGroup.groupKey);
  }

  return tableColumnGroup;
};

const addTableColumn = <TItem extends AnyObject>(
  tableColumnGroup: TableColumnGroup<TItem>,
  tableColumn: TableColumn<TItem>
): TableColumnGroup<TItem> => ({
  ...tableColumnGroup,
  tableColumns: [...tableColumnGroup.tableColumns, tableColumn]
});

const create = <TItem extends AnyObject>(
  groupKey: string,
  tableColumns: TableColumn<TItem>[]
): TableColumnGroup<TItem> => ({
  groupKey,
  tableColumns
});

const createTableGroupRecordFrom = <TItem extends AnyObject>(
  allTableColumns: TableColumn<TItem>[]
) =>
  allTableColumns.reduce((tableColumnGroupRecord, tableColumn) => {
    if (!tableColumn.groupKey) {
      return tableColumnGroupRecord;
    }

    const tableColumnGroup = tableColumnGroupRecord[tableColumn.groupKey];

    return {
      ...tableColumnGroupRecord,
      [tableColumn.groupKey]: tableColumnGroup
        ? addTableColumn(tableColumnGroup, tableColumn)
        : create(tableColumn.groupKey, [tableColumn])
    };
  }, {} as TableColumnGroupRecord<TItem>);

const createAllFrom = <TItem extends AnyObject>(
  allTableColumns: TableColumn<TItem>[]
) =>
  Object.values(createTableGroupRecordFrom(allTableColumns)).map(
    tableColumnGroup =>
      ensureConsecutiveGroupKeys(tableColumnGroup, allTableColumns)
  );

const getTableColumnGroupRecord = <TItem extends AnyObject>(
  tableColumnGroups: TableColumnGroup<TItem>[]
) =>
  tableColumnGroups.reduce((tableColumnGroupRecord, tableColumnGroup) => {
    if (tableColumnGroupRecord[tableColumnGroup.groupKey]) {
      throw new Error(
        "Duplicate table column group key: " + tableColumnGroup.groupKey
      );
    }
    return {
      ...tableColumnGroupRecord,
      [tableColumnGroup.groupKey]: tableColumnGroup
    };
  }, {} as TableColumnGroupRecord<TItem>);

const isFirstTableColumn = <TItem extends AnyObject>(
  tableColumnGroupRecord: TableColumnGroupRecord<TItem>,
  tableColumn: TableColumn<TItem>
) =>
  tableColumn.groupKey &&
  tableColumnGroupRecord[tableColumn.groupKey]?.tableColumns[0] === tableColumn;

const isLastTableColumn = <TItem extends AnyObject>(
  tableColumnGroupRecord: TableColumnGroupRecord<TItem>,
  tableColumn: TableColumn<TItem>
) =>
  tableColumn.groupKey &&
  tableColumnGroupRecord[tableColumn.groupKey]?.tableColumns.at(-1) ===
    tableColumn;

const areConsecutive = <TItem extends AnyObject>(
  allTableColumns: TableColumn<TItem>[],
  tableColumnGroup1: TableColumnGroup<TItem>,
  tableColumnGroup2: TableColumnGroup<TItem>
) =>
  allTableColumns.length !== 0 &&
  tableColumnGroup1.tableColumns.length !== 0 &&
  tableColumnGroup2.tableColumns.length !== 0 &&
  // @ts-expect-error: tableColumnGroup1.tableColumns is not empty
  allTableColumns.indexOf(tableColumnGroup1.tableColumns.at(-1)) ===
    allTableColumns.indexOf(tableColumnGroup2.tableColumns[0]) - 1;

const getNext = <TItem extends AnyObject>(
  tableColumnGroups: TableColumnGroup<TItem>[],
  tableColumnGroup: TableColumnGroup<TItem>
) => tableColumnGroups[tableColumnGroups.indexOf(tableColumnGroup) + 1];

export {
  areConsecutive,
  createAllFrom,
  getNext,
  getTableColumnGroupRecord,
  isFirstTableColumn,
  isLastTableColumn
};
export type {TableColumnGroup, TableColumnGroupRecord};
