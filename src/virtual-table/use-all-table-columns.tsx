import type {AnyObject} from "@unlockre/utils-object/dist";
import {useMemo} from "react";
import type {MouseEvent} from "react";

import * as withDragTableRowColumn from "./drag-table-row-column";
import type {GetItemId} from "./item";
import * as withSelectTableRowColumn from "./select-table-row-column";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import * as withToggleTableRowColumn from "./toggle-table-row-column";

type Params<TItem extends AnyObject> = {
  areRowsExpansible?: boolean;
  expandedItems?: TItem[];
  getItemId: GetItemId<TItem>;
  isRowDraggable?: (item: TItem) => boolean;
  isRowSelectable?: (item: TItem) => boolean;
  onExpandedItemsChange?: (expandedItems: TItem[]) => unknown;
  onSelectedItemsChange?: (params: {
    event: MouseEvent<HTMLButtonElement>;
    item: TItem;
    selectedItems: TItem[];
  }) => unknown;
  selectedItems?: TItem[];
  tableColumns: TableColumn<TItem>[];
};

const useAllTableColumns = <TItem extends AnyObject>({
  areRowsExpansible,
  expandedItems,
  getItemId,
  isRowDraggable,
  isRowSelectable,
  onExpandedItemsChange,
  onSelectedItemsChange,
  selectedItems,
  tableColumns
}: Params<TItem>) => {
  withTableColumn.ensureFixedConsecutive(tableColumns);

  withTableColumn.ensureOneInitialSortDirection(tableColumns);

  return useMemo(
    () => [
      ...(isRowDraggable
        ? [withDragTableRowColumn.create(tableColumns, isRowDraggable)]
        : []),
      ...(isRowSelectable
        ? [
            withSelectTableRowColumn.create<TItem>(
              tableColumns,
              getItemId,
              isRowSelectable,
              onSelectedItemsChange,
              selectedItems
            )
          ]
        : []),
      ...tableColumns,
      ...(areRowsExpansible
        ? [
            withToggleTableRowColumn.create<TItem>(
              getItemId,
              expandedItems,
              onExpandedItemsChange
            )
          ]
        : [])
    ],
    // TODO: Move unstable table properties to context to prevent useAllTableColumns from regenerating tableColumns on every change
    [
      areRowsExpansible,
      tableColumns,
      expandedItems,
      getItemId,
      isRowDraggable,
      isRowSelectable,
      onExpandedItemsChange,
      onSelectedItemsChange,
      selectedItems
    ]
  );
};

export {useAllTableColumns};
export type {Params as UseAllTableColumnsParams};
