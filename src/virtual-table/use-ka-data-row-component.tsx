import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IDataRowProps} from "ka-table/props";
import mergeRefs from "merge-refs";
import {useRef} from "react";
import type {MouseEvent} from "react";
import {useTheme} from "styled-components";

import type {Theme} from "@/theme-provider/theme";

import type {GetItemId} from "./item";
import * as withItemGroup from "./item-group";
import type {ItemGroup} from "./item-group";

type NonDraggableElements = Map<string, HTMLTableRowElement>;

type Params<TItem extends AnyObject> = {
  getItemId: GetItemId<TItem>;
  groupBy?: Extract<keyof TItem, string>;
  isRowClickable?: (item: TItem) => boolean;
  isRowDraggable?: (item: TItem) => boolean;
  isRowHighlighted?: (item: TItem, selectedItems?: TItem[]) => boolean;
  isRowZebraStriped?: (item: TItem, items: TItem[]) => boolean;
  items: TItem[];
  onRowClick?: (item: TItem, event: MouseEvent<HTMLElement>) => unknown;
  onRowMouseEnter?: (item: TItem) => unknown;
  onRowMouseLeave?: (item: TItem) => unknown;
  selectedItems?: TItem[];
};

type GetBackgroundColorParams<TItem extends AnyObject> = Pick<
  Params<TItem>,
  "isRowHighlighted" | "isRowZebraStriped" | "items" | "selectedItems"
> & {
  item: TItem;
  itemGroups: ItemGroup<TItem>[];
  theme: Theme;
};

const {useItemGroupsFrom} = withItemGroup;

const preventDragAndDrop = (element: HTMLTableRowElement) => {
  element.setAttribute("draggable", "false");

  element.addEventListener("drop", event => {
    event.stopPropagation();
  });
};

const getBackgroundColor = <TItem extends AnyObject>({
  isRowHighlighted,
  isRowZebraStriped,
  item,
  itemGroups,
  items,
  selectedItems,
  theme
}: GetBackgroundColorParams<TItem>) => {
  if (isRowHighlighted?.(item, selectedItems)) {
    return theme.colors.aliases.bgAccentSecondary;
  }

  const curItemGroup = withItemGroup.findWith(itemGroups, item);

  return isRowZebraStriped?.(item, curItemGroup?.items ?? items)
    ? theme.colors.aliases.bgApplication
    : theme.colors.aliases.backgroundWhite;
};

const useKaDataRowComponent = <TItem extends AnyObject>({
  getItemId,
  groupBy,
  isRowClickable,
  isRowDraggable,
  items,
  onRowClick,
  onRowMouseEnter,
  onRowMouseLeave,
  ...rest
}: Params<TItem>): ChildComponent<IDataRowProps> => {
  const nonDraggableElementsRef = useRef<NonDraggableElements>(new Map());

  const theme = useTheme();

  const itemGroups = useItemGroupsFrom(items, groupBy);

  return {
    // @ts-expect-error trRef exists but it's not typed
    elementAttributes: ({rowData, trRef}) => {
      const {item} = rowData;

      const backgroundColor = getBackgroundColor({
        item,
        itemGroups,
        items,
        theme,
        ...rest
      });

      const onRef = (element: HTMLTableRowElement | null) => {
        const nonDraggableElements = nonDraggableElementsRef.current;

        const itemId = getItemId(item);

        if (!element || !isRowDraggable || isRowDraggable(item)) {
          nonDraggableElements.delete(itemId);

          return;
        }

        const existingElement = nonDraggableElements.get(itemId);

        if (!existingElement) {
          nonDraggableElements.set(itemId, element);

          preventDragAndDrop(element);
        }
      };

      return {
        onClick: isRowClickable?.(item)
          ? event => onRowClick?.(rowData.item, event)
          : undefined,
        onMouseEnter: () => onRowMouseEnter?.(rowData.item),
        onMouseLeave: () => onRowMouseLeave?.(rowData.item),
        ref: mergeRefs(trRef, onRef),
        style: {
          backgroundColor
        }
      };
    }
  };
};

export {useKaDataRowComponent};
