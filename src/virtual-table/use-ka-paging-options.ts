import {useMemo} from "react";

import * as withPagingOptions from "./paging-options";
import type {PagingOptions} from "./paging-options";

type Params = {
  isLoading?: boolean;
  pagingOptions?: PagingOptions;
};

const useKaPagingOptions = ({isLoading, pagingOptions}: Params) =>
  useMemo(
    () =>
      isLoading || !pagingOptions
        ? undefined
        : withPagingOptions.toKaPagingOptions(pagingOptions),
    [isLoading, pagingOptions]
  );

export {useKaPagingOptions};
