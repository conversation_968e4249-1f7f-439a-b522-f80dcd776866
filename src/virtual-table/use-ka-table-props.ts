import type {AnyObject} from "@unlockre/utils-object/dist";
import {type IKaTableProps, SortingMode} from "ka-table";
import type {FormatFunc} from "ka-table/types";
import {useMemo} from "react";

import type {KaItem} from "./item";
import * as withTableColumn from "./table-column";
import {useKaItems} from "./use-ka-items";
import type {UseKaItemsParams} from "./use-ka-items";
import {useKaPagingOptions} from "./use-ka-paging-options";
import {useKaTable} from "./use-ka-table";
import type {UseKaTableParams} from "./use-ka-table";
import {useKaTableChildComponents} from "./use-ka-table-child-components";
import type {UseKaTableChildComponentsParams} from "./use-ka-table-child-components";
import {useKaTableColumns} from "./use-ka-table-columns";
import {useKaTableExtendedSort} from "./use-ka-table-extended-sort";
import type {UseKaTableExtendedSortParams} from "./use-ka-table-extended-sort";
import {useKaTableGroups} from "./use-ka-table-groups";
import type {UseKaTableGroupsParams} from "./use-ka-table-groups";
import {type UseSortedItemsParams, useSortedItems} from "./use-sorted-items";
import {useTableColumnGroups} from "./use-table-column-groups";

type ExposedUseSortedItemsParams<TItem extends AnyObject> = Pick<
  UseSortedItemsParams<TItem>,
  "onSort"
>;

type ExposedUseKaTableChildComponentsParams<TItem extends AnyObject> = Omit<
  UseKaTableChildComponentsParams<TItem>,
  "tableColumnGroups"
>;

// prettier-ignore
type Params<TItem extends AnyObject> =
  & ExposedUseKaTableChildComponentsParams<TItem>
  & ExposedUseSortedItemsParams<TItem>
  & UseKaItemsParams<TItem>
  & UseKaTableExtendedSortParams<TItem>
  & UseKaTableGroupsParams<TItem>
  & UseKaTableParams<TItem>
  & {
      expandedItems?: TItem[];
    };

// eslint-disable-next-line max-statements
const useKaTableProps = <TItem extends AnyObject>({
  expandedItems,
  getGroupDescription,
  getHeaderRowBackgroundColor,
  getItemId,
  getRowHighlightedBorderColor,
  groupBy,
  height,
  isLoading,
  isRowClickable,
  isRowDraggable,
  isRowHighlighted,
  isRowZebraStriped,
  items,
  onItemsReorder,
  onRowClick,
  onRowMouseEnter,
  onRowMouseLeave,
  onScroll,
  onSort,
  pagingOptions,
  renderEmptyElement,
  renderExpandedItem,
  selectedItems,
  setTableWrapperRef,
  tableColumns,
  variant,
  withBorder,
  withNoHeader,
  withoutRowBorders
}: Params<TItem>): IKaTableProps<KaItem<TItem>> => {
  const {kaTableColumns, updateKaTableColumnSortDirection} =
    useKaTableColumns(tableColumns);

  const kaTable = useKaTable({
    getItemId,
    items,
    onItemsReorder,
    onUpdateSortDirection: updateKaTableColumnSortDirection
  });

  const kaItems = useKaItems({
    getItemId,
    items
  });

  const tableColumnGroups = useTableColumnGroups(tableColumns);

  const kaTableExtendedSort = useKaTableExtendedSort({
    tableColumns
  });

  const sortedItems = useSortedItems({
    items,
    kaItems,
    kaTableColumns,
    kaTableExtendedSort,
    onSort,
    tableColumns
  });

  const kaTableChildComponents = useKaTableChildComponents({
    getGroupDescription,
    getHeaderRowBackgroundColor,
    getItemId,
    getRowHighlightedBorderColor,
    groupBy,
    height,
    isLoading,
    isRowClickable,
    isRowDraggable,
    isRowHighlighted,
    isRowZebraStriped,
    items: sortedItems,
    onRowClick,
    onRowMouseEnter,
    onRowMouseLeave,
    onScroll,
    pagingOptions,
    renderEmptyElement,
    renderExpandedItem,
    selectedItems,
    setTableWrapperRef,
    tableColumnGroups,
    tableColumns,
    variant,
    withBorder,
    withoutRowBorders,
    withNoHeader
  });

  const kaTableGroups = useKaTableGroups({groupBy});

  const kaTableFormat: FormatFunc = ({column: kaTableColumn, value}) => {
    const tableColumn = withTableColumn.find(
      tableColumns,
      withTableColumn.getKeyFrom(kaTableColumn)
    );

    return tableColumn?.format ? tableColumn.format(value) : String(value);
  };

  const data = useMemo(() => (isLoading ? [] : kaItems), [kaItems, isLoading]);

  const loading = useMemo(() => ({enabled: isLoading}), [isLoading]);

  const kaPagingOptions = useKaPagingOptions({isLoading, pagingOptions});

  const expandedRowIds = useMemo(
    () => expandedItems?.map(getItemId),
    [expandedItems, getItemId]
  );

  const selectedRowIds = useMemo(
    () => selectedItems?.map(getItemId),
    [selectedItems, getItemId]
  );

  const virtualScrolling = useMemo(
    () => (height === undefined ? undefined : {enabled: true}),
    [height]
  );

  return {
    columns: kaTableColumns,
    controlledPropsKeys: [
      "columns",
      "data",
      "detailsRows",
      "format",
      "extendedSort",
      "groups",
      "loading",
      "paging",
      "selectedRows"
    ],
    childComponents: kaTableChildComponents,
    data,
    extendedSort: kaTableExtendedSort,
    detailsRows: expandedRowIds,
    format: kaTableFormat,
    groups: kaTableGroups,
    loading,
    paging: kaPagingOptions,
    rowKeyField: "id",
    rowReordering: isRowDraggable !== undefined,
    selectedRows: selectedRowIds,
    sortingMode: SortingMode.SingleTripleState,
    table: kaTable,
    virtualScrolling
  };
};

export {useKaTableProps};
export type {Params as UseKaTablePropsParams};
