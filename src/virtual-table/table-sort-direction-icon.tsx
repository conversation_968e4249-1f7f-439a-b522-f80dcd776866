import {ArrowDown, ArrowUp} from "@phosphor-icons/react";

import {IconWithColor} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";

import {tableSortDirections} from "./table-sort-direction";
import type {TableSortDirection} from "./table-sort-direction";

type Props = {
  size: number;
  sortDirection?: TableSortDirection;
};

const TableSortDirectionIcon = ({size, sortDirection}: Props) => (
  <IconWithColor
    {...{size}}
    getColor={getColorByAlias("iconAccent")}
    icon={sortDirection === tableSortDirections.ascendant ? ArrowUp : ArrowDown}
    weight="regular"
  />
);

export {TableSortDirectionIcon};
