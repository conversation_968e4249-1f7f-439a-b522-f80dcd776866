import type {AnyObject} from "@unlockre/utils-object/dist";

import type {GetItemId} from "../item";
import {specialTableColumnKeys} from "../special-table-column-key";
import type {TableColumn} from "../table-column";

import {ToggleTableRowCell} from "./toggle-table-row-cell";

const create = <TItem extends AnyObject>(
  getItemId: GetItemId<TItem>,
  expandedItems?: TItem[],
  onExpandedItemsChange?: (expandedItems: TItem[]) => unknown
): TableColumn<TItem> => ({
  key: specialTableColumnKeys.toggleTableRow,
  renderItemCell: params => (
    <ToggleTableRowCell
      {...{expandedItems, getItemId}}
      {...params}
      onChange={onExpandedItemsChange}
    />
  ),
  width: 50
});

export {create};
