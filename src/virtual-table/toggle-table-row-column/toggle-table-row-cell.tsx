import type {AnyObject} from "@unlockre/utils-object/dist";

import {ItemCell} from "../item-cell";
import type {ItemCellProps} from "../item-cell";

import {ToggleTableRowButton} from "./toggle-table-row-button";
import type {ToggleTableRowButtonProps} from "./toggle-table-row-button";

type ExposedItemCellProps<TItem extends AnyObject> = Omit<
  ItemCellProps<TItem>,
  "children" | "className"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedItemCellProps<TItem>
  & ToggleTableRowButtonProps<TItem>;

const ToggleTableRowCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <ItemCell {...{tableColumn, variant}}>
    <ToggleTableRowButton {...rest} />
  </ItemCell>
);

export {ToggleTableRowCell};
