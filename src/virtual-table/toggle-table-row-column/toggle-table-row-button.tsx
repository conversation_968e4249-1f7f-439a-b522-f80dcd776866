import * as withArray from "@unlockre/utils-array/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";
import styled from "styled-components";

import {chevronDirections} from "@/icons/chevron-icon";
import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import type {GetItemId} from "../item";
import {TableArrowIcon} from "../table-arrow-icon";

type Props<TItem extends AnyObject> = {
  expandedItems?: TItem[];
  getItemId: GetItemId<TItem>;
  item: TItem;
  onChange?: (expandedItems: TItem[]) => unknown;
};

const Container = styled(UnstyledButton)`
  align-items: center;
  border-radius: 50%;
  display: flex;
  justify-content: center;

  &:hover {
    background: ${getColorByAlias("backgroundTertiary")};
  }
`;

const ToggleTableRowButton = <TItem extends AnyObject>({
  expandedItems,
  getItemId,
  item,
  onChange
}: Props<TItem>) => {
  const isItemExpanded = expandedItems?.some(
    expandedItem => getItemId(expandedItem) === getItemId(item)
  );

  const handleClick = () => {
    if (!expandedItems) {
      return onChange?.([item]);
    }

    const allExpandedItems = isItemExpanded
      ? withArray.remove(
          expandedItems,
          expandedItems.findIndex(
            expandedItem => getItemId(expandedItem) === getItemId(item)
          ),
          1
        )
      : [...expandedItems, item];

    onChange?.(allExpandedItems);
  };

  return (
    <Container onClick={handleClick}>
      <TableArrowIcon
        direction={
          isItemExpanded ? chevronDirections.up : chevronDirections.down
        }
        height={20}
      />
    </Container>
  );
};

export {ToggleTableRowButton};
export type {Props as ToggleTableRowButtonProps};
