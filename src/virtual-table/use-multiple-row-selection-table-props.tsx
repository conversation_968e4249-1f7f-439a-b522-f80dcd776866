import * as withArray from "@unlockre/utils-array/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";
import {useWindowEvent} from "@unlockre/utils-react/dist";
import {isDefined} from "@unlockre/utils-validation/dist";
import {useCallback, useEffect, useMemo, useState} from "react";
import type React from "react";

import * as withItem from "./item";

type GetItemId<TItem extends AnyObject> = (item: TItem) => string;
type IsRowSelectable<TItem extends AnyObject> = (item: TItem) => boolean;

type Params<TItem extends AnyObject> = {
  getItemId: GetItemId<TItem>;
  isRowSelectable?: IsRowSelectable<TItem>;
  items: TItem[];
};

// eslint-disable-next-line max-statements
const useMultipleRowSelectionTableProps = <TItem extends AnyObject>({
  getItemId,
  isRowSelectable,
  items
}: Params<TItem>) => {
  const [pivotIndex, setPivotIndex] = useState<number>();
  const [selectedRanges, setSelectedRanges] = useState<TItem[][]>([]);
  const [sortedItems, setSortedItems] = useState<TItem[]>(items);

  useWindowEvent("mousedown", (event: MouseEvent) => {
    if (event.shiftKey) {
      event.preventDefault();
    }
  });

  useWindowEvent("keydown", (event: KeyboardEvent) => {
    if (event.key === "Escape") {
      setSelectedRanges([]);
      event.preventDefault();
    }
  });

  useEffect(() => {
    setSortedItems(items);
  }, [items]);

  const selectedItems = useMemo(() => selectedRanges.flat(), [selectedRanges]);

  const clearSelection = useCallback(() => setSelectedRanges([]), []);

  const isRowSelected = useCallback(
    (item: TItem) =>
      withItem.getIndex(selectedItems, getItemId, getItemId(item)) !== -1,
    [getItemId, selectedItems]
  );

  const updatedRanges = useCallback(
    ({getItemId, item}: {getItemId: GetItemId<TItem>; item: TItem}) => {
      setSelectedRanges(previousSelectedRanges => {
        const rangeIndex = previousSelectedRanges.findIndex(
          range => withItem.getIndex(range, getItemId, getItemId(item)) !== -1
        );

        if (rangeIndex === -1) {
          return [...previousSelectedRanges, [item]];
        }

        const range = previousSelectedRanges[rangeIndex];
        const itemIndex = withItem.getIndex(range, getItemId, getItemId(item));

        return [
          ...withArray.remove(previousSelectedRanges, rangeIndex, 1),
          range.slice(0, itemIndex),
          range.slice(itemIndex + 1)
        ];
      });
    },
    []
  );

  const addRange = useCallback(
    ({
      getItemId,
      isRowSelectable,
      items,
      pivotIndex,
      selectedItemIndex
    }: {
      getItemId: GetItemId<TItem>;
      isRowSelectable?: IsRowSelectable<TItem>;
      items: TItem[];
      pivotIndex: number;
      selectedItemIndex: number;
    }) => {
      const firstElementIndex = Math.min(pivotIndex, selectedItemIndex);
      const lastElementIndex = Math.max(pivotIndex, selectedItemIndex);

      const newSelectedRange = items
        .slice(firstElementIndex, lastElementIndex + 1)
        .filter(item => !isRowSelectable || isRowSelectable(item));

      setSelectedRanges(previousSelectedRanges => [
        ...previousSelectedRanges.filter(range =>
          range.every(
            item =>
              withItem.getIndex(
                newSelectedRange,
                getItemId,
                getItemId(item)
              ) === -1
          )
        ),
        newSelectedRange
      ]);
    },
    []
  );

  const onRowClick = useCallback(
    // eslint-disable-next-line max-statements, complexity
    (item: TItem, event: React.MouseEvent<HTMLElement>) => {
      if (isRowSelectable && !isRowSelectable(item)) {
        return;
      }

      const isCtrlKeyPressed = event.ctrlKey || event.metaKey;

      if (isCtrlKeyPressed) {
        updatedRanges({getItemId, item});
      }

      const selectedItemIndex = withItem.getIndex(
        sortedItems,
        getItemId,
        getItemId(item)
      );

      if (selectedItemIndex === -1) {
        return;
      }

      if (!isDefined(pivotIndex) || !event.shiftKey || isCtrlKeyPressed) {
        setPivotIndex(selectedItemIndex);

        if (!isCtrlKeyPressed) {
          setSelectedRanges([[item]]);
        }

        return;
      }

      if (event.shiftKey && !isCtrlKeyPressed) {
        addRange({
          getItemId,
          isRowSelectable,
          items: sortedItems,
          pivotIndex,
          selectedItemIndex
        });
      }
    },
    [
      addRange,
      getItemId,
      isRowSelectable,
      pivotIndex,
      sortedItems,
      updatedRanges
    ]
  );

  const onSelectedItemsChange = useCallback(
    ({event, item}: {event: React.MouseEvent<HTMLElement>; item: TItem}) => {
      onRowClick(item, {
        ...event,
        ctrlKey: true
      });
    },
    [onRowClick]
  );

  const onSort = useCallback(
    ({sortedItems: newSortedItems}: {sortedItems: TItem[]}) => {
      const hasOrderChanged = items.some(
        (item, index) => getItemId(item) !== getItemId(newSortedItems[index])
      );

      if (hasOrderChanged) {
        newSortedItems.forEach((newItem, index) => {
          const currentIndex = items.indexOf(newItem);

          if (currentIndex !== index) {
            items.splice(currentIndex, 1);
            items.splice(index, 0, newItem);
          }
        });

        setSortedItems(items);
      }
    },
    [getItemId, items]
  );

  return {
    clearSelection,
    isRowSelected,
    onRowClick,
    onSelectedItemsChange,
    onSort,
    selectedItems
  };
};

export {useMultipleRowSelectionTableProps};
