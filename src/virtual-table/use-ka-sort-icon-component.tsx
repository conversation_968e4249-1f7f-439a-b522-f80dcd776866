import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {ISortIconProps} from "ka-table/props";

import * as withTableSortDirection from "./table-sort-direction";
import {TableSortDirectionIcon} from "./table-sort-direction-icon";

const useKaSortIconComponent = (): ChildComponent<ISortIconProps> => ({
  content: ({column: kaTableColumn}) => (
    <TableSortDirectionIcon
      size={16}
      sortDirection={withTableSortDirection.fromKaSortDirection(
        kaTableColumn.sortDirection
      )}
    />
  ),
  elementAttributes: () => ({
    style: {
      height: 16,
      paddingRight: 16
    }
  })
});

export {useKaSortIconComponent};
