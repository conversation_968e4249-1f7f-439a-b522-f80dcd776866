import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IPagingProps} from "ka-table/props";
import {useMemo} from "react";

import type {PagingOptions} from "./paging-options";
import {TablePagination} from "./table-pagination";

type Params = {
  pagingOptions?: PagingOptions;
};

const createPagingPagesContent =
  ({pagingOptions}: Params) =>
  ({pagesCount}: IPagingProps) => {
    if (!pagingOptions?.isEnabled) {
      return null;
    }

    return (
      <TablePagination
        currentPage={pagingOptions.pageNumber}
        onPageChange={pagingOptions.onPageChange}
        pageCount={Number(pagesCount)}
      />
    );
  };

const useKaPagingPagesComponent = ({
  pagingOptions
}: Params): ChildComponent<IPagingProps> => {
  const PagingPagesContent = useMemo(
    () => createPagingPagesContent({pagingOptions}),
    [pagingOptions]
  );

  return {
    content: PagingPagesContent,
    elementAttributes: () => ({
      style: {
        bottom: 0,
        display: "flex",
        gap: 12,
        listStyle: "none",
        margin: 0,
        padding: 0,
        position: "absolute",
        right: 0
      }
    })
  };
};

export {useKaPagingPagesComponent};
