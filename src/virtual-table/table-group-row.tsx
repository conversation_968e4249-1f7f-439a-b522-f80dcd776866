import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";

type Props = {
  colSpan: number;
  description: string;
};

const DescriptionContainer = styled.span`
  ${getTypography("body", "xs", 600)}

  color: ${getColorByAlias("textPrimary")};
  left: 16px;
  position: sticky;
`;

const Container = styled.td`
  height: 30px;
  position: relative;
`;

const TableGroupRow = ({colSpan, description}: Props) => (
  <Container {...{colSpan}}>
    <DescriptionContainer>{description}</DescriptionContainer>
  </Container>
);

export {TableGroupRow};
