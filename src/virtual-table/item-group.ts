import * as withArray from "@unlockre/utils-array/dist";
import type {AnyObject, ObjectEntryOf} from "@unlockre/utils-object/dist";
import {useMemo} from "react";

type ItemGroup<TItem extends AnyObject> = {
  itemEntry: ObjectEntryOf<TItem>;
  items: TItem[];
};

const create = <TItem extends AnyObject>(
  itemEntry: ObjectEntryOf<TItem>
): ItemGroup<TItem> => ({
  itemEntry,
  items: []
});

const append = <TItem extends AnyObject>(
  itemGroup: ItemGroup<TItem>,
  item: TItem
): ItemGroup<TItem> => ({
  ...itemGroup,
  items: [...itemGroup.items, item]
});

const includes = <TItem extends AnyObject>(
  itemGroup: ItemGroup<TItem>,
  item: TItem
) => itemGroup.items.includes(item);

const findWith = <TItem extends AnyObject>(
  itemGroups: ItemGroup<TItem>[],
  item: TItem
) => itemGroups.find(itemGroup => includes(itemGroup, item));

const canInclude = <TItem extends AnyObject>(
  itemGroup: ItemGroup<TItem>,
  item: TItem
) => item[itemGroup.itemEntry[0]] === itemGroup.itemEntry[1];

const getAllFrom = <TItem extends AnyObject>(
  items: TItem[],
  itemKey: Extract<keyof TItem, string>
) =>
  items.reduce<ItemGroup<TItem>[]>((itemGroups, item) => {
    const itemGroupIndex = itemGroups.findIndex(itemGroup =>
      canInclude(itemGroup, item)
    );

    const itemGroup = itemGroups[itemGroupIndex];

    return itemGroup
      ? withArray.update(itemGroups, itemGroupIndex, append(itemGroup, item))
      : [...itemGroups, append(create([itemKey, item[itemKey]]), item)];
  }, []);

const useItemGroupsFrom = <TItem extends AnyObject>(
  items: TItem[],
  itemKey?: Extract<keyof TItem, string>
) =>
  useMemo(
    () => (itemKey === undefined ? [] : getAllFrom(items, itemKey)),
    [items, itemKey]
  );

export {findWith, includes, useItemGroupsFrom};
export type {ItemGroup};
