import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {IGroupRowProps} from "ka-table/props";
import {useMemo} from "react";
import {useTheme} from "styled-components";

import {TableGroupRow} from "./table-group-row";

type Params = {
  getGroupDescription?: (groupValue: string) => string;
};

const useKaGroupRowComponent = ({
  getGroupDescription
}: Params = {}): ChildComponent<IGroupRowProps> => {
  const theme = useTheme();

  const GroupRowContent = useMemo(
    () =>
      ({contentColSpan, text}: IGroupRowProps) => (
        <TableGroupRow
          colSpan={contentColSpan - 1}
          description={getGroupDescription?.(text) ?? text}
        />
      ),
    [getGroupDescription]
  );

  return {
    content: GroupRowContent,
    elementAttributes: () => ({
      style: {
        backgroundColor: theme.colors.palette.gray[100]
      }
    })
  };
};

export {useKaGroupRowComponent};
