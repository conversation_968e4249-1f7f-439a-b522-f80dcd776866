import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ComponentProps, ReactNode} from "react";
import styled from "styled-components";
import type {CSSProperties} from "styled-components";

import type {CellHorizontalAlignType} from "./cell-horizontal-align-type";
import {TableCell} from "./table-cell";
import type {TableColumn} from "./table-column";
import type {VirtualTableVariant} from "./virtual-table-variant";

type TableCellProps = ComponentProps<typeof TableCell>;

type Props<TItem extends AnyObject> = {
  children?: ReactNode;
  className?: string;
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

type StyledProps = {
  $horizontalAlign: CellHorizontalAlignType;
  $variant: VirtualTableVariant;
};

const getFixedElement: TableCellProps["getFixedElement"] = tableCellElement =>
  (tableCellElement?.offsetParent as HTMLElement) ?? null;

const variants = {
  comfortable: {
    padding: "24px 16px"
  },
  dense: {
    padding: "16px"
  }
} as const satisfies Record<VirtualTableVariant, CSSProperties>;

const StyledTableCell = styled(TableCell)<StyledProps>`
  ${props => variants[props.$variant]}

  box-sizing: border-box;
  display: flex;
  justify-content: ${props => props.$horizontalAlign};
  text-align: ${props => props.$horizontalAlign};
`;

const defaultHorizontalAlignType: CellHorizontalAlignType = "start";

const BodyCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <StyledTableCell
    {...{getFixedElement}}
    {...rest}
    $horizontalAlign={tableColumn.horizontalAlign ?? defaultHorizontalAlignType}
    $variant={variant}
    isFixed={tableColumn.isFixed}
  />
);

export {BodyCell};
export type {Props as BodyCellProps};
