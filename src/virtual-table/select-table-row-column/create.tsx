import type {AnyObject} from "@unlockre/utils-object/dist";
import type {MouseEvent} from "react";

import type {GetItemId} from "../item";
import {specialTableColumnKeys} from "../special-table-column-key";
import type {TableColumn} from "../table-column";

import {SelectTableRowCell} from "./select-table-row-cell";

const create = <TItem extends AnyObject>(
  columns: TableColumn<TItem>[],
  getItemId: GetItemId<TItem>,
  isRowSelectable: (item: TItem) => boolean,
  onSelectedItemsChange?: (params: {
    event: MouseEvent<HTMLButtonElement>;
    item: TItem;
    selectedItems: TItem[];
  }) => unknown,
  selectedItems?: TItem[]
): TableColumn<TItem> => ({
  key: specialTableColumnKeys.selectTableRow,
  isFixed: columns.some(column => column.isFixed),
  renderItemCell: params => (
    <SelectTableRowCell
      {...{getItemId, isRowSelectable, selectedItems}}
      {...params}
      onChange={onSelectedItemsChange}
    />
  ),
  width: 50
});

export {create};
