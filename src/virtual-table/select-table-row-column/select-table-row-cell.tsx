import type {AnyObject} from "@unlockre/utils-object/dist";

import {ItemCell} from "../item-cell";
import type {ItemCellProps} from "../item-cell";

import {SelectTableRowCheckbox} from "./select-table-row-checkbox";
import type {SelectTableRowCheckboxProps} from "./select-table-row-checkbox";

type ExposedItemCellProps<TItem extends AnyObject> = Omit<
  ItemCellProps<TItem>,
  "children" | "className"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & ExposedItemCellProps<TItem>
  & SelectTableRowCheckboxProps<TItem> & {
    isRowSelectable: (item: TItem) => boolean;
  };

const SelectTableRowCell = <TItem extends AnyObject>({
  isRowSelectable,
  item,
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <ItemCell {...{tableColumn, variant}}>
    {isRowSelectable(item) && <SelectTableRowCheckbox {...rest} {...{item}} />}
  </ItemCell>
);

export {SelectTableRowCell};
