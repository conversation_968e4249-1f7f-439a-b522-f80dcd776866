import * as withArray from "@unlockre/utils-array/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";
import type {MouseEvent} from "react";

import Checkbox from "@/checkbox";
import {getColorByAlias} from "@/theme-provider/theme";

import type {GetItemId} from "../item";

type Props<TItem extends AnyObject> = {
  getItemId: GetItemId<TItem>;
  item: TItem;
  onChange?: (params: {
    event: MouseEvent<HTMLButtonElement>;
    item: TItem;
    selectedItems: TItem[];
  }) => unknown;
  selectedItems?: TItem[];
};

const SelectTableRowCheckbox = <TItem extends AnyObject>({
  getItemId,
  item,
  onChange,
  selectedItems
}: Props<TItem>) => {
  const isItemSelected = selectedItems?.some(
    selectedItem => getItemId(selectedItem) === getItemId(item)
  );

  const handleChange = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();

    if (!selectedItems) {
      return onChange?.({event, item, selectedItems: [item]});
    }

    const allSelectedItems = isItemSelected
      ? withArray.remove(
          selectedItems,
          selectedItems.findIndex(
            selectedItem => getItemId(selectedItem) === getItemId(item)
          ),
          1
        )
      : [...selectedItems, item];

    onChange?.({event, item, selectedItems: allSelectedItems});
  };

  return (
    <Checkbox
      getColor={
        isItemSelected
          ? getColorByAlias("bgAccent")
          : getColorByAlias("borderStrong")
      }
      isChecked={isItemSelected}
      onChange={handleChange}
      size="medium"
    />
  );
};

export {SelectTableRowCheckbox};
export type {Props as SelectTableRowCheckboxProps};
