import type {AnyObject} from "@unlockre/utils-object/dist";
import {SortDirection} from "ka-table";
import type {Column} from "ka-table/models";

import type {KaItem} from "./item";

type KaTableColumn<TItem extends AnyObject = any> = Column<KaItem<TItem>>;

const setSortDirection = (
  kaTableColumn: KaTableColumn,
  sortDirection: SortDirection | undefined
) => ({
  ...kaTableColumn,
  sortDirection
});

const getNextSortDirection = (sortDirection?: SortDirection) => {
  if (!sortDirection) {
    return SortDirection.Ascend;
  }

  return sortDirection === SortDirection.Ascend
    ? SortDirection.Descend
    : undefined;
};

const setNextSortDirection = (kaTableColumn: KaTableColumn) =>
  setSortDirection(
    kaTableColumn,
    getNextSortDirection(kaTableColumn.sortDirection)
  );

export {setNextSortDirection, setSortDirection};
export type {KaTableColumn};
