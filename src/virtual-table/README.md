# Virtual Table

## About

This component provides a virtualized table with the following main features:

- sorting
- pagination
- expandable rows
- selectable rows
- fixed columns
- grouped columns


## API

### Important

The following functions should be memoized (using useCallback)

- getItemId
- onExpandedItemsChange
- onSelectedItemsChange

## Examples

### Example Basic

```tsx
import type {TableColumn} from "@unlockre/components-library/dist/virtual-table";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {
  ItemCell,
  ItemText,
  VirtualTable,
} from "@unlockre/components-library/dist/virtual-table";
import type {VirtualTableVariant} from "@unlockre/components-library/dist/virtual-table";


type Movie = {
  id: number;
  title: string;
  year: string;
};

type MovieTableColumn = TableColumn<Movie>;

type TitleAndYearCellProps = {
  movie: Movie;
  tableColumn: MovieTableColumn;
}

const tableVariant: VirtualTableVariant = "dense";

const TitleAndYearCell = ({movie, tableColumn}: TitleAndYearCellProps) => (
  <ItemCell {...{tableColumn}} variant={tableVariant}>
    <ItemText>{movie.title + ": " + movie.year}</ItemText>
  </ItemCell>
);

const columns: MovieTableColumn [] = [
  {
    title: "Id",
    key: "id",
    width: 100,
    isSortable: true
  },
  {
    title: "Title",
    width: 100,
    key: "title",
    isSortable: true
  },
  {
    title: "Year",
    width: 200,
    key: "year",
    isSortable: true
  },
  {
    title: "Title and year",
    width: 500,
    key: "titleAndYear",
    renderItemCell: (movie, tableColumn) => (
      <TitleAndYearCell {...{movie, tableColumn}} />
    )
  }
];

const items: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Pulp Fiction",
    year: "1994"
  }
];

const getItemId = (item: Movie) => item.id;

const ExampleBasic = () => (
  <ThemeProvider>
    <VirtualTable {...{columns, getItemId, items}} height={500}  />
  </ThemeProvider>
);

export {ExampleBasic};
```

### Example with pagination

```tsx
import {useState} from "react";

import type {TableColumn} from "@unlockre/components-library/dist/virtual-table";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {VirtualTable} from "@unlockre/components-library/dist/virtual-table";


type Movie = {
  id: number;
  title: string;
  year: string;
};


const columns: TableColumn<Movie>[] = [
  {
    title: "Id",
    key: "id",
    width: 100,
    isSortable: true
  },
  {
    title: "Title",
    width: 100,
    key: "title",
    isSortable: true
  },
  {
    title: "Year",
    width: 200,
    key: "year",
    isSortable: true
  }
];

const items: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 4,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 5,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 6,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 7,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 8,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 9,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 10,
    title: "Ghostbusters",
    year: "1984"
  }
];

const getItemId = (item: Movie) => item.id;

const ExampleWithPagination = () => {
  const [pageNumber, setPageNumber] = useState(1);

  return (
    <ThemeProvider>
      <VirtualTable
        {...{columns, getItemId, items}}
        pagingOptions={
          isEnabled: true,
          onPageChange: setPageNumber,
          pageNumber,
          pageSize: 5,
          totalRows: items.length
        }
      />
    </ThemeProvider>
  )
};

export {ExampleWithPagination};
```

### Example with expandable rows

```tsx
import {useState} from "react";

import type {TableColumn} from "@unlockre/components-library/dist/virtual-table";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {VirtualTable} from "@unlockre/components-library/dist/virtual-table";

type Movie = {
  id: number;
  title: string;
  year: string;
};

const renderMovieDetails = (movie: Movie) => (
  <div>
    <h3>{movie.title}</h3>
    <p>year: {movie.year}</p>
  </div>
);


const columns: TableColumn<Movie>[] = [
  {
    title: "Id",
    key: "id",
    width: 100,
    isSortable: true
  },
  {
    title: "Title",
    width: 100,
    key: "title",
    isSortable: true
  },
  {
    title: "Year",
    width: 200,
    key: "year",
    isSortable: true
  }
];

const items: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Pulp Fiction",
    year: "1994"
  }
];

const getItemId = (item: Movie) => item.id;

const ExampleWithExpandableRows = () => {
  const [expandedItems, setExpandedItems] = useState([]);

  return (
    <ThemeProvider>
      <VirtualTable
        {...{columns, expandedItems, getItemId, items}}
        onExpandedItemsChange={setExpandedItems}
        renderExpandedItem={renderMovieDetails}
      />
    </ThemeProvider>
  )
};

export {ExampleWithExpandableRows};
```

### Example with fixed columns

```tsx
import type {TableColumn} from "@unlockre/components-library/dist/virtual-table";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {VirtualTable} from "@unlockre/components-library/dist/virtual-table";

type Movie = {
  id: number;
  title: string;
  year: string;
};

const columns: TableColumn<Movie>[] = [
  {
    title: "Id",
    key: "id",
    width: 100,
    isFixed: true,
    isSortable: true
  },
  {
    title: "Title",
    width: 100,
    key: "title",
    isFixed: true,
    isSortable: true
  },
  {
    title: "Year",
    width: 200,
    key: "year",
    isSortable: true
  }
];

const items: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    year: "1988"
  },
  {
    id: 2,
    title: "Ghostbusters",
    year: "1984"
  },
  {
    id: 3,
    title: "Pulp Fiction",
    year: "1994"
  }
];

const getItemId = (item: Movie) => item.id;

const ExampleWithFixedColumns = () => (
  <ThemeProvider>
    <VirtualTable {...{columns, getItemId, items}} />
  </ThemeProvider>
);

export {ExampleWithFixedColumns};
```

### Example with grouped columns

```tsx
import type {TableColumn} from "@unlockre/components-library/dist/virtual-table";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {VirtualTable} from "@unlockre/components-library/dist/virtual-table";

type Movie = {
  id: number;
  title: string;
  releaseDate: string;
};

const columns: TableColumn<Movie>[] = [
  {
    title: "Id",
    key: "id",
    width: 100,
  },
  {
    title: "Title",
    key: "title",
    width: 100,
  },
  {
    title: "Release Year",
    key: "releaseYear",
    groupKey: "releaseDate",
    width: 200,
  },
  {
    title: "Release Month",
    key: "releaseMonth",
    groupKey: "releaseDate",
    width: 200,
  },
];

const items: Movie[] = [
  {
    id: 1,
    title: "Beetlejuice",
    releaseDate: "1988-03-30"
  },
  {
    id: 2,
    title: "Ghostbusters",
    releaseDate: "1984-12-13"
  },
  {
    id: 3,
    title: "Pulp Fiction",
    releaseDate: "1994-11-25"
  }
];

const getItemId = (item: Movie) => item.id;

const ExampleWithGroupedColumns = () => (
  <ThemeProvider>
    <VirtualTable {...{columns, getItemId, items}} />
  </ThemeProvider>
);

export {ExampleWithGroupedColumns};
```