import type {AnyObject} from "@unlockre/utils-object/dist";

import {HeaderCell} from "./header-cell";
import {HeaderText} from "./header-text";
import {LegacyHeaderCell} from "./legacy-header-cell";
import type {TableColumn} from "./table-column";
import {virtualTableClassNames} from "./virtual-table-class-names";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Props<TItem extends AnyObject> = {
  className?: string;
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

const DefaultHeaderCell = <TItem extends AnyObject>({
  className,
  tableColumn,
  ...rest
}: Props<TItem>) => {
  const allClassNames =
    virtualTableClassNames.headerCell + (className ? " " + className : "");

  if (tableColumn.renderTableHeaderCell) {
    return (
      <LegacyHeaderCell {...{tableColumn}} {...rest} className={allClassNames}>
        {tableColumn.renderTableHeaderCell(tableColumn)}
      </LegacyHeaderCell>
    );
  }

  return (
    <HeaderCell {...{tableColumn}} {...rest} className={allClassNames}>
      <HeaderText>{tableColumn.title}</HeaderText>
    </HeaderCell>
  );
};

export {DefaultHeaderCell};
