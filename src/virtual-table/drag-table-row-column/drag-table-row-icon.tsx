import {DotsSixVertical} from "@phosphor-icons/react";
import type {AnyObject} from "@unlockre/utils-object/dist";

import {IconWithColor} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";

import {virtualTableClassNames} from "../virtual-table-class-names";

type Props<TItem extends AnyObject> = {
  isRowDraggable: (item: TItem) => boolean;
  item: TItem;
};

const DragTableRowIcon = <TItem extends AnyObject>({
  isRowDraggable,
  item
}: Props<TItem>) => {
  if (!isRowDraggable(item)) {
    return null;
  }

  return (
    <IconWithColor
      className={virtualTableClassNames.dragTableRowIcon}
      getColor={getColorByAlias("icon")}
      icon={DotsSixVertical}
      size={16}
      weight="bold"
    />
  );
};

export {DragTableRowIcon};
export type {Props as DragTableRowIconProps};
