import type {AnyObject} from "@unlockre/utils-object/dist";

import {specialTableColumnKeys} from "../special-table-column-key";
import type {TableColumn} from "../table-column";

import {DragTableRowCell} from "./drag-table-row-cell";

const create = <TItem extends AnyObject>(
  columns: TableColumn<TItem>[],
  isRowDraggable: (item: TItem) => boolean
): TableColumn<TItem> => ({
  key: specialTableColumnKeys.dragTableRow,
  isFixed: columns.some(column => column.isFixed),
  renderItemCell: params => (
    <DragTableRowCell {...{isRowDraggable}} {...params} />
  ),
  width: 48
});

export {create};
