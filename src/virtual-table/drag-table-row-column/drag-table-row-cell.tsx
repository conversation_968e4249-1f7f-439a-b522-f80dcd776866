import type {AnyObject} from "@unlockre/utils-object/dist";

import {ItemCell} from "../item-cell";
import type {ItemCellProps} from "../item-cell";

import {DragTableRowIcon} from "./drag-table-row-icon";
import type {DragTableRowIconProps} from "./drag-table-row-icon";

type ExposedItemCellProps<TItem extends AnyObject> = Omit<
  ItemCellProps<TItem>,
  "children" | "className"
>;

// prettier-ignore
type Props<TItem extends AnyObject> =
  & DragTableRowIconProps<TItem>
  & ExposedItemCellProps<TItem>;

const DragTableRowCell = <TItem extends AnyObject>({
  tableColumn,
  variant,
  ...rest
}: Props<TItem>) => (
  <ItemCell {...{tableColumn, variant}}>
    <DragTableRowIcon {...rest} />
  </ItemCell>
);

export {DragTableRowCell};
