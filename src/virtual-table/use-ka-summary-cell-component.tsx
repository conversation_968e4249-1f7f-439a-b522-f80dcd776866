import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {ISummaryCellProps} from "ka-table/props";
import {useMemo} from "react";
import {useTheme} from "styled-components";

import {DefaultItemsSummaryCell} from "./default-items-summary-cell";
import {ItemsSummaryCell} from "./items-summary-cell";
import * as withKaBodyCell from "./ka-body-cell";
import * as withKaCell from "./ka-cell";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import * as withTableColumnGroup from "./table-column-group";
import type {TableColumnGroup} from "./table-column-group";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Params<TItem extends AnyObject> = {
  hasScrolledHorizontally?: boolean;
  isLoading?: boolean;
  items: TItem[];
  tableColumnGroups: TableColumnGroup<TItem>[];
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
};

type CreateSummaryCellContentParams<TItem extends AnyObject> = Pick<
  Params<TItem>,
  "items" | "tableColumns" | "variant"
>;

const createSummaryCellContent =
  <TItem extends AnyObject>({
    items,
    tableColumns,
    variant
  }: CreateSummaryCellContentParams<TItem>) =>
  ({column}: ISummaryCellProps) => {
    const tableColumn = withTableColumn.find(
      tableColumns,
      withTableColumn.getKeyFrom(column)
    );

    if (!tableColumn) {
      return null;
    }

    if (withTableColumn.isSpecial(tableColumn)) {
      return <ItemsSummaryCell {...{items, tableColumn, variant}} />;
    }

    if (tableColumn.renderItemsSummaryCell) {
      return tableColumn.renderItemsSummaryCell({items, tableColumn, variant});
    }

    return <DefaultItemsSummaryCell {...{items, tableColumn, variant}} />;
  };

const useKaSummaryCellComponent = <TItem extends AnyObject>({
  hasScrolledHorizontally,
  isLoading,
  items,
  tableColumnGroups,
  tableColumns,
  variant
}: Params<TItem>): ChildComponent<ISummaryCellProps> => {
  const theme = useTheme();

  const SummaryCellContent = useMemo(
    () => createSummaryCellContent({items, tableColumns, variant}),
    [items, tableColumns, variant]
  );

  const tableHasSummaryRow = tableColumns.some(
    tableColumn => tableColumn.getSummary
  );

  const tableColumnGroupRecord = useMemo(
    () => withTableColumnGroup.getTableColumnGroupRecord(tableColumnGroups),
    [tableColumnGroups]
  );

  return {
    content: !isLoading && tableHasSummaryRow ? SummaryCellContent : undefined,
    elementAttributes: ({column}) => {
      const tableColumn = withTableColumn.find(
        tableColumns,
        withTableColumn.getKeyFrom(column)
      );

      if (!tableColumn) {
        return {style: {display: "none"}};
      }

      return {
        className: withKaCell.getFixedCellClassName({
          hasScrolledHorizontally,
          kaTableColumn: column,
          tableColumns
        }),
        style: withKaBodyCell.getStyle({
          tableColumn,
          tableColumnGroupRecord,
          tableColumnGroups,
          tableColumns,
          theme
        })
      };
    }
  };
};

export {useKaSummaryCellComponent};
