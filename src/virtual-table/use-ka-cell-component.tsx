import type {AnyObject} from "@unlockre/utils-object/dist";
import type {ChildComponent} from "ka-table/Models/ChildComponent";
import type {ICellProps} from "ka-table/props";
import {useMemo} from "react";
import {useTheme} from "styled-components";

import type {GetColor} from "@/theme-provider/theme";

import {DefaultItemCell} from "./default-item-cell";
import type {GetItemId} from "./item";
import * as withKaCell from "./ka-cell";
import * as withKaItemCell from "./ka-item-cell";
import * as withTableColumn from "./table-column";
import type {TableColumn} from "./table-column";
import * as withTableColumnGroup from "./table-column-group";
import type {TableColumnGroup} from "./table-column-group";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Params<TItem extends AnyObject> = {
  getItemId: GetItemId<TItem>;
  getRowHighlightedBorderColor?: (item: TItem) => GetColor | undefined;
  hasScrolledHorizontally?: boolean;
  items: TItem[];
  tableColumnGroups: TableColumnGroup<TItem>[];
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
  withoutRowBorders?: boolean;
};

type CreateCellContentParams<TItem extends AnyObject> = {
  tableColumns: TableColumn<TItem>[];
  variant: VirtualTableVariant;
};

const createCellContent =
  <TItem extends AnyObject>({
    tableColumns,
    variant
  }: CreateCellContentParams<TItem>) =>
  ({column, rowData, value}: ICellProps) => {
    const tableColumn = withTableColumn.find(
      tableColumns,
      withTableColumn.getKeyFrom(column)
    );

    if (!tableColumn) {
      return null;
    }

    const {item} = rowData;

    if (tableColumn.renderItemCell) {
      return tableColumn.renderItemCell({item, tableColumn, variant});
    }

    return (
      <DefaultItemCell {...{item, tableColumn, variant}} itemValue={value} />
    );
  };

const useKaCellComponent = <TItem extends AnyObject>({
  getItemId,
  getRowHighlightedBorderColor,
  hasScrolledHorizontally,
  items,
  tableColumnGroups,
  tableColumns,
  variant,
  withoutRowBorders
}: Params<TItem>): ChildComponent<ICellProps> => {
  const theme = useTheme();

  const CellContent = useMemo(
    () => createCellContent({tableColumns, variant}),
    [tableColumns, variant]
  );

  const tableColumnGroupRecord = useMemo(
    () => withTableColumnGroup.getTableColumnGroupRecord(tableColumnGroups),
    [tableColumnGroups]
  );

  return {
    content: CellContent,
    // eslint-disable-next-line complexity
    elementAttributes: ({column, rowData}) => {
      const tableColumn = withTableColumn.find(
        tableColumns,
        withTableColumn.getKeyFrom(column)
      );

      if (!tableColumn) {
        return {style: {display: "none"}};
      }

      const {item} = rowData;

      const tableHasItemsSummaryRow = tableColumns.some(
        withTableColumn.hasItemsSummaryRow
      );

      const isFirstRow =
        items.length > 0 &&
        getItemId(item) === getItemId(items[0]) &&
        !tableHasItemsSummaryRow;

      const isLastRow =
        items.length > 0 &&
        getItemId(item) === getItemId(items[items.length - 1]) &&
        !tableHasItemsSummaryRow;

      return {
        className: withKaCell.getFixedCellClassName({
          hasScrolledHorizontally,
          kaTableColumn: column,
          tableColumns
        }),
        style: withKaItemCell.getStyle({
          isFirstRow,
          isLastRow,
          highlightedBorderColor: getRowHighlightedBorderColor?.(item),
          tableColumn,
          tableColumnGroupRecord,
          tableColumnGroups,
          tableColumns,
          theme,
          withoutRowBorders
        })
      };
    }
  };
};

export {useKaCellComponent};
