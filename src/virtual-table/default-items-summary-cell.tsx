import {formatOptionalValue} from "@unlockre/utils-formatting/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";

import {ItemsSummaryCell} from "./items-summary-cell";
import {ItemsSummaryText} from "./items-summary-text";
import type {TableColumn} from "./table-column";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Props<TItem extends AnyObject> = {
  className?: string;
  items: TItem[];
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

const DefaultItemsSummaryCell = <TItem extends AnyObject>({
  items,
  tableColumn,
  ...rest
}: Props<TItem>) => (
  <ItemsSummaryCell {...{tableColumn}} {...rest}>
    <ItemsSummaryText>
      {formatOptionalValue(tableColumn.getSummary?.(items, items))}
    </ItemsSummaryText>
  </ItemsSummaryCell>
);

export {DefaultItemsSummaryCell};
