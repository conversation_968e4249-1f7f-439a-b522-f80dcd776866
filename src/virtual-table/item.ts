type KaItem<TItem> = {
  id: string;
  item: TItem;
};

type GetItemId<TItem> = (item: TItem) => string;

const toKaItem = <TItem>(item: TItem, getItemId: GetItemId<TItem>) => ({
  id: getItemId(item),
  item
});

const fromKaItem = <TItem>(kaItem: KaItem<TItem>) => kaItem.item;

const getIndex = <TItem>(
  items: TItem[],
  getItemId: GetItemId<TItem>,
  itemId: string
) => items.findIndex(item => getItemId(item) === itemId);

export {toKaItem, fromKaItem, getIndex};
export type {GetItemId, KaItem};
