import {formatOptionalValue} from "@unlockre/utils-formatting/dist";
import type {AnyObject} from "@unlockre/utils-object/dist";

import {GroupSummaryCell} from "./group-summary-cell";
import {GroupSummaryText} from "./group-summary-text";
import type {TableColumn} from "./table-column";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Props<TItem extends AnyObject> = {
  className?: string;
  groupItems: TItem[];
  items: TItem[];
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

const DefaultGroupSummaryCell = <TItem extends AnyObject>({
  groupItems,
  items,
  tableColumn,
  ...rest
}: Props<TItem>) => (
  <GroupSummaryCell {...{tableColumn}} {...rest}>
    <GroupSummaryText>
      {formatOptionalValue(tableColumn.getSummary?.(groupItems, items))}
    </GroupSummaryText>
  </GroupSummaryCell>
);

export {DefaultGroupSummaryCell};
