import type {AnyObject} from "@unlockre/utils-object/dist";

import {ItemCell} from "./item-cell";
import {ItemText} from "./item-text";
import {LegacyItemCell} from "./legacy-item-cell";
import type {TableColumn} from "./table-column";
import {virtualTableClassNames} from "./virtual-table-class-names";
import type {VirtualTableVariant} from "./virtual-table-variant";

type Props<TItem extends AnyObject> = {
  className?: string;
  item: TItem;
  itemValue: unknown;
  tableColumn: TableColumn<TItem>;
  variant: VirtualTableVariant;
};

const DefaultItemCell = <TItem extends AnyObject>({
  className,
  item,
  itemValue,
  tableColumn,
  ...rest
}: Props<TItem>) => {
  const allClassNames =
    virtualTableClassNames.itemCell + (className ? " " + className : "");

  if (tableColumn.renderTableCell) {
    return (
      <LegacyItemCell {...{tableColumn}} {...rest} className={allClassNames}>
        {tableColumn.renderTableCell(item, tableColumn)}
      </LegacyItemCell>
    );
  }

  return (
    <ItemCell {...{tableColumn}} {...rest} className={allClassNames}>
      <ItemText>
        {tableColumn.format ? tableColumn.format(item) : String(itemValue)}
      </ItemText>
    </ItemCell>
  );
};

export {DefaultItemCell};
