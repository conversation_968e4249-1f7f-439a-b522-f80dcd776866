import type {AnyObject} from "@unlockre/utils-object/dist";
import type {CSSProperties} from "react";

import type {Theme} from "@/theme-provider/theme";

import * as withKa<PERSON>ell from "./ka-cell";
import type {GetGroupedColumnCellStyleParams} from "./ka-cell";
import type {TableColumn} from "./table-column";

type GetStyleParams<TItem extends AnyObject> =
  GetGroupedColumnCellStyleParams<TItem> & {
    tableColumn: TableColumn<TItem>;
    theme: Theme;
  };

const getOtherStyle = <TItem extends AnyObject>({
  tableColumn,
  theme
}: GetStyleParams<TItem>): CSSProperties => ({
  backgroundColor: tableColumn.getBackgroundColor?.({theme}),
  padding: 0,
  maxWidth: tableColumn.width,
  verticalAlign: tableColumn.verticalAlign
});

const getStyle = <TItem extends AnyObject>(params: GetStyleParams<TItem>) => ({
  ...withKaCell.getGroupedColumnCellStyle(params),
  ...getOtherStyle(params)
});

export {getStyle};
export type {GetStyleParams as KaBodyCellGetStyleParams};
