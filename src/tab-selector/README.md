# Tab Selector

## About

These components provide the capability of having several tabs and switch between them.

There are two components:
- a `Tab` which contains the label, name (its identifier) and the renderView which is a ReactComponent with the content of the tab.
- a `TabSelector` to wraps the tabs.

## Types

```ts
type Tab<TName extends string, TParams extends AnyObject | void = void> = {
  name: TName;
  renderView: (
    ...args: TParams extends AnyObject ? [TParams] : []
  ) => ReactNode;
};
```

## Example

**example-tab-name.ts**

```ts
const exampleTabNames = {
  firstTab: "firstTab",
  secondTab: "secondTab"
} as const;

type ExampleTabName = (typeof exampleTabNames)[keyof typeof exampleTabNames];

const exampleTabNameDescriptions = {
  [exampleTabNames.firstTab]: "First Tab",
  [exampleTabNames.secondTab]: "Second Tab",
};

const getDescription = (exampleTabName: ExampleTabName) =>
  exampleTabNameDescriptions[exampleTabName];

export {exampleTabNames, getDescription};
export type {ExampleTabName};
```

**example-tab.ts**

```ts
import type {Tab} from "@unlockre/components-library/dist/tab-selector";

import type {ExampleTabName} from "./example-tab-name";

type ExampleTab = Tab<ExampleTabName>;

export type {ExampleTab};
```

**tabs/first-tab/first-tab-view.tsx**

```tsx
const FirstTabView = () => (
  <div>First Tab</div>
);

export {FirstTabView};
```

**tabs/first-tab/first-tab.tsx**

```tsx
import type {ExampleTab} from "../example-tab";
import {exampleTabNames} from "../example-tab-names";

import {FirstTabView} from "./first-tab-view";

const firstTab: ExampleTab = {
  name: exampleTabNames.firstTab,
  renderView: () => <FirstTabView />
};

export {firstTab};
```

**tabs/first-tab/index.ts**

```tsx
export {firstTab} from "./first-tab";
```

**tabs/second-tab/second-tab-view.tsx**

```tsx
const SecondTabView = () => (
  <div>Second Tab</div>
);

export {SecondTabView};
```

**tabs/second-tab/second-tab.tsx**

```tsx
import type {ExampleTab} from "../example-tab";
import {exampleTabNames} from "../example-tab-names";

import {SecondTabView} from "./first-tab-view";

const secondTab: ExampleTab = {
  name: exampleTabNames.secondTab,
  renderView: () => <SecondTabView />
};

export {secondTab};
```

**tabs/second-tab/index.ts**

```tsx
export {secondTab} from "./second-tab";
```

**use-example-tabs.ts**

```ts
import {useState} from "react";

import {firstTab} from "./tabs/first-tab";
import {secondTab} from "./tabs/second-tab";

import {ExampleTabName} from "./example-tab-name";

const exampleTabs = [
  firstTab,
  secondTab
];

const initialExampleTab = exampleTabs[0];

const useExampleTabs = () => {
  const [selectedExampleTab, setSelectedExampleTab] =
    useState(initialExampleTab);

  const selectExampleTab = (exampleTabName: ExampleTabName) =>
    setSelectedExampleTab(
      exampleTabs.find(exampleTab => exampleTab.name === exampleTabName)
    );

  return {
    exampleTabs,
    selectedExampleTab,
    selectExampleTab,
    setSelectedExampleTab
  };
};
```

**example.tsx**

```tsx
import {TabSelector} from "@unlockre/components-library/dist/tab-selector"

import * as withExampleTabName from "./example-tab-name";
import {useExampleTabs} from "./use-example-tabs";

const Example = () => {
  const {exampleTabs, selectedExampleTab, setSelectedExampleTab} =
    useExampleTabs();

  return (
    <>
      <TabSelector
        getTabLabel={withExampleTabName.getDescription}
        onTabSelect={setSelectedExampleTab}
        selectedTab={selectedExampleTab}
        tabs={exampleTabs}
        variant="white"
      />
      {selectedExampleTab.renderView()}
    </>
  )
};

export {Example};
```
