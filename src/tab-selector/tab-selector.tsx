import {ButtonGroup} from "@/button-group";
import type {ButtonGroupProps} from "@/button-group/button-group";

import type {AnyTab} from "./types";

type ExposedButtonGroupProps<TTab extends AnyTab> = Omit<
  ButtonGroupProps<TTab>,
  "getItemLabel" | "items" | "onItemSelect" | "selectedItem"
>;

// prettier-ignore
type Props<TTab extends AnyTab> =
  & ExposedButtonGroupProps<TTab>
  & {
    getTabLabel: (tabName: TTab["name"]) => string;
    onTabSelect: (tab: TTab) => unknown;
    selectedTab: TTab;
    tabs: TTab[];
  };

const TabSelector = <TTab extends AnyTab>({
  getTabLabel,
  onTabSelect,
  selectedTab,
  tabs,
  ...rest
}: Props<TTab>) => (
  <ButtonGroup
    {...rest}
    getItemLabel={tab => getTabLabel(tab.name)}
    items={tabs}
    onItemSelect={onTabSelect}
    selectedItem={selectedTab}
  />
);

export {TabSelector};
export type {Props as TabSelectorProps};
