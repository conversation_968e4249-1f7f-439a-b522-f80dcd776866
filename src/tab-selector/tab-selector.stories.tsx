import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import {variants} from "@/button-group/variant";

import {TabSelector} from "./tab-selector";
import type {TabSelectorProps} from "./tab-selector";
import type {Tab} from "./types";

const storyTabNames = {
  firstTab: "firstTab",
  secondTab: "secondTab",
  thirdTab: "thirdTab"
} as const;

type StoryTabName = (typeof storyTabNames)[keyof typeof storyTabNames];

type StoryTab = Tab<StoryTabName>;

type Args = Pick<TabSelectorProps<StoryTab>, "variant">;

const storyTabLabels = {
  [storyTabNames.firstTab]: "First Tab",
  [storyTabNames.secondTab]: "Second Tab",
  [storyTabNames.thirdTab]: "Third Tab"
};

const getStoryTabLabel = (storyTabName: StoryTabName) =>
  storyTabLabels[storyTabName];

const storyTabs: StoryTab[] = [
  {
    name: storyTabNames.firstTab,
    renderView: () => <div>First Tab</div>
  },
  {
    name: storyTabNames.secondTab,
    renderView: () => <div>Second Tab</div>
  },
  {
    name: storyTabNames.thirdTab,
    renderView: () => <div>Third Tab</div>
  }
];

const Default: StoryFn<TabSelectorProps<StoryTab>> = ({variant}: Args) => {
  const [selectedStoryTab, setSelectedStoryTab] = useState<StoryTab>(
    storyTabs[0]
  );

  return (
    <>
      <TabSelector
        getTabLabel={getStoryTabLabel}
        onTabSelect={setSelectedStoryTab}
        selectedTab={selectedStoryTab}
        tabs={storyTabs}
        variant={variant}
      />
      {selectedStoryTab.renderView()}
    </>
  );
};

const meta: Meta<Args> = {
  title: "tab-selector",
  argTypes: {
    variant: {
      control: "radio",
      options: Object.values(variants)
    }
  },
  args: {
    variant: "white"
  }
};

export {meta as default, Default};
