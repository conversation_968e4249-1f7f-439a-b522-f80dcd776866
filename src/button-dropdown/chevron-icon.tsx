import {ExpandLess, ExpandMore} from "@styled-icons/material";

type IconName = "expandLess" | "expandMore";

type Props = {
  isItemListOpened?: boolean;
};

const iconSize = 24;

const renderIcon = (iconName: IconName) => {
  switch (iconName) {
    case "expandLess":
      return <ExpandLess size={iconSize} />;

    case "expandMore":
      return <ExpandMore size={iconSize} />;
  }
};

const ChevronIcon = ({isItemListOpened}: Props) =>
  renderIcon(isItemListOpened ? "expandLess" : "expandMore");

export default ChevronIcon;
