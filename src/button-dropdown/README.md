# Button Dropdown

## About

This component renders a button to pick an alternative from a list of items, showing it as the label once selected.

## Examples

### With Items

```tsx
import ButtonDropdown from "@unlockre/components-library/dist/button-dropdown";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

type Item = {
  label: string;
  value: string;
};

const getItemLabel = ({label}: Item) => label;

const items: Item[] = [
  {
    label: "Active",
    value: "ACTIVE"
  },
  {
    label: "Dead",
    value: "DEAD"
  }
];

const Example = () => {
  const [value, setValue] = useState<Item | null>(items[0]);

  return (
    <ThemeProvider>
      <ButtonDropdown
        {...{items, value}}
        size="xlarge"
        variant="secondary"
        getItemLabel={getButtonItemLabel}
        onChange={setValue}
      />
    </ThemeProvider>
  );
};

export default MyApp;
```

### With Item Groups

```tsx
import Button from "@unlockre/components-library/dist/button";
import ButtonDropdown from "@unlockre/components-library/dist/button-dropdown";
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";

import type {ItemGroup} from "@unlockre/components-library/dist/button-dropdown";

type Item = {
  label: string;
  value: string;
};

const getItemLabel = ({label}: Item) => label;

const itemGroups: Item[] = [
  {
    name: "primaryStatuses",
    label: "Primary Statuses",
    items: [
      {label: "Active", value: "ACTIVE"},
      {label: "Dead", value: "DEAD"}
    ]
  },
  {
    name: "secondaryStatuses",
    label: "Secondary Statuses",
    items: [
      {label: "On Hold", value: "ON_HOLD"},
      {label: "On Review", value: "ON_REVIEW"}
    ]
  }
];

const Example = () => {
  const [value, setValue] = useState<Item | null>(itemGroups[0].items[0]);

  return (
    <ThemeProvider>
      <ButtonDropdown
        {...{itemGroups, value}}
        size="xlarge"
        variant="secondary"
        getItemLabel={getButtonItemLabel}
        onChange={setValue}
        renderFooter={({toggleItemList}) => {
          <Button onClick={() => {
            console.log("CTA");
            toggleItemList();
          }}>CTA</Button>
        }}
      />
    </ThemeProvider>
  );
};

export default MyApp;
```
