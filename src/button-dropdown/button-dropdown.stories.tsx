import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";

import ButtonDropdown from "./button-dropdown";
import type {ButtonDropdownProps} from "./button-dropdown";

const inputTypes = {
  itemGroups: "itemGroups",
  items: "items"
} as const;

type InputType = (typeof inputTypes)[keyof typeof inputTypes];

type ButtonDropdownItem = {
  label: string;
  value: string;
};

type ButtonDropdownArgs = Pick<
  ButtonDropdownProps<ButtonDropdownItem>,
  "disabled" | "isDanger" | "itemGroups" | "items" | "size" | "variant"
>;

type StoryArgs = ButtonDropdownArgs & {
  inputType: InputType;
};

const getItemDropdownLabel = ({label}: ButtonDropdownItem) => label;

const Default: StoryFn<StoryArgs> = ({
  inputType,
  itemGroups,
  items,
  ...rest
}) => {
  const [value, setValue] = useState<ButtonDropdownItem>(
    items?.[0] as ButtonDropdownItem
  );

  const inputArgs =
    inputType === inputTypes.itemGroups ? {itemGroups} : {items};

  return (
    <ButtonDropdown
      {...{value}}
      {...inputArgs}
      {...rest}
      getItemLabel={getItemDropdownLabel}
      onChange={setValue}
    />
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=2165%3A59389&t=4QGjK94qkycx2hkK-0"
  }
};

const meta: Meta<StoryArgs> = {
  title: "button-dropdown",
  argTypes: {
    variant: {
      control: "radio",
      options: ["primary", "secondary", "transparent"]
    },
    size: {
      control: "radio",
      options: ["large", "medium", "small", "xlarge"]
    },
    inputType: {
      control: "radio",
      options: Object.values(inputTypes)
    },
    itemGroups: {
      control: "object"
    },
    items: {
      control: "object"
    },
    isDanger: {
      control: "boolean"
    },
    disabled: {
      control: "boolean"
    }
  },
  args: {
    variant: "primary",
    size: "xlarge",
    inputType: inputTypes.items,
    itemGroups: [
      {
        name: "primaryStatuses",
        label: "Primary Statuses",
        items: [
          {label: "Active", value: "ACTIVE"},
          {label: "Dead", value: "DEAD"}
        ]
      },
      {
        name: "secondaryStatuses",
        label: "Secondary Statuses",
        items: [
          {label: "On Hold", value: "ON_HOLD"},
          {label: "On Review", value: "ON_REVIEW"}
        ]
      }
    ],
    items: [
      {label: "Active", value: "ACTIVE"},
      {label: "Dead", value: "DEAD"},
      {label: "On hold", value: "ON_HOLD"}
    ],
    isDanger: false,
    disabled: false
  }
};

export {meta as default, Default};
