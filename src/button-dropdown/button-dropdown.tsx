import type {ComponentProps} from "react";
import styled from "styled-components";

import Button from "@/button";
import {SingleItemList, useSingleItemList} from "@/item-list";
import type {SingleItemListProps, UseSingleItemListParams} from "@/item-list";

import ChevronIcon from "./chevron-icon";

type ButtonProps = ComponentProps<typeof Button>;

type ExposedUseSingleItemListParams<TItem> = Pick<
  UseSingleItemListParams<TItem>,
  "renderFooter"
>;

type ExposedSingleItemListProps<TItem> = Pick<
  SingleItemListProps<TItem>,
  "getItemLabel" | "itemGroups" | "items"
>;

type ExposedButtonProps = Pick<
  ButtonProps,
  "disabled" | "isDanger" | "size" | "variant"
>;

type Props<TItem> = ExposedButtonProps &
  ExposedSingleItemListProps<TItem> &
  ExposedUseSingleItemListParams<TItem> & {
    className?: string;
    onChange: (value: TItem) => unknown;
    value: TItem;
  };

type ItemListOnChange<TItem> = SingleItemListProps<TItem>["onChange"];

const Container = styled.div`
  position: relative;
  display: inline-flex;
`;

const StyledButton = styled(Button)`
  width: 100%;

  && .MuiButton-endIcon {
    margin-left: auto;
  }
`;

const ItemLabelContainer = styled.span`
  display: block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`;

const ButtonDropdown = <TItem,>({
  className,
  getItemLabel,
  itemGroups,
  items,
  onChange,
  renderFooter,
  value,
  ...rest
}: Props<TItem>) => {
  const handleItemSelect: ItemListOnChange<TItem> = item => {
    if (!item) {
      return;
    }

    toggleItemList();

    onChange(item);
  };

  const {isItemListOpened, itemListProps, setAnchorageElement, toggleItemList} =
    useSingleItemList({
      getItemLabel,
      itemGroups,
      items,
      onItemSelect: handleItemSelect,
      renderFooter
    });

  const renderItemList = () => (
    <SingleItemList
      {...{getItemLabel, value}}
      {...itemListProps}
      onChange={handleItemSelect}
    />
  );

  return (
    <Container className={className}>
      <StyledButton
        {...rest}
        endIcon={<ChevronIcon isItemListOpened={isItemListOpened} />}
        onClick={toggleItemList}
        ref={setAnchorageElement}
      >
        <ItemLabelContainer>{getItemLabel(value)}</ItemLabelContainer>
      </StyledButton>
      {isItemListOpened && renderItemList()}
    </Container>
  );
};

export default ButtonDropdown;

export type {Props as ButtonDropdownProps};
