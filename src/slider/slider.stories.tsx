import type {Meta, StoryFn} from "@storybook/react";
import {useState} from "react";
import type {ComponentProps} from "react";

import {Slider} from "./slider";

type SliderProps = ComponentProps<typeof Slider>;

type Args = Pick<SliderProps, "max" | "min" | "step">;

const Default: StoryFn<Args> = args => {
  const [value, setValue] = useState(0);

  return <Slider {...{value}} {...args} onChange={setValue} />;
};

const meta: Meta<Args> = {
  title: "slider",
  argTypes: {
    max: {
      control: "number"
    },
    min: {
      control: "number"
    },
    step: {
      control: "number"
    }
  },
  args: {
    max: 10,
    min: 0,
    step: 10
  }
};

export {meta as default, Default};
