import styled, {css} from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";
import type {GetColor} from "@/theme-provider/theme";
import {UnstyledInputRange} from "@/unstyled";
import type {UnstyledInputRangeCssProps} from "@/unstyled";

type Props = {
  className?: string;
  max: number;
  min: number;
  onChange: (value: number) => unknown;
  step: number;
  value: number;
};

const trackHeight = 4;

const thumbHeight = 20;

const getBoxShadowBorderCss = (getColor: GetColor) => css`
  box-shadow: inset 0 0 0 1px ${getColor};
`;

const thumbCss = css`
  ${getBoxShadowBorderCss(getColorByAlias("borderRegular"))}

  background: ${getColorByAlias("bgField")};
  border-radius: 50%;
  height: ${thumbHeight}px;
  width: ${thumbHeight}px;
  transform: translateY(-${(thumbHeight - trackHeight) / 2}px);
`;

const getTrackBackgroundCss = (fillPercent: number) => css`
  background: linear-gradient(
    to right,
    ${getColorByAlias("bgAccent")} ${fillPercent}%,
    ${getColorByAlias("bgAccentSecondary")} ${fillPercent}%
  );
`;

const getTrackFillPercent = (props: UnstyledInputRangeCssProps) =>
  ((props.value - props.min) / (props.max - props.min)) * 100;

const trackCss = css<UnstyledInputRangeCssProps>`
  ${props => getTrackBackgroundCss(getTrackFillPercent(props))}

  ${getBoxShadowBorderCss(getColorByAlias("borderRegular"))}

  border-radius: 2px;
  height: ${trackHeight}px;
`;

const StyledInputRange = styled(UnstyledInputRange)`
  height: ${thumbHeight}px;
`;

const Slider = ({max, min, onChange, value, ...rest}: Props) => (
  <StyledInputRange
    {...{max, min, thumbCss, trackCss, value}}
    {...rest}
    onChange={event => onChange(event.target.valueAsNumber)}
  />
);

export {Slider};
