import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import * as withArray from "@unlockre/utils-array/dist";
import type {ComponentProps} from "react";

import type {UploadableFile} from "../uploadable-file";

import MultiFileField from "./multi-file-field";

type MultiFileFieldProps = ComponentProps<typeof MultiFileField>;

type Args = Pick<
  MultiFileFieldProps,
  | "acceptedFileTypes"
  | "errorMessage"
  | "files"
  | "hasError"
  | "isDisabled"
  | "label"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  const onFileRemove = (file: UploadableFile) =>
    updateArgs({
      files: withArray.remove(args.files, args.files.indexOf(file), 1)
    });

  const onFilesSelect = (files: UploadableFile[]) =>
    updateArgs({files: [...args.files, ...files]});

  return (
    <MultiFileField
      {...{onFileRemove, onFilesSelect}}
      {...args}
      onFileDownload={() => {
        console.log("File download!");
      }}
    />
  );
};

const meta: Meta<Args> = {
  title: "multi-file-field",
  argTypes: {
    label: {
      control: "text"
    },
    acceptedFileTypes: {
      control: "object"
    },
    errorMessage: {
      control: "text"
    },
    files: {
      control: "object"
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    }
  },
  args: {
    label: "Documents",
    // eslint-disable-next-line @typescript-eslint/naming-convention
    acceptedFileTypes: {"application/pdf": [".pdf"], "image/jpg": [".jpg"]},
    errorMessage: "",
    files: [
      {
        name: "file0.pdf",
        type: "application/pdf",
        url: undefined,
        isUploading: true
      },
      {
        name: "file1.jpg",
        type: "image/jpg",
        url: "https://akamai.sscdn.co/uploadfile/letras/fotos/5/9/6/2/59625c6b8950a0b058b325f92e478dc9.jpg",
        isUploading: false
      }
    ],
    hasError: false,
    isDisabled: false
  }
};

export {meta as default, Default};
