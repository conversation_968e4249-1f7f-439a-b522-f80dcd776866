import type {ComponentProps} from "react";
import type {Accept} from "react-dropzone";
import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";
import {UnstyledUl} from "@/unstyled";

import FileEntry from "../file-entry";
import FileFieldContainer from "../file-field-container";
import FileSelector from "../file-selector";
import type {UploadableFile} from "../uploadable-file";

type FileEntryProps = ComponentProps<typeof FileEntry>;

type ExposedFileEntryProps = Pick<
  FileEntryProps,
  "hideFileRemove" | "onFileDownload" | "onFileRemove" | "renderFileActionsLeft"
>;

type FileFieldContainerProps = ComponentProps<typeof FileFieldContainer>;

type ExposedFileFieldContainerProps = Omit<
  FileFieldContainerProps,
  "children" | "className"
>;

// prettier-ignore
type Props = 
  & ExposedFileEntryProps
  & ExposedFileFieldContainerProps
  & {
  acceptedFileTypes: Accept;
  className?: string;
  files: UploadableFile[];
  onFilesSelect: (file: UploadableFile[]) => unknown;
};

const renderFileEntryUsing =
  ({
    hideFileRemove,
    isDisabled,
    onFileDownload,
    onFileRemove,
    renderFileActionsLeft
  }: Props) =>
  (file: UploadableFile, index: number) => (
    <li key={file.type + "/" + file.name + ":" + index}>
      <FileEntry
        {...{
          file,
          hideFileRemove,
          isDisabled,
          onFileDownload,
          onFileRemove,
          renderFileActionsLeft
        }}
      />
    </li>
  );

const FileEntriesInnerContainer = styled(UnstyledUl)`
  max-height: ${FileEntry.height * 3.5}px;
  overflow: auto;
`;

const FileEntriesContainer = styled.div`
  background-color: ${getColor("blue", "040")};
  border-radius: 8px;
  margin-top: 12px;
  padding: 8px 0;
`;

const renderFileEntries = (props: Props) => (
  <FileEntriesContainer>
    <FileEntriesInnerContainer>
      {props.files.map(renderFileEntryUsing(props))}
    </FileEntriesInnerContainer>
  </FileEntriesContainer>
);

const renderFileSelector = ({
  acceptedFileTypes,
  hasError,
  isDisabled,
  onFilesSelect
}: Props) => (
  <FileSelector
    {...{
      acceptedFileTypes,
      hasError,
      isDisabled,
      onFilesSelect
    }}
    filesLimit={Infinity}
    hasError={hasError}
  />
);

const getFileFieldContainerProps = ({
  className,
  files,
  hideFileRemove,
  onFileDownload,
  onFileRemove,
  onFilesSelect,
  renderFileActionsLeft,
  ...fileFieldContainerProps
}: Props) => fileFieldContainerProps;

const MultiFileField = (props: Props) => (
  <div className={props.className}>
    <FileFieldContainer {...getFileFieldContainerProps(props)}>
      {renderFileSelector(props)}
    </FileFieldContainer>
    {props.files.length > 0 && renderFileEntries(props)}
  </div>
);

export default MultiFileField;
