import type {ComponentProps} from "react";
import type {Accept} from "react-dropzone";

import FileEntry from "../file-entry";
import FileFieldContainer from "../file-field-container";
import FileSelector from "../file-selector";
import FileSelectorContainer from "../file-selector-container";
import type {UploadableFile} from "../uploadable-file";

type FileEntryProps = ComponentProps<typeof FileEntry>;

type ExposedFileEntryProps = Pick<
  FileEntryProps,
  "onFileDownload" | "renderFileActionsLeft"
>;

type FileFieldContainerProps = ComponentProps<typeof FileFieldContainer>;

type ExposedFileFieldContainerProps = Omit<FileFieldContainerProps, "children">;

type Value = UploadableFile | null;

// prettier-ignore
type Props = 
  & ExposedFileEntryProps
  & ExposedFileFieldContainerProps
  & {
  acceptedFileTypes: Accept;
  onChange: (value: Value) => unknown;
  value: Value;
};

const SingleFileField = ({
  acceptedFileTypes,
  hasError,
  isDisabled,
  onChange,
  onFileDownload,
  renderFileActionsLeft,
  value,
  ...containerProps
}: Props) => {
  const handleFilesSelect = (files: UploadableFile[]) => {
    onChange(files[0]);
  };

  const handleFileRemove = () => {
    onChange(null);
  };

  return (
    <FileFieldContainer
      {...{acceptedFileTypes, hasError, isDisabled}}
      {...containerProps}
    >
      {!value ? (
        <FileSelector
          {...{
            isDisabled,
            acceptedFileTypes
          }}
          filesLimit={1}
          hasError={hasError}
          onFilesSelect={handleFilesSelect}
        />
      ) : (
        <FileSelectorContainer>
          <FileEntry
            {...{isDisabled, onFileDownload, renderFileActionsLeft}}
            file={value}
            onFileRemove={handleFileRemove}
          />
        </FileSelectorContainer>
      )}
    </FileFieldContainer>
  );
};

export default SingleFileField;
