import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import SingleFileField from "./single-file-field";

type SingleFileFieldProps = ComponentProps<typeof SingleFileField>;

type Args = Pick<
  SingleFileFieldProps,
  | "acceptedFileTypes"
  | "errorMessage"
  | "hasError"
  | "isDisabled"
  | "label"
  | "value"
>;

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  return (
    <SingleFileField
      onChange={file => updateArgs({value: file})}
      onFileDownload={() => {
        console.log("File download!");
      }}
      {...args}
    />
  );
};

const meta: Meta<Args> = {
  title: "single-file-field",
  argTypes: {
    label: {
      control: "text"
    },
    acceptedFileTypes: {
      control: "object"
    },
    errorMessage: {
      control: "text"
    },
    hasError: {
      control: "boolean"
    },
    isDisabled: {
      control: "boolean"
    },
    value: {
      control: "object"
    }
  },
  args: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    acceptedFileTypes: {"application/pdf": [".pdf"], "image/jpg": [".jpg"]},
    label: "Document Clean Version",
    value: {
      name: "name.pdf",
      type: "application/pdf",
      url: undefined,
      isUploading: true
    }
  }
};

export {meta as default, Default};
