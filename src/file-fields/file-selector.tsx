import {useDropzone} from "react-dropzone";
import type {Accept} from "react-dropzone";
import styled, {css} from "styled-components";

import Button from "@/button";
import {getColorByAlias, getTypography} from "@/theme-provider/theme";

import AddFileIcon from "./add-file-icon";
import FileSelectorContainer from "./file-selector-container";
import * as withUploadableFile from "./uploadable-file";
import type {UploadableFile} from "./uploadable-file";

type Props = {
  acceptedFileTypes: Accept;
  filesLimit: number;
  hasError?: boolean;
  isDisabled?: boolean;
  onFilesSelect: (files: UploadableFile[]) => unknown;
};

type FileSelectorMessageStyledProps = {
  $isDisabled?: boolean;
};

const SelectFileButton = styled(Button)`
  ${getTypography("body", "xs", 600)};
  padding-right: 2px;
`;

const fileSelectorMessageEnabledCss = css`
  color: ${getColorByAlias("textPrimary")};
  cursor: pointer;
`;

const fileSelectorMessageDisabledCss = css`
  color: ${getColorByAlias("textDisabled")};
`;

const FileSelectorMessage = styled.div<FileSelectorMessageStyledProps>`
  ${getTypography("body", "xs", 600)};
  ${props =>
    props.$isDisabled
      ? fileSelectorMessageDisabledCss
      : fileSelectorMessageEnabledCss}
  align-content: center;
  align-items: center;
  display: flex;
  flex-direction: row;
  height: 100%;
  justify-content: center;
`;

const FileSelector = ({
  acceptedFileTypes,
  filesLimit,
  hasError,
  isDisabled,
  onFilesSelect
}: Props) => {
  const {getInputProps, getRootProps} = useDropzone({
    accept: acceptedFileTypes,
    disabled: isDisabled,
    onDrop: acceptedFiles => {
      if (acceptedFiles.length) {
        onFilesSelect(acceptedFiles.map(withUploadableFile.fromFile));
      }
    },
    maxFiles: filesLimit,
    // https://github.com/react-dropzone/react-dropzone/issues/1190
    useFsAccessApi: false
  });

  return (
    <FileSelectorContainer $hasError={hasError} {...getRootProps()}>
      <input {...getInputProps()} />
      <FileSelectorMessage $isDisabled={isDisabled}>
        <SelectFileButton
          disabled={isDisabled}
          size="small"
          startIcon={<AddFileIcon isDisabled={isDisabled} />}
          type="button"
          variant="transparent"
        >
          Add file
          {filesLimit > 1 ? "(s)" : ""}
        </SelectFileButton>
        or drop here
      </FileSelectorMessage>
    </FileSelectorContainer>
  );
};

export default FileSelector;
