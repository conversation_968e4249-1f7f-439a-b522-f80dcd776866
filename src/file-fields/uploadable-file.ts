import * as z from "zod";

type UploadableFile = {
  file?: File;
  isUploading?: boolean;
  name: string;
  type: string;
  url?: string;
};

const uploadableFileSchema = z.object({
  file: z.any().optional(),
  isUploading: z.boolean().optional(),
  name: z.string(),
  type: z.string(),
  url: z.string().optional()
});

const hasExtension = (uploadableFile: UploadableFile) =>
  uploadableFile.name.includes(".");

const getSubtype = (uploadableFile: UploadableFile) =>
  uploadableFile.type.split("/").at(-1);

const getNameWithoutExtension = (uploadableFile: UploadableFile) =>
  hasExtension(uploadableFile)
    ? uploadableFile.name.split(".").slice(0, -1).join(".")
    : uploadableFile.name;

const getExtension = (uploadableFile: UploadableFile) =>
  hasExtension(uploadableFile)
    ? uploadableFile.name.split(".").at(-1)
    : undefined;

const getDisplayType = (uploadableFile: UploadableFile) =>
  getExtension(uploadableFile) || getSubtype(uploadableFile);

const fromFile = (file: File): UploadableFile => ({
  file,
  isUploading: false,
  name: file.name,
  type: file.type,
  url: undefined
});

export {
  fromFile,
  getDisplayType,
  getExtension,
  getNameWithoutExtension,
  getSubtype,
  hasExtension,
  uploadableFileSchema
};

export type {UploadableFile};
