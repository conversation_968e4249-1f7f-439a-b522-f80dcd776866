import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

type Props = {
  className?: string;
  isDisabled?: boolean;
};

type StyledPathStyledProps = {
  $isDisabled?: boolean;
};

const StyledPath = styled.path<StyledPathStyledProps>`
  fill: ${props =>
    props.$isDisabled
      ? getColorByAlias("textDisabled")
      : getColorByAlias("accentSecondary")};
`;

const AddFileIcon = ({className, isDisabled}: Props) => (
  <svg
    className={className}
    fill="none"
    height="14"
    viewBox="0 0 13 14"
    width="13"
    xmlns="http://www.w3.org/2000/svg"
  >
    <StyledPath
      $isDisabled={isDisabled}
      clipRule="evenodd"
      d="M1.97452 11.5254C4.47387 14.0248 8.52613 14.0248 11.0255 11.5254C13.5248 9.02607 13.5248 4.97381 11.0255 2.47446C8.52613 -0.0248976 4.47387 -0.0248976 1.97452 2.47446C-0.524838 4.97381 -0.524838 9.02607 1.97452 11.5254ZM7.46049 3.87585C7.46049 3.40456 7.07844 3.02251 6.60715 3.02251C6.13587 3.02251 5.75382 3.40456 5.75382 3.87585L5.75382 5.99522L3.63444 5.99522C3.16316 5.99522 2.78111 6.37727 2.78111 6.84856C2.78111 7.31984 3.16316 7.70189 3.63444 7.70189L5.75382 7.70189L5.75382 9.81222C5.75382 10.2835 6.13587 10.6656 6.60716 10.6656C7.07844 10.6656 7.46049 10.2835 7.46049 9.81222L7.46049 7.70189H9.57082C10.0421 7.70189 10.4241 7.31984 10.4242 6.84856C10.4241 6.37727 10.0421 5.99522 9.57082 5.99522L7.46049 5.99522L7.46049 3.87585Z"
      fillRule="evenodd"
    />
  </svg>
);

export default AddFileIcon;
