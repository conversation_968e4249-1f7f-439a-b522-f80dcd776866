import {formatList} from "@unlockre/utils-formatting/dist";
import type {ComponentProps} from "react";
import type {Accept} from "react-dropzone";

import {FieldContainer} from "@/field";

type FieldContainerProps = ComponentProps<typeof FieldContainer>;

type ExposedFieldContainerProps = Omit<FieldContainerProps, "infoMessage">;

type Props = ExposedFieldContainerProps & {
  acceptedFileTypes: Accept;
};

const getAcceptedFileExtensionsMessage = (acceptedFileTypes: Accept) =>
  formatList(Object.values(acceptedFileTypes).flat());

const getInfoMessage = (acceptedFileTypes: Accept) =>
  "We support " + getAcceptedFileExtensionsMessage(acceptedFileTypes);

const FileFieldContainer = ({acceptedFileTypes, ...rest}: Props) => (
  <FieldContainer {...rest} infoMessage={getInfoMessage(acceptedFileTypes)} />
);

export default FileFieldContainer;
