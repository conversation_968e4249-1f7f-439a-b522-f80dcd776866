import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

type StyledProps = {
  $hasError?: boolean;
};

const getErrorBorderColor = getColorByAlias("feedbackError");

const getDefaultBorderColor = getColorByAlias("backgroundPrimary");

const FileSelectorContainer = styled.div<StyledProps>`
  border: 1px dashed
    ${props => (props.$hasError ? getErrorBorderColor : getDefaultBorderColor)};
  height: 44px;
  border-radius: 4px;
`;

export default FileSelectorContainer;
