import type {ReactNode} from "react";
import styled from "styled-components";

import Spinner from "@/spinner";
import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

import DownloadFileIcon from "./download-file-icon";
import FileActionButton from "./file-action-button";
import RemoveFileIcon from "./remove-file-icon";
import * as withUploadableFile from "./uploadable-file";
import type {UploadableFile} from "./uploadable-file";

type Props = {
  file: UploadableFile;
  hideFileRemove?: boolean;
  isDisabled?: boolean;
  onFileDownload: (file: UploadableFile) => unknown;
  onFileRemove?: (file: UploadableFile) => unknown;
  renderFileActionsLeft?: (file: UploadableFile) => ReactNode;
};

type IsDisabledStyledProps = {
  $isDisabled?: boolean;
};

const FileNameContainer = styled.div`
  ${getTypography("body", "xs")};
  color: ${getColorByAlias("textPrimary")};
  display: flex;
  min-width: 0;
`;

const renderRemoveFileIcon = ({
  file,
  hideFileRemove,
  isDisabled,
  onFileRemove
}: Props) => {
  if (hideFileRemove || isDisabled) {
    return null;
  }

  return (
    <FileActionButton onClick={() => onFileRemove?.(file)}>
      <RemoveFileIcon />
    </FileActionButton>
  );
};

const renderDownloadFileIcon = ({file, onFileDownload}: Props) => (
  <FileActionButton onClick={() => onFileDownload(file)}>
    <DownloadFileIcon />
  </FileActionButton>
);

const renderLoadingIcon = () => <Spinner size={16} />;

const Actions = styled.div`
  align-items: center;
  display: flex;
  flex-direction: row;
  gap: 16px;
`;

const FileExtensionContainer = styled.span`
  flex-shrink: 0;
`;

const renderFileExtension = ({file}: Props) => (
  <FileExtensionContainer>
    {"." + withUploadableFile.getExtension(file)}
  </FileExtensionContainer>
);

const FileNameWithoutExtensionContainer = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const renderFileNameWithoutExtension = ({file}: Props) => (
  <FileNameWithoutExtensionContainer>
    {withUploadableFile.getNameWithoutExtension(file)}
  </FileNameWithoutExtensionContainer>
);

const renderFileName = (props: Props) => (
  <FileNameContainer>
    {renderFileNameWithoutExtension(props)}
    {withUploadableFile.hasExtension(props.file) && renderFileExtension(props)}
  </FileNameContainer>
);

const FileTypeContainer = styled.div`
  ${getTypography("body", "xxxs", 600)};
  background: ${getColorByAlias("accentTertiary")};
  border-radius: 4px;
  color: ${getColorByAlias("textInverted")};
  letter-spacing: 1px;
  padding: 4px 8px;
  margin-right: 8px;
`;

const renderFileDisplayType = ({file}: Props) => (
  <FileTypeContainer>
    {withUploadableFile.getDisplayType(file)?.toUpperCase()}
  </FileTypeContainer>
);

const FileInfo = styled.div<IsDisabledStyledProps>`
  align-items: center;
  display: flex;
  flex-direction: row;
  margin-right: 20px;
  opacity: ${props => (props.$isDisabled ? 0.5 : 1)};
  overflow: hidden;
`;

const containerHeight = 44;

const Container = styled.div<IsDisabledStyledProps>`
  align-items: center;
  background: ${props =>
    props.$isDisabled ? "transparent" : getColor("blue", "040")};
  display: flex;
  flex-direction: row;
  height: ${containerHeight}px;
  justify-content: space-between;
  padding: 0 12px;

  &:hover {
    background: ${props =>
      props.$isDisabled ? "transparent" : getColor("blue", "100")};
  }
`;

// eslint-disable-next-line complexity
const FileEntry = (props: Props) => (
  <Container $isDisabled={props.isDisabled || props.file.isUploading}>
    <FileInfo $isDisabled={props.isDisabled || props.file.isUploading}>
      {renderFileDisplayType(props)}
      {renderFileName(props)}
    </FileInfo>
    <Actions>
      {props.file.isUploading && renderLoadingIcon()}
      {props.renderFileActionsLeft?.(props.file)}
      {props.file.url &&
        !props.file.isUploading &&
        renderDownloadFileIcon(props)}
      {renderRemoveFileIcon(props)}
    </Actions>
  </Container>
);

export default Object.assign(FileEntry, {height: containerHeight});
