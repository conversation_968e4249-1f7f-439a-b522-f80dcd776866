import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import FileActionButton from "./file-action-button";

type Props = {
  className?: string;
  onClick?: () => void;
};

const StyledPath = styled.path`
  fill: ${getColorByAlias("feedbackError")};
`;

const StyledSvg = styled.svg`
  ${FileActionButton.fileActionIconCss};
`;

const RemoveFileIcon = (props: Props) => (
  <StyledSvg
    fill="none"
    viewBox="0 0 14 14"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <StyledPath
      clipRule="evenodd"
      d="M6.99998 13.4001C10.5346 13.4001 13.4 10.5347 13.4 7.0001C13.4 3.46548 10.5346 0.600098 6.99998 0.600098C3.46535 0.600098 0.599976 3.46548 0.599976 7.0001C0.599976 10.5347 3.46535 13.4001 6.99998 13.4001ZM5.47007 4.11186C5.13683 3.77861 4.59653 3.77861 4.26328 4.11186C3.93003 4.44511 3.93003 4.98541 4.26328 5.31866L5.7619 6.81728L4.26328 8.31591C3.93003 8.64916 3.93003 9.18946 4.26328 9.52271C4.59653 9.85595 5.13683 9.85595 5.47007 9.52271L6.9687 8.02408L8.46093 9.51631C8.79417 9.84955 9.33448 9.84955 9.66772 9.51631C10.001 9.18306 10.001 8.64276 9.66772 8.30951L8.1755 6.81728L9.66772 5.32506C10.001 4.99181 10.001 4.45151 9.66772 4.11826C9.33448 3.78501 8.79417 3.78501 8.46093 4.11826L6.9687 5.61049L5.47007 4.11186Z"
      fillRule="evenodd"
    />
  </StyledSvg>
);

export default RemoveFileIcon;
