import type {FunctionComponent} from "react";
import styled from "styled-components";

// eslint-disable-next-line no-undef
type InputProps = JSX.IntrinsicElements["input"];

// We are creating this type because Styled component is breaking the use of
// ComponentProps<typeof UnstyledInput>
type InputComponent = FunctionComponent<InputProps>;

const UnstyledInput: InputComponent = styled.input`
  border: none;
  outline: none;

  &:disabled {
    background-color: unset;
  }

  &:focus {
    outline: none;
  }
`;

export {UnstyledInput};
