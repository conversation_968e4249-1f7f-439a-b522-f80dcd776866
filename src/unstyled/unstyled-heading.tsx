import type {HTMLAttributes} from "react";
import styled, {css} from "styled-components";

type HTMLHeadingAttributes = HTMLAttributes<HTMLHeadingElement>;

type HeadingLevel = 1 | 2 | 3 | 4 | 5 | 6;

type Props = HTMLHeadingAttributes & {
  level: HeadingLevel;
};

const unstyledHeadingCss = css`
  font-weight: 400;
  margin: 0;
  padding: 0;
`;

const UnstyledH6 = styled.h6`
  ${unstyledHeadingCss}
`;

const UnstyledH5 = styled.h5`
  ${unstyledHeadingCss}
`;

const UnstyledH4 = styled.h4`
  ${unstyledHeadingCss}
`;

const UnstyledH3 = styled.h3`
  ${unstyledHeadingCss}
`;

const UnstyledH2 = styled.h2`
  ${unstyledHeadingCss}
`;

const UnstyledH1 = styled.h1`
  ${unstyledHeadingCss}
`;

// eslint-disable-next-line complexity
const UnstyledHeading = ({level, ...rest}: Props) => {
  switch (level) {
    case 1:
      return <UnstyledH1 {...rest} />;

    case 2:
      return <UnstyledH2 {...rest} />;

    case 3:
      return <UnstyledH3 {...rest} />;

    case 4:
      return <UnstyledH4 {...rest} />;

    case 5:
      return <UnstyledH5 {...rest} />;

    case 6:
      return <UnstyledH6 {...rest} />;
  }
};

export {UnstyledHeading};
