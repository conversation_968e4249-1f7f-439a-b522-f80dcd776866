import type {FunctionComponent} from "react";
import styled from "styled-components";

// eslint-disable-next-line no-undef
type ButtonProps = JSX.IntrinsicElements["button"];

// We are creating this type because Styled component is breaking the use of
// ComponentProps<typeof UnstyledButton>
type ButtonComponent = FunctionComponent<ButtonProps>;

const UnstyledButton: ButtonComponent = styled.button`
  background: none;
  border: none;
  cursor: ${props => (props.disabled ? "auto" : "pointer")};
  padding: 0;

  :focus {
    outline: none;
  }
`;

export {UnstyledButton};
