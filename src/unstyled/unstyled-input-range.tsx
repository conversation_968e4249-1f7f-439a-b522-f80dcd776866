import {forwardRef} from "react";
import type {ComponentPropsWithoutRef, ComponentRef} from "react";
import styled, {css} from "styled-components";
import type {
  DefaultTheme,
  FlattenInterpolation,
  ThemeProps
} from "styled-components";

import {UnstyledInput} from "./unstyled-input";

type UnstyledInputProps = ComponentPropsWithoutRef<typeof UnstyledInput>;

type ExposedUnstyledInputProps = Omit<
  UnstyledInputProps,
  "max" | "min" | "type" | "value"
>;

type AnyUnstyledInputRangeCssProps = ThemeProps<DefaultTheme> &
  UnstyledInputProps;

type AnyUnstyledInputRangeCss =
  FlattenInterpolation<AnyUnstyledInputRangeCssProps>;

type RangeProps = {
  max: number;
  min: number;
  value: number;
};

type UnstyledInputRef = ComponentRef<typeof UnstyledInput>;

// prettier-ignore
type UnstyledInputRangeCssProps =
  & ExposedUnstyledInputProps
  & RangeProps
  & ThemeProps<DefaultTheme>
  & {
      type: "range";
    };

type UnstyledInputRangeCss = FlattenInterpolation<UnstyledInputRangeCssProps>;

// prettier-ignore
type Props =
  & ExposedUnstyledInputProps
  & RangeProps
  & {
      thumbCss?: UnstyledInputRangeCss;
      trackCss?: UnstyledInputRangeCss;
    };

type StyledInputStyledProps = {
  $thumbCss?: UnstyledInputRangeCss;
  $trackCss?: UnstyledInputRangeCss;
};

const unstyledThumbCss = css`
  appearance: none;
  cursor: pointer;
`;

const unstyledTrackCss = css`
  cursor: pointer;
`;

const StyledInput = styled(UnstyledInput)<StyledInputStyledProps>`
  appearance: none;
  color: unset;
  margin: 0;

  ::-moz-range-thumb {
    ${unstyledThumbCss}

    ${props => props.$thumbCss as AnyUnstyledInputRangeCss}
  }

  ::-moz-range-track {
    ${unstyledTrackCss}

    ${props => props.$trackCss as AnyUnstyledInputRangeCss}
  }

  ::-webkit-slider-thumb {
    ${unstyledThumbCss}

    ${props => props.$thumbCss as AnyUnstyledInputRangeCss}
  }

  ::-webkit-slider-runnable-track {
    ${unstyledTrackCss}

    ${props => props.$trackCss as AnyUnstyledInputRangeCss}
  }
`;

const UnstyledInputRange = forwardRef<UnstyledInputRef, Props>(
  ({thumbCss, trackCss, ...rest}, ref) => (
    <StyledInput
      {...rest}
      $thumbCss={thumbCss}
      $trackCss={trackCss}
      ref={ref}
      type="range"
    />
  )
);

export {UnstyledInputRange};
export type {UnstyledInputRangeCssProps};
