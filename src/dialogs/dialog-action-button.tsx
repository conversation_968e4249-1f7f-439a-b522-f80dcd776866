import type {ComponentProps, ReactElement} from "react";

import {But<PERSON>} from "@/buttons";

import {type DialogActionType, dialogActionTypes} from "./dialog-action-type";

type ButtonProps = ComponentProps<typeof Button>;

type Props = Pick<
  ButtonProps,
  "autoFocus" | "children" | "isDisabled" | "onClick"
> & {
  actionType: DialogActionType;
};

const isDialogActionButtonWith = (
  element: ReactElement,
  actionType: DialogActionType
) =>
  element.type === DialogActionButton &&
  element.props.actionType === actionType;

const ensureDialogActionButtonWith = (
  element: ReactElement,
  actionType: DialogActionType
) => {
  if (!isDialogActionButtonWith(element, actionType)) {
    throw new Error(
      "Expected a DialogActionButton element with actionType: " + actionType
    );
  }

  return element;
};

const variants = {
  [dialogActionTypes.cancel]: "transparent",
  [dialogActionTypes.primary]: "primary",
  [dialogActionTypes.secondary]: "secondary",
  [dialogActionTypes.tertiary]: "transparent"
} as const;

const DialogActionButton = ({actionType, ...rest}: Props) => (
  <Button {...rest} size="medium" variant={variants[actionType]} />
);

export {ensureDialogActionButtonWith, DialogActionButton};
