import type {ReactElement, ReactNode} from "react";
import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import {dialogSectionHorizontalPadding} from "./dialog-section";
import {DialogTextBody} from "./dialog-text-body";
import {renderElementOrString} from "./render-element-or-string";

type Props = {
  adornmentBox?: ReactNode;
  children: ReactNode;
  className?: string;
  footer?: ReactElement;
  header: ReactElement;
};

const renderHeaderAndBody = ({children, header}: Props) => (
  <>
    {header}
    {renderElementOrString(children, children => (
      <DialogTextBody>{children}</DialogTextBody>
    ))}
  </>
);

const TopRightContainer = styled.div`
  flex: 1;
`;

const TopContainer = styled.div`
  align-items: flex-start;
  display: flex;
  padding-left: ${dialogSectionHorizontalPadding}px;
`;

const Container = styled.div`
  background-color: ${getColorByAlias("bgDialog")};
  border: 1px solid ${getColorByAlias("borderSubtle")};
  border-radius: 16px;
  padding: 24px 0 16px;
`;

const renderWithoutAdornment = (props: Props) => (
  <Container className={props.className}>
    {renderHeaderAndBody(props)}
    {props.footer}
  </Container>
);

const renderWithAdornment = (props: Props, adornmentBox: ReactNode) => (
  <Container className={props.className}>
    <TopContainer>
      {adornmentBox}
      <TopRightContainer>{renderHeaderAndBody(props)}</TopRightContainer>
    </TopContainer>
    {props.footer}
  </Container>
);

const DialogBox = (props: Props) =>
  props.adornmentBox
    ? renderWithAdornment(props, props.adornmentBox)
    : renderWithoutAdornment(props);

export {DialogBox};
