import type {ComponentProps} from "react";
import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledDialog} from "@/unstyled";

import {DialogBox} from "./dialog-box";
import {useDialog} from "./use-dialog";

type DialogBoxProps = ComponentProps<typeof DialogBox>;

type ExposedDialogBoxProps = Omit<DialogBoxProps, "className">;

type Props = ExposedDialogBoxProps & {
  canCloseWithEsc?: boolean;
  isOpen?: boolean;
  onClose?: () => unknown;
};

const StyledDialog = styled(UnstyledDialog)`
  &::backdrop {
    background-color: ${getColorByAlias("bgDialogBackdrop")};
  }
`;

/**
 * You should probably use AlertDialog or ModalDialog instead
 */
const Dialog = ({canCloseWithEsc, isOpen, onClose, ...rest}: Props) => {
  const {dialogRef, handleClose, handleKeydown, isActuallyOpen} = useDialog({
    canCloseWithEsc,
    isOpen,
    onClose
  });

  return (
    <StyledDialog
      {...{canCloseWithEsc}}
      onClose={handleClose}
      onKeyDown={handleKeydown}
      ref={dialogRef}
    >
      {isActuallyOpen && <DialogBox {...{onClose}} {...rest} />}
    </StyledDialog>
  );
};

export {Dialog};
