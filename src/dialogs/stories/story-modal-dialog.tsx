import type {ComponentProps} from "react";

import {DialogActionButton} from "../dialog-action-button";
import {DialogFooter} from "../dialog-footer";
import {ModalDialog} from "../modal-dialog";

type ModalDialogProps = ComponentProps<typeof ModalDialog>;
type ExposedModalDialogProps = Omit<
  ModalDialogProps,
  "children" | "footer" | "isLoading"
>;
type Props = ExposedModalDialogProps & {
  content: string;
  hasFooter?: boolean;
};

const renderFooter = ({onClose}: Props) => (
  <DialogFooter
    cancelButton={
      <DialogActionButton actionType="cancel" onClick={onClose}>
        Cancel
      </DialogActionButton>
    }
    primaryButton={
      <DialogActionButton actionType="primary" autoFocus>
        Primary Action
      </DialogActionButton>
    }
  />
);

const StoryModalDialog = (props: Props) => {
  const {content, hasFooter, ...rest} = props;

  return (
    <ModalDialog
      {...rest}
      footer={(hasFooter || undefined) && renderFooter(props)}
    >
      {content}
    </ModalDialog>
  );
};

export {StoryModalDialog};
