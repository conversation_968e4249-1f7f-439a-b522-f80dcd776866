import {<PERSON><PERSON>} from "@phosphor-icons/react";
import type {ComponentProps} from "react";

import {AlertDialog} from "../alert-dialog";
import {DialogActionButton} from "../dialog-action-button";

type AlertDialogProps = ComponentProps<typeof AlertDialog>;
type ExposedAlertDialogProps = Pick<
  AlertDialogProps,
  "isLoading" | "isOpen" | "title"
>;

type Props = ExposedAlertDialogProps & {
  content: string;
  hasIcon?: boolean;
  onClose: () => unknown;
};

const StoryAlertDialog = (props: Props) => {
  const {content, hasIcon, onClose, ...rest} = props;

  return (
    <AlertDialog
      {...rest}
      cancelButton={
        <DialogActionButton actionType="cancel" onClick={onClose}>
          Cancel
        </DialogActionButton>
      }
      icon={(hasIcon || undefined) && Clover}
      primaryButton={
        <DialogActionButton actionType="primary" onClick={() => null}>
          Accept
        </DialogActionButton>
      }
    >
      {content}
    </AlertDialog>
  );
};

export {StoryAlertDialog};
