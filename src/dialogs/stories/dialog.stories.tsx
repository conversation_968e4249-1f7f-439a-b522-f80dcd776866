import {action} from "@storybook/addon-actions";
import type {Meta, StoryFn} from "@storybook/react";
import {useArray} from "@unlockre/utils-react/dist";
import {type ComponentProps, useState} from "react";
import styled from "styled-components";

import {OpenDialogButton} from "./open-dialog-button";
import {StoryAlertDialog} from "./story-alert-dialog";
import {StoryDialog} from "./story-dialog";
import {StoryModalDialog} from "./story-modal-dialog";

type StoryDialogProps = ComponentProps<typeof StoryDialog>;
type StoryAlertDialogProps = ComponentProps<typeof StoryAlertDialog>;

type Args = Omit<
  StoryDialogProps,
  "isOpen" | "onClose" | "onSiblingDialogOpen"
> &
  Pick<StoryAlertDialogProps, "isLoading"> & {
    description?: string;
    title: string;
  };

const openDialogAction = action("openDialog");

const closeDialogAction = action("closeDialog");

const ButtonsContainer = styled.div`
  display: flex;
  gap: 10px;
`;

const Default: StoryFn<Args> = args => {
  const [dialogIds, dialogIdsActions] = useArray<number>();
  const [isAlertDialogOpen, setIsAlertDialogOpen] = useState(false);
  const [isModalDialogOpen, setIsModalDialogOpen] = useState(false);

  const addDialogId = () => {
    const lastDialogId = dialogIds.reduce(
      (result, dialogId) => Math.max(result, dialogId),
      0
    );

    const newDialogId = lastDialogId + 1;

    dialogIdsActions.append(newDialogId);

    openDialogAction(newDialogId);
  };

  return (
    <ButtonsContainer>
      <OpenDialogButton label="Open Dialog" onClick={addDialogId} />
      {dialogIds.map(dialogId => (
        <StoryDialog
          {...args}
          key={dialogId}
          onClose={() => {
            dialogIdsActions.remove(dialogId);

            closeDialogAction(dialogId);
          }}
          onSiblingDialogOpen={addDialogId}
        />
      ))}
      <OpenDialogButton
        label="Open Alert Dialog"
        onClick={() => {
          setIsAlertDialogOpen(true);
        }}
      />
      <StoryAlertDialog
        {...args}
        isOpen={isAlertDialogOpen}
        onClose={() => setIsAlertDialogOpen(false)}
      />
      <OpenDialogButton
        label="Open Modal Dialog"
        onClick={() => setIsModalDialogOpen(true)}
      />
      <StoryModalDialog
        onClose={() => setIsModalDialogOpen(false)}
        {...args}
        isOpen={isModalDialogOpen}
      />
    </ButtonsContainer>
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://embed.figma.com/design/E8eTOTmhxUkhViwxXT2DfY/Components?node-id=140-408&m=dev&embed-host=share"
  }
};

const meta: Meta<Args> = {
  title: "dialog",
  argTypes: {
    canCloseWithEsc: {
      control: "boolean"
    },
    content: {
      control: "text"
    },
    description: {
      control: "text"
    },
    hasCloseButton: {
      control: "boolean"
    },
    hasFooter: {
      control: "boolean"
    },
    hasIcon: {
      control: "boolean"
    },
    hasSecondaryButton: {
      control: "boolean"
    },
    hasTertiaryButton: {
      control: "boolean"
    },
    isLoading: {
      control: "boolean"
    },
    title: {
      control: "text"
    }
  },
  args: {
    canCloseWithEsc: true,
    content: "This is the content of the dialog",
    description: "This is the description of the dialog",
    hasCloseButton: true,
    hasFooter: true,
    hasIcon: false,
    hasSecondaryButton: true,
    hasTertiaryButton: true,
    isLoading: false,
    title: "This is the title of the dialog"
  }
};

export {meta as default, Default};
