import {Clover} from "@phosphor-icons/react";
import {useState} from "react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {Dialog} from "../dialog";
import {DialogActionButton} from "../dialog-action-button";
import {DialogBody} from "../dialog-body";
import {DialogBodyText} from "../dialog-body-text";
import {DialogFooter} from "../dialog-footer";
import {DialogHeader} from "../dialog-header";
import {DialogIconBox} from "../dialog-icon-box";
import {ModalDialog} from "../modal-dialog";

import {OpenDialogButton} from "./open-dialog-button";

type DialogProps = ComponentProps<typeof Dialog>;

type ExposedDialogProps = Omit<
  DialogProps,
  "adornmentBox" | "children" | "className" | "footer" | "header" | "isOpen"
>;

type Props = ExposedDialogProps & {
  content: string;
  description?: string;
  hasCloseButton?: boolean;
  hasFooter?: boolean;
  hasIcon?: boolean;
  hasSecondaryButton?: boolean;
  hasTertiaryButton?: boolean;
  onSiblingDialogOpen: () => unknown;
  title: string;
};

const renderFooter = ({
  hasSecondaryButton,
  hasTertiaryButton,
  onClose
}: Props) => (
  <DialogFooter
    cancelButton={
      <DialogActionButton actionType="cancel" onClick={onClose}>
        Cancel
      </DialogActionButton>
    }
    primaryButton={
      <DialogActionButton actionType="primary" autoFocus>
        Primary Action
      </DialogActionButton>
    }
    secondaryButton={
      (hasSecondaryButton || undefined) && (
        <DialogActionButton actionType="secondary">
          Secondary Action
        </DialogActionButton>
      )
    }
    tertiaryButton={
      (hasTertiaryButton || undefined) && (
        <DialogActionButton actionType="tertiary">
          Tertiary Action
        </DialogActionButton>
      )
    }
  />
);

const StoryDialogBody = styled(DialogBody)`
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

// eslint-disable-next-line complexity
const StoryDialog = (props: Props) => {
  const {
    canCloseWithEsc,
    content,
    description,
    hasCloseButton,
    hasFooter,
    hasIcon,
    onClose,
    onSiblingDialogOpen,
    title
  } = props;

  const [isModalDialogOpen, setIsModalDialogOpen] = useState(false);
  return (
    <Dialog
      {...{canCloseWithEsc, hasCloseButton, onClose}}
      adornmentBox={(hasIcon || undefined) && <DialogIconBox icon={Clover} />}
      footer={(hasFooter || undefined) && renderFooter(props)}
      header={
        <DialogHeader
          description={description || undefined}
          hasCloseButton={hasCloseButton}
          onClose={onClose}
          title={title}
        />
      }
      isOpen
    >
      <StoryDialogBody>
        <OpenDialogButton
          label="Open Nested ModalDialog"
          onClick={() => setIsModalDialogOpen(true)}
        />
        <ModalDialog
          canCloseWithEsc
          isOpen={isModalDialogOpen}
          onClose={() => setIsModalDialogOpen(false)}
          title="Are you sure?"
        >
          This action is irreversible
        </ModalDialog>
        <OpenDialogButton
          label="Open Sibling Dialog"
          onClick={onSiblingDialogOpen}
        />
        <DialogBodyText>{content}</DialogBodyText>
      </StoryDialogBody>
    </Dialog>
  );
};

export {StoryDialog};
