import type {ReactElement} from "react";
import styled from "styled-components";

import {ensureDialogActionButtonWith} from "./dialog-action-button";
import {DialogSection} from "./dialog-section";

type Props = {
  cancelButton?: ReactElement;
  primaryButton: ReactElement;
  secondaryButton?: ReactElement;
  tertiaryButton?: ReactElement;
};

const RightContainer = styled.div`
  display: flex;
  gap: 8px;
  margin-left: auto;
`;

const Container = styled(DialogSection)`
  display: flex;
  gap: 12px;
  margin-top: 16px;
`;

const DialogFooter = ({
  cancelButton,
  primaryButton,
  secondaryButton,
  tertiaryButton
}: Props) => (
  <Container>
    {tertiaryButton && ensureDialogActionButtonWith(tertiaryButton, "tertiary")}
    <RightContainer>
      {cancelButton && ensureDialogActionButtonWith(cancelButton, "cancel")}
      {secondaryButton &&
        ensureDialogActionButtonWith(secondaryButton, "secondary")}
      {ensureDialogActionButtonWith(primaryButton, "primary")}
    </RightContainer>
  </Container>
);

export {DialogFooter};
