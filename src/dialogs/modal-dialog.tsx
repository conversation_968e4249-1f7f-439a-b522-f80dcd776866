import type {ComponentProps} from "react";
import styled from "styled-components";

import {Dialog} from "./dialog";
import {DialogHeader} from "./dialog-header";

type DialogProps = ComponentProps<typeof Dialog>;
type DialogHeaderProps = ComponentProps<typeof DialogHeader>;
type ExposedDialogHeaderProps = Pick<
  DialogHeaderProps,
  "description" | "title"
>;

type Props = ExposedDialogHeaderProps &
  Omit<DialogProps, "hasCloseButton" | "header" | "icon">;

const StyledDialogHeader = styled(DialogHeader)`
  margin-bottom: 16px;
`;

const ModalDialog = ({description, title, ...rest}: Props) => (
  <Dialog
    header={
      <StyledDialogHeader
        description={description}
        hasCloseButton
        onClose={rest.onClose}
        title={title}
      />
    }
    {...rest}
  />
);

export {ModalDialog};
