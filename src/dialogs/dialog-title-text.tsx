import styled from "styled-components";

import {getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledHeading} from "@/unstyled";

type Props = {
  children: string;
  className?: string;
};

const Container = styled(UnstyledHeading)`
  ${getTypography("title", "s", 500)}

  color: ${getColorByAlias("text")};
`;

const DialogTitleText = (props: Props) => <Container {...props} level={3} />;

export {DialogTitleText};
