import {useEffect} from "react";

// TODO: Update the one we have in @unlockre/utils-react with this one
const useElementEvent = <
  TElement extends HTMLElement,
  TEventName extends keyof HTMLElementEventMap
>(
  element: TElement | null,
  eventName: TEventName,
  listener?: (event: HTMLElementEventMap[TEventName]) => void
) =>
  useEffect(() => {
    if (!element || !listener) {
      return;
    }

    element.addEventListener(eventName, listener);

    return () => {
      element.removeEventListener(eventName, listener);
    };
  }, [element, eventName, listener]);

export {useElementEvent};
