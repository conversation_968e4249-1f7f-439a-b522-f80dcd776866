import {X} from "@phosphor-icons/react";
import styled from "styled-components";

import {IconWithColor} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

type Props = {
  onClose: () => unknown;
};

const Container = styled(UnstyledButton)`
  padding: 4px;
`;

const CloseDialogButton = ({onClose}: Props) => (
  <Container onClick={onClose}>
    <IconWithColor
      getColor={getColorByAlias("iconAccent")}
      icon={X}
      size={16}
      weight="regular"
    />
  </Container>
);

export {CloseDialogButton};
