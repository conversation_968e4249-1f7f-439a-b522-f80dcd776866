import type {ReactNode} from "react";
import styled, {css} from "styled-components";

type Props = {
  children: ReactNode;
  className?: string;
  noHorizontalPadding?: boolean;
};

type ContainerStyledProps = {
  $hasHorizontalPadding?: boolean;
};

const dialogSectionHorizontalPadding = 24;

const containerHorizontalPaddingCss = css`
  padding: 0 ${dialogSectionHorizontalPadding}px;
`;

const Container = styled.div<ContainerStyledProps>`
  ${props => props.$hasHorizontalPadding && containerHorizontalPaddingCss}
`;

const DialogSection = ({children, className, noHorizontalPadding}: Props) => (
  <Container $hasHorizontalPadding={!noHorizontalPadding} {...{className}}>
    {children}
  </Container>
);

export {DialogSection, dialogSectionHorizontalPadding};
