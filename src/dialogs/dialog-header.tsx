import type {ReactElement} from "react";
import styled from "styled-components";

import {CloseDialogButton} from "./close-dialog-button";
import {DialogDescriptionText} from "./dialog-description-text";
import {DialogSection} from "./dialog-section";
import {DialogTitleText} from "./dialog-title-text";
import {renderElementOrString} from "./render-element-or-string";

type Props = {
  className?: string;
  description?: ReactElement | string;
  hasCloseButton?: boolean;
  onClose?: () => unknown;
  title: ReactElement | string;
};

const LeftContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const Container = styled(DialogSection)`
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
  gap: 10px;
`;

const DialogHeader = ({
  className,
  description,
  hasCloseButton,
  onClose,
  title
}: Props) => (
  <Container className={className}>
    <LeftContainer>
      {renderElementOrString(title, title => (
        <DialogTitleText>{title}</DialogTitleText>
      ))}
      {renderElementOrString(description, description => (
        <DialogDescriptionText>{description}</DialogDescriptionText>
      ))}
    </LeftContainer>
    {hasCloseButton && onClose && <CloseDialogButton {...{onClose}} />}
  </Container>
);

export {DialogHeader};
