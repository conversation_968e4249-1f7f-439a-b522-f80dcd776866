import {useCallback, useEffect, useRef, useState} from "react";
import type {KeyboardEvent<PERSON><PERSON><PERSON>, ReactEventHandler, RefObject} from "react";

type Params = {
  canCloseWithEsc?: boolean;
  isOpen?: boolean;
  onClose?: () => unknown;
};

type DialogRef = RefObject<HTMLDialogElement>;

const useToggleDialog = ({isOpen}: Params, dialogRef: DialogRef) => {
  // We are using this boolean to postpone the rendering of DialogBox until the
  // dialog is actually open as this is needed to allow any of the children to
  // auto focus
  const [isActuallyOpen, setIsActuallyOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }

    setIsActuallyOpen(isOpen ?? false);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  return {isActuallyOpen};
};

const useDialog = (params: Params) => {
  const {canCloseWithEsc, onClose} = params;

  const dialogRef = useRef<HTMLDialogElement>(null);

  const {isActuallyOpen} = useToggleDialog(params, dialogRef);

  const handleClose: ReactEventHandler<HTMLDialogElement> = useCallback(
    event => {
      // We need this to solve a react issue that bubbles up this event where it
      // shouldn't (closes event doesn't bubble) and closes any parent dialogs
      event.stopPropagation();
    },
    []
  );

  // This should be done by preventing the default in the modal cancel event,
  // but unfortunately this doesn't work because react bubbles up the Escape
  // keydown event and closes any parent dialogs, this is why we have to
  // stop propagation here
  const handleKeydown: KeyboardEventHandler<HTMLDialogElement> = useCallback(
    event => {
      if (event.key !== "Escape") {
        return;
      }

      event.stopPropagation();

      if (canCloseWithEsc) {
        onClose?.();
      } else {
        event.preventDefault();
      }
    },
    [canCloseWithEsc, onClose]
  );

  return {isActuallyOpen, dialogRef, handleClose, handleKeydown};
};

export {useDialog};
