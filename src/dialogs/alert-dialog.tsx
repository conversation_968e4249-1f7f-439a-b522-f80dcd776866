import type {Icon as IconComponent} from "@phosphor-icons/react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {Dialog} from "./dialog";
import {DialogFooter} from "./dialog-footer";
import {DialogHeader} from "./dialog-header";
import {DialogIconBox} from "./dialog-icon-box";
import {DialogSpinnerBox} from "./dialog-spinner-box";

type DialogProps = ComponentProps<typeof Dialog>;
type DialogHeaderProps = ComponentProps<typeof DialogHeader>;
type ExposedDialogProps = Pick<DialogProps, "isOpen">;
type ExposedDialogHeaderProps = Pick<DialogHeaderProps, "title">;
type DialogFooterProps = ComponentProps<typeof DialogFooter>;

type RenderAdornmentBoxParams = {
  icon?: IconComponent;
  isLoading?: boolean;
};

type Props = DialogFooterProps &
  ExposedDialogHeaderProps &
  ExposedDialogProps &
  RenderAdornmentBoxParams & {
    children?: string | false | null;
  };

const StyledDialog = styled(Dialog)`
  min-width: 400px;
  max-width: 560px;
`;

const StyledDialogHeader = styled(DialogHeader)`
  margin-bottom: 8px;
`;

const renderAdornmentBox = ({icon, isLoading}: RenderAdornmentBoxParams) =>
  icon && !isLoading ? <DialogIconBox {...{icon}} /> : <DialogSpinnerBox />;

const AlertDialog = ({
  children,
  icon,
  isLoading,
  isOpen,
  title,
  ...rest
}: Props) => (
  <StyledDialog
    {...{isOpen}}
    adornmentBox={(icon || isLoading) && renderAdornmentBox({icon, isLoading})}
    footer={<DialogFooter {...rest} />}
    header={<StyledDialogHeader {...{title}} />}
  >
    {children}
  </StyledDialog>
);

export {AlertDialog};
