import type {Icon as IconComponent} from "@phosphor-icons/react";

import {IconWithColor} from "@/icons";
import {getColorByAlias} from "@/theme-provider/theme";

import {DialogAdornmentBox} from "./dialog-adornment-box";

type Props = {
  icon: IconComponent;
};

const getDialogIconColor = getColorByAlias("iconAccent");

const DialogIconBox = (props: Props) => (
  <DialogAdornmentBox>
    <IconWithColor
      {...props}
      getColor={getDialogIconColor}
      size={32}
      weight="regular"
    />
  </DialogAdornmentBox>
);

export {DialogIconBox};
