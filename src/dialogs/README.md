# dialogs

The following components implement the two main types of dialogs:

- [AlertDialog](#alertdialog)
- [ModalDialog](#modaldialog)

> There is also a [Dialog](./dialog.tsx) component available that can be used for cases not covered by the two main types.

- All dialogs implement a focus trap mechanism provided by the native html [dialog](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/dialog) element.
- None of the dialogs support closing by clicking outside the dialog.
- If text is given to any of the dialog components, it will be rendered inside a `DialogBodyText` component wrapped inside a `DialogBody` (which provides an horizontal padding).

## AlertDialog

### About

This component is used to ask for user confirmation before taking an action. It doesn't have a close button and it has a mandatory footer with at least a primary and a cancel action buttons (it could have a secondary and tertiary action buttons as well).

### Examples

```tsx
import {AlertDialog, DialogActionButton} from "@unlockre/components-library/dist/dialogs";
import {Delete} from "@phosphor-icons/react";

type Props = {
  isOpen: boolean;
  onClose: () => unknown;
  onDelete: () => unknown;
};

const DeleteDialog = ({isOpen, onClose, onDelete}: Props) => (
  <AlertDialog
    {...{isOpen, onClose}}
    // Passing an icon is optional
    icon={Delete}
    cancelActionButton={
      <DialogActionButton actionType="cancel" onClick={onClose}>
        Cancel
      </DialogActionButton>
    }
    primaryActionButton={
      <DialogActionButton actionType="primary" onClick={onDelete}>
        Delete
      </DialogActionButton>
    }
    title="Are you sure you want to delete this item?"
  >
    This item will be permanently deleted.
  </AlertDialog>
);
```

## ModalDialog

### About

This component can be used to show any content in a modal.

It has a close button and can or not have a footer with action buttons.

It can be closed using the `Esc` key if `canCloseWithEsc` prop is set to `true`.

### Examples

```tsx
import {
  DialogActionButton,
  DialogBody,
  DialogFooter,
  ModalDialog
} from "@unlockre/components-library/dist/dialogs";

type Props = {
  isOpen: boolean;
  onClose: () => unknown;
  onSave: () => unknown;
};

  /* Some styles */
const EditModalBody = styled(DialogBody)``;

const EditModal = (props: Props) => (
  <ModalDialog
    {...{isOpen, onClose}}
    canCloseWithEsc
    // footer is optional
    footer={
      <DialogFooter
        cancelActionButton={
          <DialogActionButton actionType="cancel" onClick={onClose}>
            Cancel
          </DialogActionButton>
        }
        primaryActionButton={
          <DialogActionButton actionType="primary" onClick={onSave}>
            Save
          </DialogActionButton>
        }
      />
    }
  >
    <EditModalBody noHorizontalPadding>{/* Some content */}</EditModalBody>
  </ModalDialog>
);
```
