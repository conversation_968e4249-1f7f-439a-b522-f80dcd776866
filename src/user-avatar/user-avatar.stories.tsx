// TODO: Upgrade storybook to v7 so we could install the addon for nextjs which allows rendering an Image component from next
// (https://www.npmjs.com/package/@storybook/nextjs)

import type {Meta, StoryFn} from "@storybook/react";

import UserAvatar from "./user-avatar";

type Args = {
  size: number;
  userFirstName?: string;
  userLastName?: string;
  withUserPictureUrl?: boolean;
};

const userPictureUrl =
  "https://e0.pxfuel.com/wallpapers/1006/73/desktop-wallpaper-neytiri-avatar-avatar-face.jpg";

const Default: StoryFn<Args> = ({withUserPictureUrl, ...rest}) => (
  <UserAvatar
    {...rest}
    userPictureUrl={withUserPictureUrl ? userPictureUrl : undefined}
  />
);

const meta: Meta<Args> = {
  title: "user-avatar",
  argTypes: {
    size: {
      control: "number"
    },
    userFirstName: {
      control: "text"
    },
    userLastName: {
      control: "text"
    },
    withUserPictureUrl: {
      control: "boolean"
    }
  },
  args: {
    size: 40,
    userFirstName: "John",
    userLastName: "Doe",
    withUserPictureUrl: true
  }
};

export {meta as default, Default};
