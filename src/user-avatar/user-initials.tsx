import styled from "styled-components";

import {getColor, getColorByAlias} from "@/theme-provider/theme";

type Props = {
  className?: string;
  size: number;
  userFirstName: string;
  userLastName: string;
};

type ContainerStyledProps = {
  $size: number;
};

const getNameInitial = (name: string) => name[0].toUpperCase();

const fontSizeVsSize = 0.3;

const Container = styled.div<ContainerStyledProps>`
  align-items: center;
  background-color: ${getColor("blue", "200")};
  border-radius: 50%;
  color: ${getColorByAlias("accentPrimary")};
  display: flex;
  font-family: "Inter Variable";
  font-size: ${props => props.$size * fontSizeVsSize}px;
  font-weight: 600;
  height: ${props => props.$size}px;
  justify-content: center;
  width: ${props => props.$size}px;
`;

const UserInitials = ({
  className,
  size,
  userFirstName,
  userLastName
}: Props) => (
  <Container {...{className}} $size={size}>
    {getNameInitial(userFirstName)}
    {getNameInitial(userLastName)}
  </Container>
);

export default UserInitials;
