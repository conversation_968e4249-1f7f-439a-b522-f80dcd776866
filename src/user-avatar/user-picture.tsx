import Image from "next/image";
import styled from "styled-components";

type Props = {
  className?: string;
  size: number;
  userPictureUrl: string;
};

const StyledImage = styled(Image)`
  border-radius: 50%;
`;

const UserPicture = ({className, size, userPictureUrl}: Props) => (
  <StyledImage
    {...{className}}
    alt="User Picture"
    height={size}
    objectFit="cover"
    src={userPictureUrl}
    width={size}
  />
);

export default UserPicture;
