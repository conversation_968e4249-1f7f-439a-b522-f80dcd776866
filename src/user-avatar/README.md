# User Avatar

## About

This component renders a user picture or initials if no picture is given.

> If both things are given (picture and initials), the component will render the user picture.

## Examples

### Example with picture

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import UserAvatar from "@unlockre/components-library/dist/user-avatar";

const ExampleWithPicture = () => (
  <ThemeProvider>
    <UserAvatar
      size={40}
      userPictureUrl="https://e0.pxfuel.com/wallpapers/1006/73/desktop-wallpaper-neytiri-avatar-avatar-face.jpg"
    />
  </ThemeProvider>
);

export default WithoutActionButtons;
```

### Example with initials (without picture)

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import UserAvatar from "@unlockre/components-library/dist/user-avatar";

const ExampleWithInitials = () => (
  <ThemeProvider>
    <UserAvatar
      size={40}
      userFirstName="John"
      userLastName="Doe"
    />
  </ThemeProvider>
);

export default WithActionButtons;
```