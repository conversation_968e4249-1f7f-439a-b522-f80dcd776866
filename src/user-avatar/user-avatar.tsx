import {ensureIsDefined} from "@unlockre/utils-validation/dist";

import UserInitials from "./user-initials";
import UserPicture from "./user-picture";

type Props = {
  className?: string;
  size: number;
  userFirstName?: string;
  userLastName?: string;
  userPictureUrl?: string;
};

const renderUserInitials = (
  size: number,
  userFirstName: string,
  userLastName: string,
  className?: string
) => <UserInitials {...{className, size, userFirstName, userLastName}} />;

const renderUserPicture = (
  size: number,
  userPictureUrl: string,
  className?: string
) => <UserPicture {...{className, size, userPictureUrl}} />;

const getEnsureErrorMessage = (propName: string) =>
  `Expected ${propName} to be defined when no userPictureUrl is given`;

const ensureUserLastName = (userLastName?: string) =>
  ensureIsDefined(userLastName, getEnsureErrorMessage("userLastName"));

const ensureUserFirstName = (userFirstName?: string) =>
  ensureIsDefined(userFirstName, getEnsureErrorMessage("userFirstName"));

const UserAvatar = ({
  className,
  size,
  userFirstName,
  userLastName,
  userPictureUrl
}: Props) =>
  userPictureUrl
    ? renderUserPicture(size, userPictureUrl, className)
    : renderUserInitials(
        size,
        ensureUserFirstName(userFirstName),
        ensureUserLastName(userLastName),
        className
      );

export default UserAvatar;
