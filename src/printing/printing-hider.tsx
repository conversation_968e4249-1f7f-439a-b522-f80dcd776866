import {useIsPrintView} from "@unlockre/utils-react/dist";
import type {ReactNode} from "react";

type Props = {
  children: ReactNode;
};

const PrintingHider = ({children}: Props) => {
  const isPrintView = useIsPrintView();

  // We need the fragment to ensure the return type to be JSX.Element, so we
  // can use this component to the main container of another
  return <>{isPrintView ? null : children}</>;
};

export {PrintingHider};
