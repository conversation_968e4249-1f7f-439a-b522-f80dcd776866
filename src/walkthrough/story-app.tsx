import styled from "styled-components";

import Button from "@/button";
import {getTypography} from "@/theme-provider/theme";

import useWalkthrough from "./use-walkthrough";
import * as withWalkthroughStep from "./walkthrough-step";

const Container = styled.div``;

const SomeComponent = styled.span`
  ${getTypography("body", "s", 600)};

  margin-right: 16px;
`;

const StoryApp = () => {
  const {setIsWalkthroughOpen, setWalkthroughStep} = useWalkthrough();

  const onWalkthroughStart = () => {
    setWalkthroughStep(0);
    setIsWalkthroughOpen(true);
  };

  return (
    <Container>
      <SomeComponent className={withWalkthroughStep.getClassNameFrom(0)}>
        First step
      </SomeComponent>
      <SomeComponent className={withWalkthroughStep.getClassNameFrom(1)}>
        Second step
      </SomeComponent>
      <SomeComponent className={withWalkthroughStep.getClassNameFrom(2)}>
        Last step
      </SomeComponent>

      <Button onClick={onWalkthroughStart} size="medium" variant="primary">
        Start the walkthrough!
      </Button>
    </Container>
  );
};

export default StoryApp;
