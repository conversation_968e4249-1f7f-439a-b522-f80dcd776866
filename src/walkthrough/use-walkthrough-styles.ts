import type {ProviderProps as TourProviderProps} from "@reactour/tour";
import {useTheme} from "styled-components";

type WalkthroughStyles = NonNullable<TourProviderProps["styles"]>;

const useWalkthroughStyles = () => {
  const theme = useTheme();

  const walkthroughStyles: WalkthroughStyles = {
    popover: base => ({
      ...base,
      padding: 0,
      borderRadius: "6px",
      background: theme.colors.aliases.accentPrimary
    }),
    maskArea: base => ({...base, rx: 10}),
    maskWrapper: base => ({...base, color: "#000000", opacity: 0.5})
  };

  return walkthroughStyles;
};

export default useWalkthroughStyles;
