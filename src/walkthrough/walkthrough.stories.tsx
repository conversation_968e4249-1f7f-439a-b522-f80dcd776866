import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";

import StoryApp from "./story-app";
import Walkthrough from "./walkthrough";

type WalkthroughProps = ComponentProps<typeof Walkthrough>;

type ExposedWalkthroughProps = Pick<WalkthroughProps, "steps">;

type Args = ExposedWalkthroughProps & {
  isOpened: boolean;
};

const Default: StoryFn<Args> = args => {
  const [, updateArgs] = useArgs();

  const onClose = () => updateArgs({isOpened: false});

  return (
    <Walkthrough {...{onClose}} {...args}>
      <StoryApp />
    </Walkthrough>
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=1341%3A3316"
  }
};

const meta: Meta<Args> = {
  title: "walkthrough",
  argTypes: {
    isOpened: {
      control: "boolean"
    },
    steps: {
      control: "object"
    }
  },
  args: {
    isOpened: true,
    steps: [
      {
        description: "This is the content of the first step",
        title: "First Step"
      },
      {
        description: "This is the content of the second step",
        title: "Second Step"
      },
      {
        description: "This is the content of the last step",
        title: "Last Step"
      }
    ]
  }
};

export {meta as default, Default};
