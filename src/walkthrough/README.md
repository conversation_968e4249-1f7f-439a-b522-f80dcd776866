# Walkthrough

## About

This component implements the [React Tour](https://github.com/elrumordelaluz/reactour/tree/master/packages/tour) Component.

It allows to create a guided walkthrough for the users with step-by-step tooltips.

## Usage

In order to use this package, for the moment you will need to add the following resolution to your `package.json`.

```json
  "resolutions": {
    "@reactour/utils": "npm:@reactour/utils@^0.3.1"
  }
```

## Example

```tsx
import styled from "styled-components";

import Button from "@unlockre/components-library/dist/button";

import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import {getTypography} from "@unlockre/components-library/dist/theme-provider/theme";

import Walkthrough from "@unlockre/components-library/dist/walkthrough";

const Container = styled.div``;

const SomeComponent = styled.span`
  ${getTypography("body", "s", 600)};
  margin-right: 16px;
`;

const Component = () => {
  const {setIsWalkthroughOpen, setWalkthroughStep} = useWalkthrough();

  const onWalkthroughStart = () => {
    setWalkthroughStep(0);
    setIsWalkthroughOpen(true);
  };

  return (
    <Container>
      <SomeComponent className={withWalkthroughStep.getClassNameFrom(0)}>
        First step
      </SomeComponent>
      <SomeComponent className={withWalkthroughStep.getClassNameFrom(1)}>
        Second step
      </SomeComponent>
      <SomeComponent className={withWalkthroughStep.getClassNameFrom(2)}>
        Last step
      </SomeComponent>

      <Button onClick={onWalkthroughStart} size="medium" variant="primary">
        Start the walkthrough!
      </Button>
    </Container>
  );
};

const steps = [
  {
    description: "This is the content of the first step",
    title: "First Step",
  },
  {
    description: "This is the content of the second step",
    title: "Second Step",
  },
  {
    description: "This is the content of the last step",
    title: "Last Step",
  },
];

const Application = () => (
  <ThemeProvider>
    <Walkthrough onClose={() => console.log("walkthrough was closed")} steps={steps}>
      <Component />
    </Walkthrough>
  </ThemeProvider>
);

export default Application;
```
