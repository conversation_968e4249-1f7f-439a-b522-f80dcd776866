import {TourProvider} from "@reactour/tour";
import type {StepType} from "@reactour/tour";
import type {ContentProps} from "@reactour/tour/dist/types";
import {useMutationObserver} from "@unlockre/utils-react/dist";
import {useCallback, useMemo, useState} from "react";
import type {ReactNode} from "react";

import useWalkthroughStyles from "./use-walkthrough-styles";
import * as withWalkthroughStep from "./walkthrough-step";
import type {WalkthroughStep} from "./walkthrough-step";
import WalkthroughTooltip from "./walkthrough-tooltip";

type Props = {
  children: ReactNode;
  className?: string;
  onClose: () => void;
  steps: WalkthroughStep[];
};

const renderWalkthroughTooltip = (
  {currentStep, setCurrentStep, setIsOpen}: ContentProps,
  walkthroughSteps: WalkthroughStep[]
) => (
  <WalkthroughTooltip
    currentStepIndex={currentStep}
    onClose={() => setIsOpen(false)}
    onNextStep={() => setCurrentStep(step => step + 1)}
    onRestart={() => setCurrentStep(0)}
    steps={walkthroughSteps}
  />
);

const getStep = (
  walkthroughSteps: WalkthroughStep[],
  index: number
): StepType => ({
  content: contentProps =>
    renderWalkthroughTooltip(contentProps, walkthroughSteps),
  selector: "." + withWalkthroughStep.getClassNameFrom(index)
});

const doNothing = () => undefined;

const areStepsReady = (containerElement: HTMLElement, tourSteps: StepType[]) =>
  tourSteps.every(tourStep =>
    containerElement.querySelector(tourStep.selector as string)
  );

const Walkthrough = ({children, className, onClose, steps}: Props) => {
  const [shouldRender, setShouldRender] = useState(false);

  const [containerElement, setContainerElement] = useState<HTMLElement | null>(
    null
  );

  const walkThroughstyles = useWalkthroughStyles();

  const tourSteps = useMemo(
    () => steps.map((walkthroughStep, index) => getStep(steps, index)),
    [steps]
  );

  const callback: MutationCallback = useCallback(() => {
    if (containerElement && areStepsReady(containerElement, tourSteps)) {
      setShouldRender(true);
    }
  }, [containerElement, tourSteps]);

  useMutationObserver(containerElement, callback, {
    childList: true,
    subtree: true
  });

  return (
    <div ref={setContainerElement} {...{className}}>
      {shouldRender ? (
        <TourProvider
          beforeClose={onClose}
          onClickMask={doNothing}
          showBadge={false}
          showCloseButton={false}
          showNavigation={false}
          showPrevNextButtons={false}
          steps={tourSteps}
          styles={walkThroughstyles}
        >
          {children}
        </TourProvider>
      ) : (
        children
      )}
    </div>
  );
};

export default Walkthrough;
