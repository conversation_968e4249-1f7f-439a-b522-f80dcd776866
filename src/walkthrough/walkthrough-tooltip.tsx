import styled from "styled-components";

import Button from "@/button";
import {getColor, getTypography} from "@/theme-provider/theme";

import type {WalkthroughStep} from "./walkthrough-step";

type Props = {
  currentStepIndex: number;
  onClose: () => unknown;
  onNextStep: () => unknown;
  onRestart: () => unknown;
  steps: WalkthroughStep[];
};

const Container = styled.div`
  padding: 16px;
`;

const TitleAndDescriptionContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const Title = styled.span`
  ${getTypography("body", "s", 600)};
  color: ${getColor("gray", "000")};
  margin-bottom: 4px;
`;

const Description = styled.span`
  ${getTypography("body", "s", 400)};
  color: ${getColor("gray", "000")};
`;

const StepsAndNavigationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  margin-top: 16px;
`;

const Steps = styled.span`
  ${getTypography("body", "xs", 400)};
  color: ${getColor("gray", "000")};
`;

const TransparentButton = styled(Button)`
  margin-right: 8px;
  color: ${getColor("gray", "000")};
`;

const isLastStep = ({currentStepIndex, steps}: Props) =>
  currentStepIndex === steps.length - 1;

const renderLastStepButtons = ({onClose, onRestart}: Props) => (
  <div>
    <TransparentButton onClick={onRestart} size="small" variant="transparent">
      Restart
    </TransparentButton>
    <Button onClick={onClose} size="small" variant="secondary">
      Finish
    </Button>
  </div>
);

const renderStepButtons = ({onClose, onNextStep}: Props) => (
  <div>
    <TransparentButton onClick={onClose} size="small" variant="transparent">
      Skip
    </TransparentButton>
    <Button onClick={onNextStep} size="small" variant="secondary">
      Next
    </Button>
  </div>
);

const renderButtons = (props: Props) =>
  isLastStep(props) ? renderLastStepButtons(props) : renderStepButtons(props);

const WalkthroughTooltip = (props: Props) => {
  const {currentStepIndex, steps} = props;
  const {description, title} = steps[currentStepIndex];

  return (
    <Container>
      <TitleAndDescriptionContainer>
        <Title>{title}</Title>
        <Description>{description}</Description>
      </TitleAndDescriptionContainer>
      <StepsAndNavigationContainer>
        <Steps>{`${currentStepIndex + 1} of ${steps.length}`}</Steps>
        {renderButtons(props)}
      </StepsAndNavigationContainer>
    </Container>
  );
};

export default WalkthroughTooltip;
