import type {Meta, StoryFn} from "@storybook/react";
import {ExpandLess} from "@styled-icons/material";

import Pill from "./pill";

const storyParameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=1598%3A10220"
  }
};

const Default: StoryFn<typeof Pill> = args => <Pill {...args} />;

Default.parameters = storyParameters;

const WithRightIcon: StoryFn<typeof Pill> = args => (
  <Pill
    {...args}
    rightElement={<ExpandLess size="24" />}
    type="information"
    variant="primary"
  />
);

WithRightIcon.parameters = storyParameters;

const WithLeftIcon: StoryFn<typeof Pill> = args => (
  <Pill
    {...args}
    leftElement={<ExpandLess size="24" />}
    type="information"
    variant="primary"
  />
);

WithLeftIcon.parameters = storyParameters;

const meta: Meta<typeof Pill> = {
  title: "pill",
  argTypes: {
    label: {
      control: "text"
    },
    type: {
      control: "radio",
      options: [
        "error",
        "information",
        "success",
        "warning",
        "blue",
        "black",
        "gray",
        "lightBlue"
      ]
    },
    variant: {
      control: "radio",
      options: ["primary", "secondary", "inverted"]
    }
  },
  args: {
    label: "Some Label",
    type: "information",
    variant: "primary"
  }
};

export {meta as default, Default, WithRightIcon, WithLeftIcon};
