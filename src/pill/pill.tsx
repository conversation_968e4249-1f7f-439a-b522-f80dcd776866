import type {ReactChild} from "react";
import styled, {css} from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";

type PillType =
  | "black"
  | "blue"
  | "error"
  | "gray"
  | "information"
  | "lightBlue"
  | "success"
  | "warning";

type PillVariant = "inverted" | "primary" | "secondary";

type PillHeightVariant = "short" | "tall";

type Props = {
  className?: string;
  heightVariant?: PillHeightVariant;
  label: string;
  leftElement?: ReactChild;
  rightElement?: ReactChild;
  type: PillType;
  variant: PillVariant;
};

type ContainerStyledProps = {
  $heightVariant?: PillHeightVariant;
  $type: PillType;
  $variant: PillVariant;
};

const LabelContainer = styled.span`
  ${getTypography("body", "xs", 600)};
`;

const colors = {
  information: {
    primary: css`
      background: ${getColorByAlias("accentPrimary")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("blue", "150")};
      color: ${getColorByAlias("accentPrimary")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColorByAlias("accentPrimary")};
    `
  },
  success: {
    primary: css`
      background: ${getColorByAlias("feedbackSuccess")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("green", "150")};
      color: ${getColorByAlias("feedbackSuccess")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColorByAlias("feedbackSuccess")};
    `
  },
  warning: {
    primary: css`
      background: ${getColorByAlias("feedbackWarning")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("orange", "100")};
      color: ${getColorByAlias("feedbackWarning")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColorByAlias("feedbackWarning")};
    `
  },
  error: {
    primary: css`
      background: ${getColorByAlias("feedbackError")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("red", "100")};
      color: ${getColorByAlias("feedbackError")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColorByAlias("feedbackError")};
    `
  },
  blue: {
    primary: css`
      background: ${getColor("blue", "900")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("blue", "400")};
      color: ${getColor("blue", "900")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColor("blue", "900")};
    `
  },
  black: {
    primary: css`
      background: ${getColor("gray", "900")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("gray", "040")};
      color: ${getColorByAlias("textPrimary")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColorByAlias("textPrimary")};
    `
  },
  gray: {
    primary: css`
      background: ${getColor("gray", "550")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("gray", "100")};
      color: ${getColor("gray", "550")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColor("gray", "550")};
    `
  },
  lightBlue: {
    primary: css`
      background: ${getColor("blue", "450")};
      color: ${getColorByAlias("textInverted")};
    `,
    secondary: css`
      background: ${getColor("blue", "100")};
      color: ${getColor("blue", "450")};
    `,
    inverted: css`
      background: ${getColorByAlias("backgroundWhite")};
      color: ${getColor("blue", "450")};
    `
  }
};

const Container = styled.div<ContainerStyledProps>`
  ${props => colors[props.$type]?.[props.$variant]};

  height: ${props => (props.$heightVariant === "tall" ? 32 : 24)}px;
  align-items: center;
  border-radius: 12px;
  display: flex;
  gap: 4px;
  justify-content: space-between;
  padding: 0px 8px;
  width: fit-content;
`;

const Pill = ({
  className,
  heightVariant,
  label,
  leftElement,
  rightElement,
  type,
  variant
}: Props) => (
  <Container
    $heightVariant={heightVariant}
    $type={type}
    $variant={variant}
    className={className}
  >
    {leftElement}
    <LabelContainer>{label}</LabelContainer>
    {rightElement}
  </Container>
);

export default Pill;
export type {PillType, PillVariant};
