# ToggleButtonGroup

## API

### ToggleButtonGroup

#### About

This component receives a list of items and display them as a list where only one item can be selected at the same time.

It also allows to remove an item when `isRemovingItems` boolean prop is true.

#### Types

```tsx

type ToggleButtonSize = "large" | "medium" | "small" | "xsmall";

type ToggleButtonGroupProps<TItem> = {
  className?: string;
  getItemLabel: (item: TItem) => string;
  isRemovingItems?: boolean;
  items: TItem[];
  onItemRemove?: (value: TItem) => unknown;
  onItemSelect: (value: TItem | undefined) => unknown;
  selectedItem?: TItem;
  size: ToggleButtonSize;
};

```

#### Example

```tsx
import {ThemeProvider} from "@unlockre/components-library/dist/theme-provider";
import ToggleButtonGroup, {useToggleButtonGroup} from "@unlockre/components-library/dist/toggle-button-group";

type ToggleButtonItem = {
  label: string;
};

const toggle = (value: boolean) => !value;

const getToggleButtonLabel = (item: ToggleButtonItem) => item.label;

const items = [
  {label: "one"},
  {label: "two"},
  {label: "three"}
];

const MyApp = () => {
  const {setIsRemovingItems, toggleButtonGroupProps} = useToggleButtonGroup<ToggleButtonItem>({
    initialItems: items,
    initialIsRemovingItems: false,
    initialSelectedItem: items[0]
  });

  return (
    <ThemeProvider>
      <ToggleButtonGroup
        {...toggleButtonGroupProps}
        getItemLabel={getToggleButtonLabel}
        size="large"
      />

      <button onClick={() => setIsRemovingItems(toggle)}>Toggle Removing Items</button>
    </ThemeProvider>
  )
};

export default MyApp;
```