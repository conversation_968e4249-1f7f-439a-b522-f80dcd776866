import styled from "styled-components";

import {getColor} from "@/theme-provider/theme";

import type {ToggleButtonIconSize} from "./types";

type Props = {
  size: ToggleButtonIconSize;
};

// TODO: Remove this once we have fixed the svg
const StyledSVG = styled.svg`
  margin-top: ${props => (props.height === 24 ? 4 : -3)}px;
`;

const StyledPath = styled.path`
  stroke: ${getColor("gray", "000")};
`;

const CheckmarkIcon = ({size}: Props) => (
  <StyledSVG
    fill="none"
    height={size}
    viewBox={`0 0 ${size} ${size}`}
    width={size}
    xmlns="http://www.w3.org/2000/svg"
  >
    {size === 24 ? (
      <StyledPath
        d="M20 6L9 17L4 12"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    ) : (
      <StyledPath
        d="M13.6667 5L6.33333 12.3333L3 9"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    )}
  </StyledSVG>
);

export default CheckmarkIcon;
