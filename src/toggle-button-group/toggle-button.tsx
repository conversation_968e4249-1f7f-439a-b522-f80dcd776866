import type {ReactElement} from "react";
import styled, {css} from "styled-components";

import {getColor, getColorByAlias, getTypography} from "@/theme-provider/theme";
import {UnstyledButton} from "@/unstyled";

import type {ToggleButtonIconSize} from "./types";

type Size = "large" | "medium" | "small" | "xsmall";

type StyledRightElementContainerProps = {
  size: Size;
};

type StyledContainerProps = {
  isRemovingItems?: boolean;
  isSelected?: boolean;
  onClick: () => unknown;
  size: Size;
};

type Props = StyledContainerProps & {
  label: string;
  renderRightIcon?: (size: ToggleButtonIconSize) => ReactElement | undefined;
};

const getRightIconSizeValue = (size: Size) =>
  size === "large" || size === "medium" ? 24 : 14;

const rightElementMarginLefts = {
  large: 8,
  medium: 8,
  small: 4,
  xsmall: 4
};

const RightIconContainer = styled.div<StyledRightElementContainerProps>`
  display: flex;
  margin-left: ${props => rightElementMarginLefts[props.size]}px;
`;

const renderRightElement = (
  size: Size,
  renderRightIcon?: Props["renderRightIcon"]
) => {
  const rightIconElement = renderRightIcon?.(getRightIconSizeValue(size));

  return (
    rightIconElement && (
      <RightIconContainer size={size}>{rightIconElement}</RightIconContainer>
    )
  );
};

const nonSelectedAndRemovedCss = css`
  background: ${getColorByAlias("backgroundWhite")};
  border: 1px solid ${getColor("gray", "250")};
  color: ${getColorByAlias("textPrimary")};
`;

const nonSelectedCss = css`
  ${nonSelectedAndRemovedCss}

  :hover {
    background: ${getColorByAlias("accentSecondary")};
    border: 1px solid ${getColorByAlias("accentSecondary")};
    color: ${getColor("gray", "000")};
  }
`;

const removingCss = nonSelectedAndRemovedCss;

const selectedCss = css`
  background: ${getColorByAlias("accentTertiary")};
  border: 0.5px solid ${getColorByAlias("accentTertiary")};
  color: ${getColor("gray", "000")};
`;

const xsmallCss = css`
  ${getTypography("body", "xs", 600)}
  padding: 7px 8px;
`;

const smallCss = css`
  ${getTypography("body", "s", 600)}
  padding: 7px 12px;
`;

const mediumCss = css`
  ${getTypography("title", "xs", 600)}
  padding: 12px 16px;
`;

const largeCss = css`
  ${getTypography("title", "l", 600)}
  padding: 15px 24px;
`;

const cssBySize = {
  large: largeCss,
  medium: mediumCss,
  small: smallCss,
  xsmall: xsmallCss
};

const nonRemovingCsses = {
  selected: selectedCss,
  nonSelected: nonSelectedCss
};

const Container = styled(UnstyledButton)<StyledContainerProps>`
  border-radius: 6px;
  cursor: pointer;
  width: fit-content;
  display: flex;
  align-items: center;

  ${props => cssBySize[props.size]};
  ${props =>
    props.isRemovingItems
      ? removingCss
      : nonRemovingCsses[props.isSelected ? "selected" : "nonSelected"]}
`;

const ToggleButton = ({
  isRemovingItems,
  label,
  renderRightIcon,
  size,
  ...rest
}: Props) => (
  <Container {...{size, isRemovingItems}} {...rest} type="button">
    <span>{label}</span>
    {renderRightElement(size, renderRightIcon)}
  </Container>
);

export default ToggleButton;

export type {Size as ToggleButtonSize};
