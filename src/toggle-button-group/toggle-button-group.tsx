import styled from "styled-components";

import {UnstyledUl} from "@/unstyled";

import CheckmarkIcon from "./checkmark-icon";
import RemoveIcon from "./remove-icon";
import ToggleButton from "./toggle-button";
import type {ToggleButtonSize} from "./toggle-button";
import type {ToggleButtonIconSize} from "./types";

type Props<TItem> = {
  className?: string;
  getItemLabel: (item: TItem) => string;
  isRemovingItems?: boolean;
  items: TItem[];
  onItemRemove?: (value: TItem) => unknown;
  onItemSelect: (value: TItem | undefined) => unknown;
  selectedItem?: TItem;
  size: ToggleButtonSize;
};

type RenderToggleButtonParams<TItem> = Pick<
  Props<TItem>,
  | "getItemLabel"
  | "isRemovingItems"
  | "onItemRemove"
  | "onItemSelect"
  | "selectedItem"
  | "size"
> & {
  item: TItem;
};

type RenderRightButtonParams<TItem> = {
  iconSize: ToggleButtonIconSize;
  isRemovingItems?: boolean;
  item: TItem;
  selectedItem: TItem | null;
};

const liMarginBottom = 8;

const StyledLi = styled.li`
  margin-bottom: ${liMarginBottom}px;

  :not(:last-child) {
    margin-right: 8px;
  }
`;

const StyledUl = styled(UnstyledUl)`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: -${liMarginBottom}px;
`;

const renderRightIcon = <TItem,>({
  iconSize,
  isRemovingItems,
  item,
  selectedItem
}: RenderRightButtonParams<TItem>) => {
  if (isRemovingItems) {
    return <RemoveIcon size={iconSize} />;
  }

  return item === selectedItem ? <CheckmarkIcon size={iconSize} /> : undefined;
};

const renderToggleButton = <TItem,>({
  getItemLabel,
  isRemovingItems,
  item,
  onItemRemove,
  onItemSelect,
  selectedItem,
  ...rest
}: RenderToggleButtonParams<TItem>) => (
  <ToggleButton
    isRemovingItems={isRemovingItems}
    isSelected={item === selectedItem}
    label={getItemLabel(item)}
    onClick={() =>
      isRemovingItems
        ? onItemRemove?.(item)
        : onItemSelect(item === selectedItem ? undefined : item)
    }
    renderRightIcon={iconSize =>
      renderRightIcon({isRemovingItems, iconSize, item, selectedItem})
    }
    {...rest}
  />
);

const ToggleButtonGroup = <TItem,>({
  className,
  getItemLabel,
  items,
  ...rest
}: Props<TItem>) => (
  <StyledUl {...{className}}>
    {items.map((item, index) => (
      <StyledLi key={index}>
        {renderToggleButton({item, getItemLabel, ...rest})}
      </StyledLi>
    ))}
  </StyledUl>
);

export default ToggleButtonGroup;

export type {Props as ToggleButtonGroupProps};
