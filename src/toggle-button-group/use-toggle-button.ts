import * as withArray from "@unlockre/utils-array/dist";
import {useEffect, useState} from "react";

type GetNextItem<TItem> = (items: TItem[], curItem: TItem) => TItem;

type Params<TItem> = {
  getNextItem?: GetNextItem<TItem>;
  initialIsRemovingItems?: boolean;
  initialItems: TItem[];
  initialSelectedItem?: TItem;
};

const defaultGetNextItem = <TItem>(items: TItem[], curItem: TItem) =>
  items.indexOf(curItem) === items.length - 1
    ? items[0]
    : items[items.indexOf(curItem) + 1];

const useToggleButtonGroup = <TItem>({
  getNextItem = defaultGetNextItem,
  initialIsRemovingItems = false,
  initialItems,
  initialSelectedItem
}: Params<TItem>) => {
  const [items, setItems] = useState(initialItems);
  const [selectedItem, setSelectedItem] = useState(initialSelectedItem);
  const [isRemovingItems, setIsRemovingItems] = useState(
    initialIsRemovingItems
  );

  useEffect(() => {
    if (initialItems === items) {
      return;
    }

    setItems(initialItems);

    if (selectedItem && !initialItems.includes(selectedItem)) {
      setSelectedItem(undefined);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialItems]);

  useEffect(() => {
    const currentItems = items === initialItems ? items : initialItems;

    if (initialSelectedItem && !currentItems.includes(initialSelectedItem)) {
      throw Error("Selected item is not present in items");
    }

    if (initialSelectedItem !== selectedItem) {
      setSelectedItem(initialSelectedItem);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialSelectedItem]);

  useEffect(() => {
    if (initialIsRemovingItems !== isRemovingItems) {
      setIsRemovingItems(initialIsRemovingItems);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialIsRemovingItems]);

  const handleItemRemove = (item: TItem) => {
    const itemIndex = items.indexOf(item);

    const updatedItems = withArray.remove(items, itemIndex, 1);

    setItems(updatedItems);

    if (item === selectedItem) {
      setSelectedItem(
        updatedItems.length ? getNextItem(items, item) : undefined
      );
    }
  };

  const toggleButtonGroupProps = {
    isRemovingItems,
    items,
    onItemSelect: setSelectedItem,
    onItemRemove: handleItemRemove,
    selectedItem
  };

  return {
    toggleButtonGroupProps,
    setIsRemovingItems
  };
};

export default useToggleButtonGroup;
