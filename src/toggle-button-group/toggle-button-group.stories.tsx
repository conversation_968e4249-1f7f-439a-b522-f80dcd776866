import {useArgs} from "@storybook/preview-api";
import type {Meta, StoryFn} from "@storybook/react";
import {useEffect} from "react";

import ToggleButtonGroup from "./toggle-button-group";
import type {ToggleButtonGroupProps} from "./toggle-button-group";
import useToggleButtonGroup from "./use-toggle-button";

type ToggleButtonItem = {
  label: string;
};

type Args = Pick<
  ToggleButtonGroupProps<ToggleButtonItem>,
  "isRemovingItems" | "items" | "size"
>;

const getToggleButtonLabel = (item: ToggleButtonItem) => item.label;

const Default: StoryFn<Args> = ({isRemovingItems, items, ...rest}) => {
  const [, updateArgs] = useArgs();

  const {toggleButtonGroupProps} = useToggleButtonGroup<ToggleButtonItem>({
    initialItems: items,
    initialIsRemovingItems: Boolean(isRemovingItems)
  });

  useEffect(() => {
    updateArgs({items: toggleButtonGroupProps.items});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toggleButtonGroupProps.items]);

  return (
    <ToggleButtonGroup
      {...rest}
      {...toggleButtonGroupProps}
      getItemLabel={getToggleButtonLabel}
    />
  );
};

Default.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/file/uBujwmPDwySYpwVwSJWCDf/Keyway---Main-library?node-id=679%3A1665"
  }
};

const meta: Meta<Args> = {
  title: "toggle-button-group",
  argTypes: {
    items: {
      control: "object"
    },
    size: {
      control: "radio",
      options: ["large", "medium", "small", "xsmall"]
    },
    isRemovingItems: {
      control: "boolean"
    }
  },
  args: {
    items: [{label: "one"}, {label: "two"}, {label: "three"}],
    size: "medium"
  }
};

export {meta as default, Default};
