import styled from "styled-components";

import {getColorByAlias} from "@/theme-provider/theme";

import type {ToggleButtonIconSize} from "./types";

type Props = {
  size: ToggleButtonIconSize;
};

// TODO: Remove this once we have fixed the svg
const StyledSVG = styled.svg`
  margin-top: ${props => (props.height === 24 ? 4 : 1)}px;
`;

const StyledPath = styled.path`
  fill: ${getColorByAlias("feedbackError")};
`;

const RemoveIcon = ({size}: Props) => (
  <StyledSVG
    fill="none"
    height={size}
    viewBox="0 0 14 14"
    width={size}
    xmlns="http://www.w3.org/2000/svg"
  >
    <StyledPath
      clipRule="evenodd"
      d="M6.99961 13.4016C10.5342 13.4016 13.3996 10.5362 13.3996 7.00156C13.3996 3.46694 10.5342 0.601562 6.99961 0.601562C3.46499 0.601562 0.599609 3.46694 0.599609 7.00156C0.599609 10.5362 3.46499 13.4016 6.99961 13.4016ZM5.46971 4.11333C5.13646 3.78008 4.59616 3.78008 4.26291 4.11333C3.92966 4.44657 3.92966 4.98687 4.26291 5.32012L5.76154 6.81875L4.26291 8.31737C3.92966 8.65062 3.92966 9.19092 4.26291 9.52417C4.59616 9.85742 5.13646 9.85742 5.46971 9.52417L6.96833 8.02554L8.46056 9.51777C8.79381 9.85102 9.33411 9.85102 9.66736 9.51777C10.0006 9.18452 10.0006 8.64422 9.66736 8.31098L8.17513 6.81875L9.66736 5.32652C10.0006 4.99327 10.0006 4.45297 9.66736 4.11972C9.33411 3.78648 8.79381 3.78648 8.46056 4.11973L6.96833 5.61195L5.46971 4.11333Z"
      fillRule="evenodd"
    />
  </StyledSVG>
);

export default RemoveIcon;
