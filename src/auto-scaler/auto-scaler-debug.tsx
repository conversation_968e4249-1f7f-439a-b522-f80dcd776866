import styled from "styled-components";

type Props = {
  contentSize?: Size;
  parentSize?: Size;
  scale?: number;
};

type Size = {
  height: number;
  width: number;
};

const FloatingCard = styled.div`
  position: absolute;
  left: 12px;
  top: 12px;
  z-index: 99999;
  color: red;
  padding: 8px;
  border: 1px solid red;
  background-color: white;
`;

const formatSize = (size?: Size) =>
  size ? `${size.height}px x ${size.width}px` : "N/A";

const formatScaledSize = (size?: Size, scale?: number) =>
  size && scale
    ? formatSize({height: size.height * scale, width: size.width * scale})
    : "N/A";

// eslint-disable-next-line complexity
const AutoScalerDebug = ({contentSize, parentSize, scale}: Props) => (
  <div
    style={{
      position: "relative"
    }}
  >
    <FloatingCard>
      <div> Scale: {scale}</div>
      <div>Parent Size: {formatSize(parentSize)}</div>
      <div>Content size: {formatSize(contentSize)}</div>
      <div>Scaled size: {formatScaledSize(contentSize, scale)}</div>
    </FloatingCard>
  </div>
);

export {AutoScalerDebug};
