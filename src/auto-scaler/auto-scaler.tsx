import {useEffect} from "react";
import type {ReactNode} from "react";
import useResizeObserver from "use-resize-observer";

import {AutoScalerDebug} from "./auto-scaler-debug";

type Props = {
  children: ReactNode;
  maxHeight?: number;
  maxWidth?: number;
  onScale?: () => unknown;
  showDebugInfo?: boolean;
};

type MaxSize = {
  maxHeight?: number;
  maxWidth?: number;
};

const getScale = (height: number, width: number, maxSize: MaxSize) => {
  const {maxHeight, maxWidth} = maxSize;

  if (typeof maxHeight === "number" && typeof maxWidth === "number") {
    return Math.min(maxHeight / height, maxWidth / width, 1);
  }

  if (typeof maxHeight === "number") {
    return Math.min(maxHeight / height, 1);
  }

  if (typeof maxWidth === "number") {
    return Math.min(maxWidth / width, 1);
  }

  return 1;
};

const getContentSize = (height?: number, width?: number) =>
  height && width ? {height, width} : undefined;

const getTransformedSize = (height: number, width: number, scale: number) => ({
  height: height * scale,
  width: width * scale
});

// eslint-disable-next-line complexity
const AutoScaler = ({
  children,
  maxHeight,
  maxWidth,
  onScale,
  showDebugInfo
}: Props) => {
  const {height, ref, width} = useResizeObserver<HTMLDivElement>({
    box: "border-box"
  });

  const scale =
    height && width
      ? getScale(height, width, {maxHeight, maxWidth})
      : undefined;

  const scaleStyle =
    scale === undefined
      ? undefined
      : {
          transform: `scale(${scale})`,
          transformOrigin: "0 0 0"
        };

  const parentSize =
    height && width && scale
      ? getTransformedSize(height, width, scale)
      : undefined;

  useEffect(() => {
    if (scale) {
      onScale?.();
    }
  }, [scale, onScale]);

  return (
    <div
      style={{
        ...parentSize,
        overflow: "hidden",
        ...(showDebugInfo ? {border: "1px solid red"} : {})
      }}
    >
      {showDebugInfo && (
        <AutoScalerDebug
          contentSize={getContentSize(height, width)}
          parentSize={parentSize}
          scale={scale}
        />
      )}
      <div
        ref={ref}
        style={{
          width: "max-content",
          ...scaleStyle,
          ...(showDebugInfo
            ? {
                outline: "1px solid blue",
                outlineOffset: "-1px"
              }
            : {})
        }}
      >
        {children}
      </div>
    </div>
  );
};

export {AutoScaler};
