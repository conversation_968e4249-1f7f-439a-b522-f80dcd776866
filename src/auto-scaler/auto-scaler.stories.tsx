import type {Meta, StoryFn} from "@storybook/react";
import type {ComponentProps} from "react";
import styled from "styled-components";

import {AutoScaler} from "./auto-scaler";

type AutoScalerProps = ComponentProps<typeof AutoScaler>;

type StoryArgs = Omit<AutoScalerProps, "children" | "onScale">;

const contentSize = 500;

const Container = styled.div`
  border: 1px solid black;
  width: 300px;
  height: 300px;
  resize: both;
  overflow: auto;
`;

const Content = styled.div`
  width: ${contentSize}px;
  height: ${contentSize}px;
  background: gray;
`;

const Default: StoryFn<StoryArgs> = args => (
  <Container>
    <AutoScaler {...args}>
      <Content>
        <h2>
          Real size: {contentSize}px x {contentSize}px
        </h2>
      </Content>
    </AutoScaler>
  </Container>
);

const meta: Meta<StoryArgs> = {
  title: "auto-scaler",
  argTypes: {
    maxHeight: {
      control: "number",
      description: "The maximum height of the scaled content"
    },
    maxWidth: {
      control: "number",
      description: "The maximum width of the scaled content"
    }
  },
  args: {
    maxHeight: 200,
    maxWidth: 100
  }
};

export {meta as default, Default};
