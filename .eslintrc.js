module.exports = {
  root: true,
  parserOptions: {
    project: "./tsconfig.json"
  },
  extends: [
    "@unlockre/eslint-config/typescript",
    "@unlockre/eslint-config/react"
  ],
  globals: {
    GeoJSON: "readonly",
    google: "readonly"
  },
  // TODO: Move this to @unlockre/eslint-config package
  rules: {
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        js: "never",
        jsx: "never",
        json: "always",
        ts: "never",
        tsx: "never"
      }
    ]
  }
};
